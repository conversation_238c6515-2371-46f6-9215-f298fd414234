{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"], "cache": false}, "lint": {}, "type-check": {}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "clean": {"cache": false}, "test": {"outputs": ["coverage/**"], "dependsOn": ["^build"], "cache": false}}}