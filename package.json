{"private": true, "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "type-check": "turbo type-check", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "turbo run test"}, "devDependencies": {"@types/cls-hooked": "^4.3.9", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-custom-rules": "workspace:*", "eslint-plugin-perfectionist": "^3.9.1", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "3.3.3", "prettier-plugin-tailwindcss": "0.6.8", "turbo": "^2.5.2", "typescript-eslint": "^8.31.1"}, "packageManager": "pnpm@9.6.0", "engines": {"node": ">=20"}, "name": "core-platform"}