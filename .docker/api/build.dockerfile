ARG NODE_VERSION=22.6.0
ARG PROJECT=api
ARG NPM_TOKEN

FROM node:${NODE_VERSION}-alpine AS base
RUN npm install pnpm turbo --global
RUN pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
ARG PROJECT
WORKDIR /app
COPY . .
RUN turbo prune --scope=${PROJECT} --docker

FROM base AS builder
ARG PROJECT
ARG NPM_TOKEN
WORKDIR /app
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner /app/out/json/ . 
COPY --from=pruner /app/out/full/apps/api/.scripts /app/apps/api/.scripts
COPY --from=pruner /app/.prettierrc ./.prettierrc
RUN echo "//registry.npmjs.org/:_authToken=${NPM_TOKEN}" > ~/.npmrc
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile
RUN rm -f ~/.npmrc
COPY --from=pruner /app/out/full/ .
RUN turbo build --filter=${PROJECT}
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

FROM base AS runner
ARG PROJECT
RUN addgroup --system --gid 1001 causer
RUN adduser --system --uid 1001 causer
USER causer
WORKDIR /app
COPY --from=builder --chown=causer:causer /app .
WORKDIR /app/apps/${PROJECT}
ENV NODE_ENV=production
EXPOSE 4000
ENTRYPOINT ["node", "dist/main"]