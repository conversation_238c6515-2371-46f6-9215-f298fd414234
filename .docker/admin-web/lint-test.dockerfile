ARG NODE_VERSION=22.6.0
ARG PROJECT=admin-web
ARG NEXT_PUBLIC_API_URL=http://localhost:4000
ARG NEXT_PUBLIC_ENV=development
ARG NEXT_PUBLIC_UPLOADCARE_API_KEY=test

FROM node:${NODE_VERSION}-alpine AS base
RUN npm install pnpm turbo --global
RUN pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
ARG PROJECT
WORKDIR /app
COPY . .
RUN turbo prune --scope=${PROJECT} --docker

FROM base AS builder
ARG PROJECT
ARG NEXT_PUBLIC_ENV
ENV NEXT_PUBLIC_ENV=${NEXT_PUBLIC_ENV}
ARG NEXT_PUBLIC_API_URL
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ARG NEXT_PUBLIC_UPLOADCARE_API_KEY
ENV NEXT_PUBLIC_UPLOADCARE_API_KEY=${NEXT_PUBLIC_UPLOADCARE_API_KEY}
WORKDIR /app
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner /app/out/json/ .
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile
RUN rm -f ~/.npmrc
COPY --from=pruner /app/out/full/ .
COPY --from=pruner /app/.prettierrc .
RUN pnpm build
RUN pnpm lint
RUN pnpm test
