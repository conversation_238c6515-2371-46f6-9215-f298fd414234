ARG NODE_VERSION=22.6.0
ARG PROJECT=web
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_UPLOADCARE_API_KEY
ARG NEXT_PUBLIC_ENV
ARG NEXT_PUBLIC_CALCOM_URL
ARG NEXT_PUBLIC_SEGMENT_WRITE_KEY

FROM node:${NODE_VERSION}-alpine AS base
RUN npm install pnpm turbo --global
RUN pnpm config set store-dir ~/.pnpm-store

FROM base AS pruner
ARG PROJECT
WORKDIR /app
COPY . .
RUN turbo prune --scope=${PROJECT} --docker

FROM base AS builder
ARG PROJECT
ARG NEXT_PUBLIC_ENV
ARG NEXT_PUBLIC_API_URL
ARG NEXT_PUBLIC_UPLOADCARE_API_KEY
ARG NEXT_PUBLIC_CALCOM_URL
ARG NEXT_PUBLIC_SEGMENT_WRITE_KEY
ENV NEXT_PUBLIC_ENV=${NEXT_PUBLIC_ENV}
ENV NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
ENV NEXT_PUBLIC_UPLOADCARE_API_KEY=${NEXT_PUBLIC_UPLOADCARE_API_KEY}
ENV NEXT_PUBLIC_CALCOM_URL=${NEXT_PUBLIC_CALCOM_URL}
ENV NEXT_PUBLIC_SEGMENT_WRITE_KEY=${NEXT_PUBLIC_SEGMENT_WRITE_KEY}
WORKDIR /app
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
COPY --from=pruner /app/out/json/ .
COPY --from=pruner /app/.prettierrc ./.prettierrc
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm install --frozen-lockfile
RUN rm -f ~/.npmrc
COPY --from=pruner /app/out/full/ .
RUN turbo build --filter=${PROJECT}
RUN --mount=type=cache,id=pnpm,target=~/.pnpm-store pnpm prune --prod --no-optional
RUN rm -rf ./**/*/src

FROM base AS runner
ARG PROJECT
WORKDIR /app
RUN addgroup --system --gid 1001 causer
RUN adduser --system --uid 1001 causer
RUN mkdir .next
RUN chown causer:causer .next
COPY --from=builder --chown=causer:causer /app/apps/${PROJECT}/.next/standalone ./
COPY --from=builder --chown=causer:causer /app/apps/${PROJECT}/.next/static  ./apps/${PROJECT}/.next/static
COPY --from=builder --chown=causer:causer /app/apps/${PROJECT}/public ./apps/${PROJECT}/public
ENV NODE_ENV=production
WORKDIR /app/apps/${PROJECT}
USER causer
EXPOSE 3000
ENTRYPOINT ["node", "server.js"]
