apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: core-admin-web-ingress
  namespace: core
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - admin-dev.a.team
      secretName: admin-dev.a.team
  rules:
    - host: admin-dev.a.team
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: core-admin-web
                port:
                  number: 80
