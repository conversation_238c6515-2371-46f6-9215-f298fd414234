---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-admin-web
  namespace: core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: core-admin-web
  template:
    metadata:
      labels:
        app: core-admin-web
    spec:
      containers:
        - name: core-admin-web
          image: 257861300614.dkr.ecr.us-east-2.amazonaws.com/ateams/core-platform-admin-web:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
