apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: core-web-ingress
  namespace: core
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - platform-v2.a.team
      secretName: platform-v2.a.team
  rules:
    - host: platform-v2.a.team
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: core-web
                port:
                  number: 80
