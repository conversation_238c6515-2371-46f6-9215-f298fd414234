---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-web
  namespace: core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: core-web
  template:
    metadata:
      labels:
        app: core-web
    spec:
      containers:
        - name: core-web
          image: 257861300614.dkr.ecr.us-east-2.amazonaws.com/ateams/core-platform-web:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 3000
