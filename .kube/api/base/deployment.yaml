---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: core-api
  namespace: core
spec:
  replicas: 1
  selector:
    matchLabels:
      app: core-api
  template:
    metadata:
      labels:
        app: core-api
    spec:
      containers:
        - name: core-api
          image: 257861300614.dkr.ecr.us-east-2.amazonaws.com/ateams/core-platform-api:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 4000
          envFrom:
            - configMapRef:
                name: core-api
            - secretRef:
                name: core-api
          env:
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: 'http://groundcover-sensor.groundcover.svc.cluster.local:4318'
            - name: OTEL_EXPORTER_OTLP_PROTOCOL
              value: 'http/protobuf'
            - name: OTEL_LOGS_EXPORTER
              value: 'none'
