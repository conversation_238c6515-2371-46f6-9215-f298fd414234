apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: core-api-ingress
  namespace: core
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rewrite-target: /$1
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - capi.a.team
      secretName: capi.a.team
  rules:
    - host: capi.a.team
      http:
        paths:
          - path: /?(.*)
            pathType: Prefix
            backend:
              service:
                name: core-api
                port:
                  number: 80
