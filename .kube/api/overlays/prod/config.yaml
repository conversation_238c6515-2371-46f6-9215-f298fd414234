apiVersion: v1
kind: ConfigMap
metadata:
    name: core-api
    namespace: core
data:
    ENV: ENC[AES256_GCM,data:dScm5DBgeHAWhA==,iv:1zuR3N3DKL/7hrvQw6fs9f7EoIGg83pJ0Ptm9NGzVPw=,tag:EETwQlv6ZqrQ2zVSoctL0Q==,type:str]
    WEB_BASE_URL: ENC[AES256_GCM,data:+HxEjWccuwOmeKQbgcCfw3sPueQUg32nnDA=,iv:2GgSLNwZ6BNg1QhvPWvEH6rH2LDS/CzNbaV1jQheYHI=,tag:1UTjVCD9lboATCvKMHPI6Q==,type:str]
    ADMIN_WEB_BASE_URL: ENC[AES256_GCM,data:3oEBDirvh3wPBX1cN1wGmsVXtvA=,iv:eNYqLBsyUAd9LCbJcrr6la7IA2vHM5j91qM4KplleEg=,tag:gaEZNJh2pWIMtqcp7elnfg==,type:str]
    API_BASE_URL: ENC[AES256_GCM,data:XwjM4+s2TrP3t219GWyfrwO0gg==,iv:z4oTjCxNNFVaLMxkkikmE0pBS9zIwPBmwhjHZ2T47rM=,tag:9OeYOWRWQIYIcatVyqB8wg==,type:str]
    PLATFORM_API_URL: ENC[AES256_GCM,data:NGaWM78fsGcBjdMQhAMOtcVEn3e8HC1cupspkBi7,iv:VQUPl3CTVsjsyf+T8J9M5S2f9OtKp7fgdSKiZMR0REI=,tag:u4fKYCipyf7P0uzEqhE8Hg==,type:str]
    PLATFORM_WEB_URL: ENC[AES256_GCM,data:9Hh1b/dW3tz0uci4NBHZMM/uAblXYrw=,iv:LeZ29X+Mx5aMAglzQ2mxwr2mW45J1yp69ouQ0rCPK4c=,tag:UD9XoVtcN4VftFtRpLe4Fg==,type:str]
    HUBSPOT_OPPORTUNITY_PIPELINE_ID: ENC[AES256_GCM,data:nIV/M65HGmQ=,iv:Z0wGiOL8zCq9whTkj/lDokX093TY0zjRhRFgcFBeoCs=,tag:kjj7tVmEBpOV8E4WfpTsjg==,type:str]
    HUBSPOT_MISSION_SENT_STAGE_ID: ENC[AES256_GCM,data:nUNpvsDlgYo=,iv:Fh2vx6pfqCE5GoS/sy//HMhmThfebKbOs6GzEw0Rdsg=,tag:njpL8yeiAfe4udeQFmUIEw==,type:str]
    SENTRY_DSN: ENC[AES256_GCM,data:wR05RpMCA+C9N9FA20AJ+eoHafEIm2YjJjbInNv/nQHRO0NMbJU0DbFjHbnmpkArxdR5RE6wikRVL7gO8yKaq7Apnl0I2RHkXNwWXwr8K6YZoflh9w==,iv:/l8oOqY7AMYHyX9iIIuISMUW4/Ui8rD0Evy5FvDfK54=,tag:mFMVs0BGQpiJ+S1g/TCV9g==,type:str]
    DEFAULT_TEAM_ADVISOR_ID: ENC[AES256_GCM,data:YoxXdtLz6KU5XnTsJSsEH2LRCpWQ/sm1,iv:JWJyIFysbpDhuR2DSN7lYetRqz5Vftn8K9Hs1lic4t0=,tag:/8gaMpf9BjS/ueQS3KKqXA==,type:str]
    CLIENT_APP_SERVER_V1_URL: ENC[AES256_GCM,data:6w8f90v/BmmQJAdj2sD429KENhA+B6vP7ppxnQ==,iv:SF5NVaCk97D4aVbRe8nn3n+P1kfM2plh4JYzhDocHeI=,tag:3885Hneb+clw2ape9ijGfw==,type:str]
    LANGFUSE_BASE_URL: ENC[AES256_GCM,data:BfLwvt44QsrUsbroQVs/VN3qN0PpIX4d/FDaPKrdwobeV7fFgY7icezNvjuztmn1LKE=,iv:82XI2uFLqNYe/x2Ns+TOjFnRxPgnjO3ztP/+rAA2sMY=,tag:uEz8JjSMs31fYHn/Wws1Pg==,type:str]
    GOOGLE_CLIENT_ID: ENC[AES256_GCM,data:8mQ5+l7s9GG9OiWZQgPdy+SuMWpPSf1OhvGTO30rw+rbh2jdA7CZARL0Knc2AgyCcFnBH8yl2gAxzkcwaTIKLdamYGy4Gls=,iv:clc0uUur6W772WNyEfqmnpBnV7dDQFKhaD+ntYOn9dM=,tag:lT68Q33EZouBQeAYnxznjw==,type:str]
    DAILY_API_URL: ENC[AES256_GCM,data:Gw9ZKcVgQfyfZKF1nq2B+wVZuyO9hUSdjEf0Y/4=,iv:LvcKiIRuqorXtZ2ONhk3CAXtdSdkmmEakY7KJDVXV3A=,tag:URhvqumz4tKLuF5zGuQLHQ==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:257861300614:key/333f9f38-bf88-4539-88b8-fdfdcdcefae3
          created_at: "2025-04-28T15:07:16Z"
          enc: AQICAHjzvtztNMX2Kghi6dFr/qzg03fN+7f0zntUN/tgZxpRCAG2/SwQzXzEWB8Gwz6jiTCJAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM4/jguGnS2JPR836yAgEQgDubX6JZGFYy5QHusqlSyxFJd7UIRypA/e5i8uTp2lXpsr2s9cjY/FKqGrlPaBgsvmEvLeHNZB1blqynsw==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-05-14T13:28:57Z"
    mac: ENC[AES256_GCM,data:zoyYUSA/8MoLjzYebUb0rk/XDhO0O3UlwMDFUvfvFAuTmX4w9bhJnzTFqhpMq3Aj2YtmjbIoizvpGlbNaWTBSzOJFd24slhSX+gJLMjVCSgea8owgY/TLT4WcbBxnBBmBVmvMuvvxG4sXygCtKyzkYdYOvfJ9lrmUjNXk5nWxV4=,iv:vbdsS83rfbnMObSwrWDDwD87x8hIAVcwQs8nK48v+xs=,tag:Fxy42+o4cRUJS0d9runUXw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.8.1
---
kind: Secret
type: Opaque
apiVersion: v1
metadata:
    name: core-api
    namespace: core
data:
    MONGODB_URL: ENC[AES256_GCM,data:5q6XpB/jzBAiqfaqIfcWFvL2SrXg20G+ZnjA+siLZldU2SIF1W+RuXOCXwvFCsUxQOSPiolrXsVFmG1zpAgkV5dcS8Yv0fd/FDSo+0ZZCGRyBayz3taIva/EX81LuozyXoa/Gs7HuAl1LisQ7gozS/o8YgEny00bflHKXeDpho9ilGEysETuUY3kzCUgjpI/L6Cf4BmGBLARtVsBNORpe2AhY0Nc/SaTRPnW+tFXavE=,iv:ddW8FmQ3lzdUzAJcIJzfxzUGktWtXONwf4Ux3DkA6sQ=,tag:qDMCfXGQ1AQdZ5mzAZ1wfA==,type:str]
    JWT_SECRET: ENC[AES256_GCM,data:1UrR6Zuj7DuB40LshKUERr5dirGXbjhd7TBnbBgSSquEfig1cRm1aNsxopqRrDlIv0guS1+KffDkdWLYG9pBs3j61UNlXOHKcGRQ2j9+eVvPAaxKY54Mnw==,iv:pxvn0NJOZzaTb4PM88touErIrmBerqaJkw1SfgtW4oE=,tag:adbBBlX0PcLWxkKuxB4tAw==,type:str]
    OPENAI_API_KEY: ENC[AES256_GCM,data:WYg6TeNj5N+fkzlxIsRCu4hV3QkkKYJy32qnL+Mun8ky140S6ZH/HTSPLMFwQGDP6VJhIw3SH8fgW0pLEXTQnkBHy9FgazaDYgjZMw==,iv:jwpkdunBw9GE2x4kE8d8H8gEFs6dOXYmOnqhHkCQ4xQ=,tag:jhn0P6NJzXFtcwkqViGKTA==,type:str]
    ACCOUNT_JWT_SECRET: ENC[AES256_GCM,data:Dul5/dLPwgld5V2pIMebrgzjF6Vxl31acZyvuHiYs4qz8Hbdu9h+RoINZ6qrgud0CaMNSJBbS8/e4Cj/,iv:ReFaQCTuvzZ06IwrMHO9gM/ngFScqIFQuEF5ZWX3S+Y=,tag:wzVR0IuzqjuC3lLR7iuBjw==,type:str]
    HUBSPOT_ACCESS_TOKEN: ENC[AES256_GCM,data:gDkCCDR8np1wO6YzFHKBJVXzGQysN/YsFHQkL19lK/w4MaRyTEbTHdodGojUM8Hl7iCEDV3sZAn37Diw,iv:OEm+46fYRXbUin3BEd6HoO8zchwU/kM6OXXY25oyCqA=,tag:/1adPtg7EowParoaRVor6g==,type:str]
    PLATFORM_ADMIN_JWT: ENC[AES256_GCM,data:5bnp7q/Z/ZxW/UVOjOddLZrBAqpFDo/3lfEVL6K08HQCd/UsRMyrIlq3W/wXqL7sbd++OblJjYcuC+ZcsA9eJCW+OeNY3TqgP9sISNak/TTxSMc9JIEybTFwkZ9paEltag8oJP8kWp6SwiQGWiUAGu1G0sl3OzKrCMAiB2jhgXj97C56W8uFw0YLsnFpXADyEzOf2uke1yfnY3hzXQ3jbF3SKZcb2Zf5f5CTdMwMpo2BEWVS8Pk9u0O1vYCG+3jO3sycjs5tePsbxaLM51PdChSHQRLbma53YMfdp0FApmvI1AnAQQ1UYB77PfWBoh8fpmnPwmTvikpCtCgomxX2N1TpSUWdJay6oI2DA3NG4ylGU0aKLinF7SVoMnS17k7RyMz51S9yn3eOJhNf9f/yBjSMAm5E0inD2sAsmtQGXFSYRmbq4dlaEjboM7IewJf8iXN7TKnwARzi/tOavhtyHeXBO3jMz0XHVdFP7npx0XiHDKV6+eBvdL1gwtEIbYnDOVHKFft26St9LR0ZC3CA0E9qYf2XvpghKco+k1cuAtgY6CnridjlZxMfLFDBBj5pjHdrSSA8SKnVAVuiNIHKn17A652DghMzMjYiG1ljUyUpizCri6+70QxdoIm8Wj1t5KUlLYTwcTuTWhs7FR+cvA9UZb7RSxfDwRq2IaWtr7eURExdmC23bPCOQdf07itGiNmJlRsIejmmlFv/6XytwCCicPNY66jSWcqPAwuuAJMmNA/MzSpr0hxVvEcIpf9zeXhkMt/dGxvFvktYZHULjZowvIzkVO0M5wAh3QPiRhTDNN+kurCZgXod8AFOxnCT6F9Je4jPsbKyIfRAv2O2d4mpG8RnRt5iTJ7K8v3EDRw=,iv:EHWdCt1bknEJ9CjWTHzbS89RA7Lk4P26K2HA7+c4OCU=,tag:svFSx+nw2SWPlHOsUj4hdg==,type:str]
    API_KEY_PLATFORM_API: ENC[AES256_GCM,data:G1gmcYyrVY9QQq3WyujUi4FP4s8QKxgyIbcsZU2BycWDu9Nv,iv:mJw8Yp7es1MwgfP8M2m3pTZkzPwePZJoKx1OSoq1VTA=,tag:ZEvsT+jHMJPRIYetBPsQsA==,type:str]
    SENDGRID_API_KEY: ENC[AES256_GCM,data:leb34bI2jOyqXqOKON5+94WbPLr1P9ROgT0ot86BktYbsnNxixlDGSIvQnuDFXfwW3qItI53J4y8b2dMPYMTLCbhkmH72Q3oXS6gMb9FUQhjPgYXJXrI/Lq29Ao=,iv:2DWif0rrBnivPBF8WI9LBAlBVy11xY9baRDFwfJ/l6Y=,tag:Jz8nIHvoLy3uoZN2KQZJdg==,type:str]
    SLACK_TOKEN: ENC[AES256_GCM,data:M5keh79rUEfeZiw+Y6fOXLmzvZFtRsJS0McVdXpG5goWz5+aARVLrPOQHCNbpu/aDOQtsslE0lb/U+anRZOHBKiSz5MycrB920F9gQ==,iv:ACaDbvdzfak691rwfhYf/AIMDYmaRAi0LugPbX5eHvI=,tag:4G6xLfysIXtaNycMUoFhtQ==,type:str]
    PANDADOC_WEBHOOK_SHARED_KEY: ENC[AES256_GCM,data:08GyqSjYYq/5oLqTfDfeB14oZa6cheX5yVUYVQamvrY=,iv:LT1iFW6XHUDHtqujBdg7b7GWZLPFPzcZRYiqmakHDpE=,tag:kb9Zm+EPj3pcWR4ftWzIPw==,type:str]
    PANDADOC_API_KEY: ENC[AES256_GCM,data:KzoeqKPLWbETX3Pk8AfymHgaYUO0KE8/eatQsJqbuOdod/X1mTgSWv3o6u1GSVQAF3NokubBJRQ=,iv:HH0pmySJGqVIl2kAEOGUuyp9hCPgc7NFUNzdjuPD5WA=,tag:JiQrGQENfToHXPxxg5QiMA==,type:str]
    CLIENT_APP_SERVER_V1_API_KEY: ENC[AES256_GCM,data:XyQ/e6+f+dgsK6FwnYZgD9wdCOz8tGwzgKsEByIykDjNVckPINJsicZnmcQ=,iv:iEddZ+Zp422tHisUkmIs2qB9fsQ8fq2gx0LIQN8etFw=,tag:FVrtl+L1uBRjBqxDP8H6Hw==,type:str]
    UPLOADCARE_PUBLIC_KEY: ENC[AES256_GCM,data:WNRPM6l4K75I3VARH/U5hevO+J7fY6OFWlRLcw==,iv:mlqGCdLGtWTVjcwbzqm+W9LcYn6p7hzyJ+ACUurT3Vs=,tag:Jp4jOPx6yZgqZl6eK5w6rg==,type:str]
    UPLOADCARE_SECRET_KEY: ENC[AES256_GCM,data:xwYamlY1Ea/rx73pnE44U+Rrz4VnTSivuctnrg==,iv:onphgUi+J7/yoRaDpE9QQiWjapgV2oTqV8/ZAHyhybA=,tag:yutaPoADnaYExi8HI6wHwQ==,type:str]
    APOLLO_API_KEY: ENC[AES256_GCM,data:ahabcMK9PgVTWiR/7BL/XePv9WzBrdpIaK2Jvb06KFM=,iv:WMLOkxGC1zt6I7qF+RIpKvxyK8fsCI3pFVo3VVJubFQ=,tag:v84PfzH4rhUD1CYAX09gcA==,type:str]
    LANGFUSE_SECRET_KEY: ENC[AES256_GCM,data:6Yv2qQyUxNe/u8ggNQ6caNSe89GOb/HM833IUPWdN2oK0WthxtKIhOd7V6COnFjPI8xuAoYQ3KA=,iv:GfHXbLyYshY9+1JvYAPjtCsGr3HyVo+okZJ+o7KjBYA=,tag:p2euf6FP01p2SdhVjL2YOA==,type:str]
    LANGFUSE_PUBLIC_KEY: ENC[AES256_GCM,data:dRvl36RlFfxXmIwGrveOr0OAslZjFu1Z4l/c2pdELOwg9xfgJyYttALD79eRqj83oHgJtwBTops=,iv:OCLyXywgkxFqyysM6bqLhCx/W77AArZRwMxnUJmYqfY=,tag:ZC+HSQxiLsvtNRdvOXAkNA==,type:str]
    GOOGLE_CLIENT_SECRET: ENC[AES256_GCM,data:kTk0YWIXC/Pq25iydJcz/S6H9x4V5xL88zCBZt58InvzY9sp+7Ka//6J/k1sDf55,iv:EPP9Q5MenpUOnQmoHV2YIxP8W+q4zIF4P3YmYmpeSqo=,tag:sxlecoq9Gh2lfM5fdIReOw==,type:str]
    DAILY_API_KEY: ENC[AES256_GCM,data:w4bzD4QzBeOfu4aOAvVjTcQXy4sVk9sNacj6P5ZTDAMVqmp2ffVWRw8fj4Ip6LdIQXIkBpvD1fb3TS/3TQrqpgj02YN9xw1e5DQfInW54a/bPdHOu9yN4Q==,iv:ubcCFpOeVDxNAGBxI4E1WU/CQECatb8DSC/WvIPbn0w=,tag:yERVntRF54kMuFiks8B55A==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:257861300614:key/333f9f38-bf88-4539-88b8-fdfdcdcefae3
          created_at: "2025-04-28T15:07:16Z"
          enc: AQICAHjzvtztNMX2Kghi6dFr/qzg03fN+7f0zntUN/tgZxpRCAG2/SwQzXzEWB8Gwz6jiTCJAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQM4/jguGnS2JPR836yAgEQgDubX6JZGFYy5QHusqlSyxFJd7UIRypA/e5i8uTp2lXpsr2s9cjY/FKqGrlPaBgsvmEvLeHNZB1blqynsw==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-05-14T13:28:57Z"
    mac: ENC[AES256_GCM,data:zoyYUSA/8MoLjzYebUb0rk/XDhO0O3UlwMDFUvfvFAuTmX4w9bhJnzTFqhpMq3Aj2YtmjbIoizvpGlbNaWTBSzOJFd24slhSX+gJLMjVCSgea8owgY/TLT4WcbBxnBBmBVmvMuvvxG4sXygCtKyzkYdYOvfJ9lrmUjNXk5nWxV4=,iv:vbdsS83rfbnMObSwrWDDwD87x8hIAVcwQs8nK48v+xs=,tag:Fxy42+o4cRUJS0d9runUXw==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.8.1
