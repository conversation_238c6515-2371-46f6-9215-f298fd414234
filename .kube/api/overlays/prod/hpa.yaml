apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: core-api
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: core-api
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600
  minReplicas: 1
  maxReplicas: 5
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 80
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
