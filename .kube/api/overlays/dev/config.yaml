apiVersion: v1
kind: ConfigMap
metadata:
    name: core-api
    namespace: core
data:
    ENV: ENC[AES256_GCM,data:hIDeJ8a2dFtKzDg=,iv:FeP8OOEZPBZIK73dh0yPOxQu2bGwBZU+YR+z6FP1+PQ=,tag:uCyUogG3HIvumJtRsGw3iw==,type:str]
    WEB_BASE_URL: ENC[AES256_GCM,data:diIZ0TnXr0n3rWeVEhxqBkNkEC29m10sqsE1,iv:QDrc0pBtFYoIIOSgTSVN8f5sN1KcJadCeYWF8FielKQ=,tag:bLvw/awt836wR7BTlR4dFg==,type:str]
    ADMIN_WEB_BASE_URL: ENC[AES256_GCM,data:AmBF6ZPSygLWwRh6GIZVtHO/1c7acSVY,iv:9sYXene312Q2XzJDaHfycdB0VEO2EmUOfxpZaKf47vI=,tag:+gODnluRd0TVCVbgkUDVTw==,type:str]
    API_BASE_URL: ENC[AES256_GCM,data:2Sy72CT9csDFOHy0EJ5JlamVyvLDxy0=,iv:hXN+h3jSC05l5ytUMInwO1/dW2e+mteK5wDmulOB8NY=,tag:3XauyjhtB9FHJRoL7T1Ttg==,type:str]
    PLATFORM_API_URL: ENC[AES256_GCM,data:9scfePAB2gLC3J+pamXSTOnuT4jtMBhTLRSWbIgP,iv:O0i6f7Gosg6EM/KPJyB8OEr55PUqGcR+HV1XXYiuY/E=,tag:Z7OCU5KdZ+6tjonMMIf2Jw==,type:str]
    PLATFORM_WEB_URL: ENC[AES256_GCM,data:ZavENWnbUNVUlfA7NjpQKMeNX4xkRLgpouC77i+Pbg==,iv:foNi78VQkvFyoLFLjhOja5eG2sbxMx73cLLg3c4cZGc=,tag:c2Ar+WYN2z8ANPhKvbdxag==,type:str]
    HUBSPOT_OPPORTUNITY_PIPELINE_ID: ENC[AES256_GCM,data:6WUThTR+ODg=,iv:Wwny8EUKYslqFVwPK5O1dWmSdsTpew6wDH1c+NAItWQ=,tag:LHj7Hscx8bu4NL4fHlt/3A==,type:str]
    HUBSPOT_MISSION_SENT_STAGE_ID: ENC[AES256_GCM,data:/ZViRnsvbmI=,iv:fsD+kIcQ5HgqQ65bs7PlFC0j5E/t7zq08eaQQFA5l5E=,tag:EV1EYkn6aWdAmGqEoTE+MQ==,type:str]
    SLACK_CHANNEL_OVERRIDE: ENC[AES256_GCM,data:Dj5PzqWXaoTgY9ACrOvjDNbYin3Y,iv:Mpcuu4Pi3LghnCh290+3IFJ2UdSi5PLiby1ZgZyQ1yg=,tag:fH275W6S050OFQgpY7k1Xw==,type:str]
    DEFAULT_TEAM_ADVISOR_ID: ENC[AES256_GCM,data:p8r4oesZoNvZe7dfQkRoNdXVNuRLf+qZ,iv:pTWtcYjwDDmFjH/QGEaTt4Se/LxAkSPvNzqJSBYefC0=,tag:CDAKH3tC8PbtgpXp84w4ug==,type:str]
    CLIENT_APP_SERVER_V1_URL: ENC[AES256_GCM,data:502JFiKJjA4DyZZW+BTUrl9OuD22MSlswBbxpk549XCl,iv:AhIFAR0uERvaRPiPrTx7pXXrx34fCIy8Z4B6pRhFe68=,tag:r9Ka/OYPNqsuxjOs9TuE5Q==,type:str]
    LANGFUSE_BASE_URL: ENC[AES256_GCM,data:MWaeRtlq6ZUZ02uz6FMUWCA19vwlORbHjk1ZBLgB5Wd664zIndOwrVmOiUu/kYVJDA==,iv:dLahkP/oJDhIzjo7jVB2dxgDEF+1kiCfnyPxqB9FYGk=,tag:G9pzzxan/cYJ+rda3gv1Iw==,type:str]
    GOOGLE_CLIENT_ID: ENC[AES256_GCM,data:a3Uc008MX4gr6F4ZVPDUuwX016kmTsnApfzKvgIFfxyhlmtMv3D8WoF4BEjqsTU3QQFYlwhQ9bGDnzYV7UiVKZ627y6y1c8=,iv:FCBSZyJzb2SzaIP9NbZFlXy7mefhh000545W345Zrj4=,tag:63rwN3kqv6cndEE7X9lQUg==,type:str]
    DAILY_API_URL: ENC[AES256_GCM,data:3F9NakY8l8zL6zS68CbTeG7DR6GIuzKU7Ds4lkI=,iv:gledXwIbjHWYe5Fpo00eCnaXyq7ryzptJT4gcU3kn4M=,tag:Ql4ymHaLLyQgd2y7AndeQw==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:257861300614:key/d7ac3ef7-9652-42cb-bf10-0d41500d5492
          created_at: "2025-04-28T15:06:53Z"
          enc: AQICAHiC/aYmCRDHW8JE32HFRANJ6n3qI48P4ExeAuNfSl1OLgEmWkjCqHPf5ahAk5kBEqWkAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMcJeOQRVsbmG5tJauAgEQgDt+wfj6nvKQt2hZ3N+nRlSDkoRFBnXm2S0vOcZsJa5u7RRk3qP9vw7RIshY1ceTDk3D7o5uVoiICc0W3A==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-05-14T13:28:08Z"
    mac: ENC[AES256_GCM,data:t/0Pn4RCzFiFSpKv2Y4gFuEHn2j9adG0j3ybU0xzj9I7lqPtOerlTjVyGVNbV67oesfoR6SRexnNeoM3edINH9V92Z2/XU1aFaG+jsPamH6QLrxmj8xtwbrP9qc9xlQjmcMmGSPsLpJgZS9DasNSqlIgafZazY5/RfNipolTA0k=,iv:/wXjZJnLoNz/4J+ALaH/BY7HhNS0xuWNR58PoFi+/oE=,tag:ATHttv3upYB8gQIDPmJbyQ==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.8.1
---
kind: Secret
type: Opaque
apiVersion: v1
metadata:
    name: core-api
    namespace: core
data:
    MONGODB_URL: ENC[AES256_GCM,data:r0/9D+z1xB/JCkYZzhkUzzZiWnV3fsBU/rJPJRrGcjNT4k/vPbB8or7pg2nUUTV5oKISwlDgvXJY2Iglse1jIvOFmncLxf1R3EWM3SB6vnxlDEoBbLLsRrkiC/k5SIdRN5E/yT0SdiAlCifcigowq8cTdWUHG92nIVfALww7wb4rqZgl62oier+mhpzgdsE8LZHr7u5wAYasDAwDx6SP8TVi7uMTaeXygIGbCwOibEoQ5YEBv6Li3oeURwtK03w4uj8rZQ==,iv:/eP2aSXkX7tdlCVX9WeLcVI9XUH/GfahDG/tQg5/CTA=,tag:5M9cx2R4zWnna6wxH0ASEQ==,type:str]
    JWT_SECRET: ENC[AES256_GCM,data:6WpJI0R+qrB6x/6c,iv:UYvDPIpxrycgK6u0x5SLKNcBDfSsArQEY0U+xS8BrUY=,tag:moGloxCC0Ni5ifmpj8g6CQ==,type:str]
    OPENAI_API_KEY: ENC[AES256_GCM,data:qXe+AaUprpyIfX2NCMjA/oB96UKOAQJbmJnEtHCKEUi9FUWsL/qI+k7sFLdRctALwm5BUHgNj8GAJjeNhuJ9Js7dcF4AAi7EVhi+zg==,iv:IR9LFTXsDRaNPVu+hVCMO2fXtvLPWo7pW1khopJ73Ew=,tag:NnxcHq5HHxukcMdsBeaYVw==,type:str]
    ACCOUNT_JWT_SECRET: ENC[AES256_GCM,data:LXTNqTxj9m/s8qjP,iv:GlinT9gpq/3U7QGD4NkdgLLCmKgiiDOQU6FYvVpTZeg=,tag:VTGQUDoYUJIxy122FC6CeA==,type:str]
    HUBSPOT_ACCESS_TOKEN: ENC[AES256_GCM,data:28YVxjeQTh667OvQOOEElBVwwcsXwI93z+bASwqRKmlGhovAajzCRJ+kOp/mEC/OQC4mNnKeoJw73+Sn,iv:Q3yXjc9JLsSpcDutk2LLeQlG4/izaQMGEFZ0sqoBgfU=,tag:hQ9ugiDIAECSu3uVMCcOiQ==,type:str]
    PLATFORM_ADMIN_JWT: ENC[AES256_GCM,data:2nr8eGDcypoOKzs4dQt5o9FvnDBsxMGjagAK1bNBQ4f0NZCjjo+RjmjWPEt9oge2H6pF124aV8mSmYZThbWD+0+OhBemcw5gJmt/uGYhXWmaQztsFO9Iw9zgO13EEzLzxtxW9x4lTiRqS/RDoBGKdODp0hVxsqqhRgvGpBkdm+vwkgqM1YuCR262sgSWjFz7e9ECz7ggx4bgt/OUCNodgaUsyAl2m8N8nS89TjAf5zxxDC25efR0Lny5bP4bGN/knn6kFjQiBsRkXubhwFPH1TOFlByAIPVPJND5SieKk8TkGbEoWOYbGqZT0FmU8mn+EdBCjqtyyiVA+6x7sOyfWk79Okfuq71oV4GxBktMzqUrDlIVWzfF+r+c5JKLOi3jj0rf+lzo1iDh9ZhgW6xokc20TLN8XJiA8AStQgz21iEG0sENq1Jf+NoCJPN1O5zSN1E3xWvkRIZFAkvnQgonxXf7yNr9awP8qX8aG+BBcFpSkk4FeA5ALO3KAWODc1pUX1NRZ81uSDcnX/DV4cpQH8DCGF2pcOWdFJmiMgu3acD1KYt1Fxlw+fDQ06SekzoTIutGEImu5QPaWmkSqPdwBLZTpxVe1PUTn4Yw+de7NFMk3JZnmPTapYM2pUTy9CRQBeYtz5lzG2akb1dNiTfAa1YuID9ypEIJ1W/qCuv66YVrq9+z5DiDvVltkx8dEkKseTlmqAOajElrH9gUqupLQG4BACjb8KqSLCE3q+3IRqAlddzikj0whGvIRIqBhHtNPK/cQh3fXW+b7q1ScDRdy/ewZzB7mEa3CjhkjCZLUpvSXonllV4P/IzAbDAGBEsgZjU/uV9g7h2HC81vdo8oCXK89TNcv0qCpzD42kjD/4w=,iv:ilZmpsnLVJfdH/rgocqpjRy35fsYYQKiHOdo1swhGV4=,tag:JM1KTWVcGN01ZoaNexxf1g==,type:str]
    API_KEY_PLATFORM_API: ENC[AES256_GCM,data:OFrqcehaFNu3pcFj,iv:cuV45AGtSgeGiYYNDUB+C3IJI845yNpo3x9vtAhC5l0=,tag:Jv0+pMdiv/4C98kIPixuvA==,type:str]
    SENDGRID_API_KEY: ENC[AES256_GCM,data:/PFo5mO45RNInfwcTwVVB1sOTzw0SSDm2OudJIiZnvjzA0Ex/xkazak+SuNC8hqdC17NwWwIrMayFp5tP8dgh4XhyvKHpWs+rzYNUFP3YLE9oZkx2Nj2eYc/+8Y=,iv:jevsNEPfytuLteI6gkf+kXeCN5QVX6Kgal935FIRx3E=,tag:gWY58P0HXPzLLQj3NX0b1A==,type:str]
    SLACK_TOKEN: ENC[AES256_GCM,data:k9UtK5ZyPsvDtTsctAYfUHRaW7in528SNuafn0psQSTaZmzrR0X7tu1oYNi2TAXScHilRultAugJ3Va3ox09BGulbdZMFoJfrZjbIg==,iv:Uph09MShaLO9b/A12++zoU2XT8dTxtctkwHsUwnUXdY=,tag:yCoU12lfpgmEBS+dV0RBRA==,type:str]
    PANDADOC_WEBHOOK_SHARED_KEY: ENC[AES256_GCM,data:a3k6qTFZVt4jxXsKrty3ga5qcgRCpokGCZKEKVcSI+Y=,iv:Yn9m05jfsCeaMAQ6i5ENdUiH7i0ZSiMr5AiGlIaUhmo=,tag:u6EGdhvcntcS8VKGVKdbvA==,type:str]
    PANDADOC_API_KEY: ENC[AES256_GCM,data:54Fwn5EUncmPzIY+gpwbdZ72gVQx9ioRyO8GIfiR3zlxtYtYmx3gYlsX/+MBCydxkJPv3xxDZmw=,iv:GNf/ruXG5KbNf+5dHYTIoJwSLd8vgKfF19dtJinQ5mA=,tag:GsjTuVty9NQMi8MiFSlVmA==,type:str]
    CLIENT_APP_SERVER_V1_API_KEY: ENC[AES256_GCM,data:lHPBn2o1i/3Zb8MYvRNMJULoD1vcFjZgK0RbCO9XlC0KaqYDCIFqj0eIZZ8=,iv:LsqVIFLfkA5ZFt13DzNavp6e+mb2Ap8TtZTJ6qzwL5k=,tag:Ixd1hY8OVo9J1qymDkCa/Q==,type:str]
    UPLOADCARE_PUBLIC_KEY: ENC[AES256_GCM,data:q+Z0qgTUpSqr4SbLoTfcim/rLSJAW/IZS6W3UA==,iv:tYzvegnmPqLUtgxrsP2OBCOLA2hrc7BDwBZNPNy5f4s=,tag:2o370kMwijiwqjEwKUat8A==,type:str]
    UPLOADCARE_SECRET_KEY: ENC[AES256_GCM,data:o1eZd+whjI70fvnm6GBjJYo1Aqfv2gY0DYxHZA==,iv:+kO9H1bcGrbis5XXOMT3eCOajPdDeTDdLjxqY6ATsLE=,tag:6IA2/fc3XGd3/iLjAfUKzQ==,type:str]
    APOLLO_API_KEY: ENC[AES256_GCM,data:ZhX7Fzg48/pE81qtidk6O3ZcXOjiiPMeFxEEbX8gpjU=,iv:wG73LW/79q2s4ofZjUYB+lWh4tL4XRXC1rVVKCVAAaY=,tag:CA9oiDiLxCn8yDZBt9II9Q==,type:str]
    LANGFUSE_SECRET_KEY: ENC[AES256_GCM,data:y8mqVKklORFMBwXKvw3ZxWbU+kc4Q4SrdYR4NrQ/w6Nd1hAKlzooValbuU2P9rPg+TeGuMrN13Q=,iv:gktZq2bKtSjOv1p9z5EUdnD7Kyfwg/zB41Oi8VL1mWA=,tag:G3qWCIj4dm/pb37GtYYhCA==,type:str]
    LANGFUSE_PUBLIC_KEY: ENC[AES256_GCM,data:PTwr0R53olzdnzvB4HuLsRBORtqVhQ3j9b4spKSI6aaV1+5v6GnjxmoOST6TI6yfIjUhaVJzaM0=,iv:WEAkuljVxBPWrN8en/TM8v6v0Z6KuE7juiyg28ZvuDE=,tag:By7SqC01VQmnGu5c++DxMA==,type:str]
    GOOGLE_CLIENT_SECRET: ENC[AES256_GCM,data:ANVm3rPem5D0AmgORpYfxClAYIvf2tivY/FpIxffgBFWn9NgEerd9m61GZrsl+SL,iv:2orBr3eBG0S/AEzWXaCPpVnnt8R5KRxLbrsViTOADfM=,tag:Tax/Rl3x+DhqXeMrKUK/cw==,type:str]
    DAILY_API_KEY: ENC[AES256_GCM,data:iFDoPxt3up8ho73mBWoEB9Xj+IZPFPSvfxjn+ZWkh6MLUnV9BkMw9Au82jZWAaWs6SKu7MdpUMrVAzLrZzGl+H1J3W/LOELOl1EB+KdJ8OkJ78seintUGw==,iv:+wlEY2+2FNKR2um0HE7aEl1nnwMy5X9C5DbJUf6Mgjs=,tag:WdIVqovacrNX1g4+9njjIQ==,type:str]
sops:
    kms:
        - arn: arn:aws:kms:us-east-2:257861300614:key/d7ac3ef7-9652-42cb-bf10-0d41500d5492
          created_at: "2025-04-28T15:06:53Z"
          enc: AQICAHiC/aYmCRDHW8JE32HFRANJ6n3qI48P4ExeAuNfSl1OLgEmWkjCqHPf5ahAk5kBEqWkAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMcJeOQRVsbmG5tJauAgEQgDt+wfj6nvKQt2hZ3N+nRlSDkoRFBnXm2S0vOcZsJa5u7RRk3qP9vw7RIshY1ceTDk3D7o5uVoiICc0W3A==
          aws_profile: ""
    gcp_kms: []
    azure_kv: []
    hc_vault: []
    age: []
    lastmodified: "2025-05-14T13:28:08Z"
    mac: ENC[AES256_GCM,data:t/0Pn4RCzFiFSpKv2Y4gFuEHn2j9adG0j3ybU0xzj9I7lqPtOerlTjVyGVNbV67oesfoR6SRexnNeoM3edINH9V92Z2/XU1aFaG+jsPamH6QLrxmj8xtwbrP9qc9xlQjmcMmGSPsLpJgZS9DasNSqlIgafZazY5/RfNipolTA0k=,iv:/wXjZJnLoNz/4J+ALaH/BY7HhNS0xuWNR58PoFi+/oE=,tag:ATHttv3upYB8gQIDPmJbyQ==,type:str]
    pgp: []
    encrypted_regex: ^(data|stringData)$
    version: 3.8.1
