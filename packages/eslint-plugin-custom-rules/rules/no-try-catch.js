// no-try-catch.js
module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Disallow try/catch blocks in favour of neverthrow',
      category: 'Best practices',
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      noTryCatch: 'Try/catch blocks should not be used. Use ResultAsync.fromPromise() or other neverthrow methods instead.',
    },
  },
  create: function (context) {
    return {
      TryStatement(node) {
        context.report({
          node,
          messageId: 'noTryCatch',
        });
      },
    };
  },
};
