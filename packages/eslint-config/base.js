/*
 * This is a custom ESLint configuration for use with all of our packages and apps.
 *
 * Use it in any app/package by adding this to package.json dependencies:
 * - `"@packages/eslint-config": "workspace:*"`
 * And then extend it in .eslintrc.js:
 * - `extends: ['@packages/eslint-config/base.js']`
 */
module.exports = {
  plugins: ['@typescript-eslint', '@typescript-eslint/eslint-plugin', 'prettier', 'unused-imports', 'perfectionist', 'prefer-arrow', 'custom-rules'],
  ignorePatterns: ['node_modules/', 'dist/'],
  extends: ['plugin:@typescript-eslint/recommended', 'plugin:@typescript-eslint/stylistic', 'plugin:prettier/recommended'],
  rules: {
    'custom-rules/no-try-catch': 'warn',
    'no-unused-vars': 'off',
    'no-console': 'error',
    '@typescript-eslint/require-await': 'off',
    '@typescript-eslint/no-explicit-any': 'error',
    '@typescript-eslint/consistent-type-definitions': ['error', 'type'],
    '@typescript-eslint/member-ordering': ['error', { default: ['signature', 'field', 'private-method', 'constructor', 'accessor', 'get', 'set', 'method'] }],
    'unused-imports/no-unused-imports': 'warn',
    '@typescript-eslint/no-duplicate-enum-values': 'off',
    'no-restricted-syntax': [
      'error',
      {
        selector: 'TSEnumDeclaration',
        message: 'Enums are not allowed. Use union types or const objects instead.',
      },
      {
        selector: 'ExportNamedDeclaration > TSEnumDeclaration',
        message: 'Exported enums are not allowed. Use union types or const objects instead.',
      },
    ],
    'unused-imports/no-unused-vars': [
      'warn',
      {
        vars: 'all',
        varsIgnorePattern: '^_',
        args: 'after-used',
        argsIgnorePattern: '^_',
      },
    ],
    'prefer-arrow/prefer-arrow-functions': [
      'error',
      {
        disallowPrototype: true,
        singleReturnOnly: false,
        classPropertiesAllowed: false,
      },
    ],
    'perfectionist/sort-imports': [
      'error',
      {
        type: 'alphabetical',
        order: 'asc',
        ignoreCase: true,
        specialCharacters: 'keep',
        matcher: 'regex',
        newlinesBetween: 'always',
        maxLineLength: undefined,
        environment: 'node',
        groups: [['builtin', 'external'], 'business', 'lib', 'components', 'model', 'constants', 'internal', ['parent', 'sibling', 'index']],
        customGroups: {
          type: {},
          value: {
            business: '^.*(tests|service|repository|config/|([.]client)|([.]controller)|module|common/).*$',
            lib: '^.*lib/.*$',
            components: '^.*shared-component.*$',
            model: '^.*model.*$',
            constants: '^.*(constant|icon).*$',
          },
        },
      },
    ],
    curly: 'error',
    'prettier/prettier': ['error', { endOfLine: 'auto' }],
  },
};
