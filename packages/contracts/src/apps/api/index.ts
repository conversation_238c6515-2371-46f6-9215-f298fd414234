export * from './auth';
export * from './missions';
export * from './proposals';
export * from './accounts';
export * from './contracts';
export * from './interview-scheduling';

import { initContract } from '@ts-rest/core';

import accountsContract from './accounts/contracts/v1-accounts';
import adminAccountsContract from './accounts/contracts/v1-admin-accounts';
import authContract from './auth/contracts/v1-auth';
import contractsContract from './contracts/contracts/v1-contracts';
import calendarAvailabilitiesContract from './interview-scheduling/contracts/v1-calendar-availabilities';
import calendarEventsContract from './interview-scheduling/contracts/v1-calendar-events';
import calendarsContract from './interview-scheduling/contracts/v1-calendars';
import adminMissionsContract from './missions/contracts/v1-admin-missions';
import missionsContract from './missions/contracts/v1-missions';
import missionsPrefillContract from './missions/contracts/v1-missions-prefill';
import adminProposalsContract from './proposals/contracts/v1-admin-proposals';
import proposalsContract from './proposals/contracts/v1-proposals';

const c = initContract();

export const apiContract = c.router({
  auth: authContract,
  missions: missionsContract,
  missionsPrefill: missionsPrefillContract,
  proposals: proposalsContract,
  accounts: accountsContract,
  contracts: contractsContract,
  adminAccounts: adminAccountsContract,
  adminMissions: adminMissionsContract,
  adminProposals: adminProposalsContract,
  calendars: calendarsContract,
  calendarAvailabilities: calendarAvailabilitiesContract,
  calendarEvents: calendarEventsContract,
});
