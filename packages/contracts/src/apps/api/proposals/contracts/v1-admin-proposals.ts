import { initContract } from '@ts-rest/core';

import { authHeadersSchema, badRequestSchema, internalServerErrorSchema, notFoundSchema } from '../../../../lib/schemas/shared';

import {
  approveProposalBodySchema,
  approveProposalParamsSchema,
  approveProposalResponseSchema,
  getGeneratedSectionForCandidateResponseSchema,
  getGeneratedSectionForRoleResponseSchema,
  getGeneratedSectionForRoleQuerySchema,
  getMissionDataForProposalParamsSchema,
  getMissionDataForProposalResponseSchema,
  getRoleApplicationsForProposalParamsSchema,
  getRoleApplicationsForProposalResponseSchema,
  rejectProposalBodySchema,
  rejectProposalParamsSchema,
  rejectProposalResponseSchema,
  setProposalPublicUntilBodySchema,
  setProposalPublicUntilParamsSchema,
  setProposalPublicUntilResponseSchema,
  updateProposalCandidateSectionBodySchema,
  updateProposalCandidateSectionParamsSchema,
  updateProposalCandidateSectionResponseSchema,
  getGeneratedSectionForCandidateQuerySchema,
  createProposalBodySchema,
  createProposalResponseSchema,
  getTeamAdvisorsResponseSchema,
} from '../schemas/admin-proposals';

const c = initContract();

const adminProposalsContract = c.router(
  {
    updateProposalCandidateSection: {
      method: 'POST',
      path: '/:proposalId/update-candidate-section/:userId',
      pathParams: updateProposalCandidateSectionParamsSchema,
      body: updateProposalCandidateSectionBodySchema,
      responses: {
        200: updateProposalCandidateSectionResponseSchema,
      },
      summary: 'Update proposal candidate section',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    approveProposal: {
      method: 'POST',
      path: '/:proposalId/approve',
      pathParams: approveProposalParamsSchema,
      body: approveProposalBodySchema,
      responses: {
        200: approveProposalResponseSchema,
      },
      summary: 'Approve proposal',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    rejectProposal: {
      method: 'POST',
      path: '/:proposalId/reject',
      pathParams: rejectProposalParamsSchema,
      body: rejectProposalBodySchema,
      responses: {
        200: rejectProposalResponseSchema,
      },
      summary: 'Reject proposal',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    setProposalPublicUntil: {
      method: 'POST',
      path: '/:proposalId/set-public-until',
      pathParams: setProposalPublicUntilParamsSchema,
      body: setProposalPublicUntilBodySchema,
      responses: {
        200: setProposalPublicUntilResponseSchema,
      },
      summary: 'Set proposal public until value',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    getMissionDataForProposal: {
      method: 'GET',
      path: '/:missionId/mission-data',
      pathParams: getMissionDataForProposalParamsSchema,
      responses: {
        200: getMissionDataForProposalResponseSchema,
      },
      summary: 'Get mission data for proposal',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    getRoleApplicationsForProposal: {
      method: 'GET',
      path: '/:missionId/roles/:roleId/applications',
      pathParams: getRoleApplicationsForProposalParamsSchema,
      responses: {
        200: getRoleApplicationsForProposalResponseSchema,
      },
      summary: 'Get role applications for proposal',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    getGeneratedSectionForRole: {
      method: 'GET',
      path: '/roles/generated-section',
      query: getGeneratedSectionForRoleQuerySchema,
      responses: {
        200: getGeneratedSectionForRoleResponseSchema,
      },
      summary: 'Get generated section for role',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    getGeneratedSectionForCandidate: {
      method: 'GET',
      path: '/candidates/generated-section',
      query: getGeneratedSectionForCandidateQuerySchema,
      responses: {
        200: getGeneratedSectionForCandidateResponseSchema,
      },
      summary: 'Get generated section for candidate',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    createProposal: {
      method: 'POST',
      path: '/save',
      body: createProposalBodySchema,
      responses: {
        200: createProposalResponseSchema,
      },
      summary: 'Create proposal',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    getTeamAdvisors: {
      method: 'GET',
      path: '/team-advisors',
      responses: {
        200: getTeamAdvisorsResponseSchema,
      },
      summary: 'Get team advisors',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
  },
  {
    pathPrefix: '/admin-proposals/v1',
    commonResponses: {
      400: badRequestSchema,
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default adminProposalsContract;
