import { initContract } from '@ts-rest/core';

import { accountAuthHeadersSchema, internalServerErrorSchema, notFoundSchema } from '../../../../lib/schemas/shared';

import { getBuilderRecommendationsResponseSchema } from '../schemas/builder-recommendations';
import { getBuilderRecommendationsParamsSchema } from '../schemas/builder-recommendations';
import { getBuilderRecommendationsQuerySchema } from '../schemas/builder-recommendations';
import {
  getProposalBuildersParamsSchema,
  getProposalBuildersQuerySchema,
  getProposalBuildersResponseSchema,
  getProposalParamsSchema,
  getProposalResponseSchema,
} from '../schemas/proposals';

const c = initContract();

const proposalsContract = c.router(
  {
    getProposal: {
      method: 'GET',
      path: '/:proposalId',
      pathParams: getProposalParamsSchema,
      responses: {
        200: getProposalResponseSchema,
      },
      summary: 'Get proposal by id',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    getProposalBuilders: {
      method: 'GET',
      path: '/:proposalId/builders',
      pathParams: getProposalBuildersParamsSchema,
      query: getProposalBuildersQuerySchema,
      responses: {
        200: getProposalBuildersResponseSchema,
      },
      summary: 'Get proposal builders',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    getBuilderRecommendations: {
      method: 'GET',
      path: '/builder-recommendations/:proposalId/user/:userId',
      query: getBuilderRecommendationsQuerySchema,
      pathParams: getBuilderRecommendationsParamsSchema,
      responses: {
        200: getBuilderRecommendationsResponseSchema,
      },
    },
  },
  {
    pathPrefix: '/proposals/v1',
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default proposalsContract;
