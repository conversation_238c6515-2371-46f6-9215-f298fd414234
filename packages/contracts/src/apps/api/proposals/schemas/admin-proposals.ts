export * from './candidates';

import { z } from 'zod';

import { missionApplicationStagesSchema, userBadgesSchema } from '../../../../lib/schemas';

// Proposal update
export const updateProposalCandidateSectionParamsSchema = z.object({
  proposalId: z.string(),
  userId: z.string(),
});

const proposalCandidateSections = ['experience', 'requirements'] as const;

export const updateProposalCandidateSectionBodySchema = z.object({
  section: z.enum(proposalCandidateSections),
  text: z.string(),
  title: z.string().nullish(),
});

export type UpdateProposalCandidateSectionBody = z.infer<typeof updateProposalCandidateSectionBodySchema>;

export type updateProposalCandidateSectionData = z.infer<typeof updateProposalCandidateSectionBodySchema>;

export const updateProposalCandidateSectionResponseSchema = z.object({
  id: z.string(),
});

// Approve proposal

export const approveProposalParamsSchema = z.object({
  proposalId: z.string(),
});

export const approveProposalBodySchema = z
  .object({
    collaboratorIds: z.array(z.string()),
  })
  .optional();

export const approveProposalResponseSchema = z.object({
  id: z.string(),
});

export type ApproveProposalResponseDto = z.infer<typeof approveProposalResponseSchema>;

export type ApproveProposalParamsDto = z.infer<typeof approveProposalParamsSchema>;

export type ApproveProposalBodyDto = z.infer<typeof approveProposalBodySchema>;

// Reject proposal

export const rejectProposalParamsSchema = z.object({
  proposalId: z.string(),
});

export const rejectProposalBodySchema = z.object({}).optional();

export const rejectProposalResponseSchema = z.object({
  id: z.string(),
});

export type RejectProposalResponseDto = z.infer<typeof rejectProposalResponseSchema>;

export type RejectProposalParamsDto = z.infer<typeof rejectProposalParamsSchema>;

export type RejectProposalBodyDto = z.infer<typeof rejectProposalBodySchema>;

// Set proposal public until

export const setProposalPublicUntilParamsSchema = z.object({
  proposalId: z.string(),
});

export const setProposalPublicUntilBodySchema = z.object({
  publicUntil: z.coerce.date().nullable(),
});

export type SetProposalPublicUntilBodyDto = z.infer<typeof setProposalPublicUntilBodySchema>;

export const setProposalPublicUntilResponseSchema = z.object({
  id: z.string(),
});

export type SetProposalPublicUntilResponseDto = z.infer<typeof setProposalPublicUntilResponseSchema>;

export type SetProposalPublicUntilParamsDto = z.infer<typeof setProposalPublicUntilParamsSchema>;

// Get mission data for proposal

export const missionProposalRoleSkillSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export type MissionProposalRoleSkill = z.infer<typeof missionProposalRoleSkillSchema>;

export const missionProposalRoleSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string().nullish(),
  builderMinHourlyRate: z.number().nullish(),
  builderMaxHourlyRate: z.number().nullish(),
  minHoursPerWeek: z.number().nullish(),
  locations: z.array(z.string()),
  requiredSkills: z.array(missionProposalRoleSkillSchema),
  preferredSkills: z.array(missionProposalRoleSkillSchema),
});

export type MissionProposalRole = z.infer<typeof missionProposalRoleSchema>;

export const getMissionDataForProposalParamsSchema = z.object({
  missionId: z.string(),
});

export const getMissionDataForProposalResponseSchema = z.object({
  id: z.string(),
  title: z.string(),
  teamAdvisor: z.object({
    id: z.string(),
    name: z.string(),
    pictureURL: z.string().nullish(),
  }),
  roles: z.array(missionProposalRoleSchema),
});

export type MissionDataForProposal = z.infer<typeof getMissionDataForProposalResponseSchema>;

export type GetMissionDataForProposalParamsDto = z.infer<typeof getMissionDataForProposalParamsSchema>;

// Get role applications for proposal

export const getRoleApplicationsForProposalParamsSchema = z.object({
  missionId: z.string(),
  roleId: z.string(),
});

export type GetRoleApplicationsForProposalParamsDto = z.infer<typeof getRoleApplicationsForProposalParamsSchema>;

export const rateRangeSchema = z.object({
  min: z.number().nullish(),
  max: z.number().nullish(),
});

export type RateRange = z.infer<typeof rateRangeSchema>;

export const proposalRoleApplicationSchema = z.object({
  id: z.string(),
  stage: missionApplicationStagesSchema,
  hourlyRateRange: rateRangeSchema,
  monthlyRateRange: rateRangeSchema,
  user: z.object({
    id: z.string(),
    name: z.string(),
    pictureURL: z.string().nullish(),
    title: z.string().nullish(),
    badges: z.array(userBadgesSchema),
    cvUrl: z.string().nullish(),
    websites: z.object({
      personalWebsite: z.string().nullish(),
      githubUsername: z.string().nullish(),
      linkedinUsername: z.string().nullish(),
    }),
  }),
  customQuestion: z
    .object({
      questionText: z.string(),
      replyText: z.string(),
    })
    .nullish(),
});

export type ProposalRoleApplication = z.infer<typeof proposalRoleApplicationSchema>;

export const getRoleApplicationsForProposalResponseSchema = z.array(proposalRoleApplicationSchema);

export type GetRoleApplicationsForProposalResponseDto = z.infer<typeof getRoleApplicationsForProposalResponseSchema>;

// Get generated section for role

export const getGeneratedSectionForRoleQuerySchema = z.object({
  missionId: z.string(),
  roleId: z.string(),
});

export const getGeneratedSectionForRoleResponseSchema = z.object({
  section: z.string(),
});

export type GetGeneratedSectionForRoleResponseDto = z.infer<typeof getGeneratedSectionForRoleResponseSchema>;

export type GetGeneratedSectionForRoleQueryDto = z.infer<typeof getGeneratedSectionForRoleQuerySchema>;

// Get generated section for candidate

export const getGeneratedSectionForCandidateQuerySchema = z.object({
  missionId: z.string(),
  roleId: z.string(),
  userId: z.string(),
  section: z.enum(['experience-section', 'requirements-section']),
});

export const getGeneratedSectionForCandidateResponseSchema = z.object({
  title: z.string().nullish(),
  section: z.string(),
});

export type GetGeneratedSectionForCandidateResponseDto = z.infer<typeof getGeneratedSectionForCandidateResponseSchema>;

export type GetGeneratedSectionForCandidateQueryDto = z.infer<typeof getGeneratedSectionForCandidateQuerySchema>;

// Create proposal

const teamMemberSchema = z.object({
  roleId: z.string(),
  applicationId: z.string(),
  builderId: z.string(),
  showHourlyRate: z.boolean(),
  showMonthlyRate: z.boolean(),
  hourlyRate: z.coerce.number().optional(),
  monthlyRate: z.coerce.number().optional(),
  markup: z.coerce.number().optional(),
  website: z.string().optional(),
  githubUrl: z.string().optional(),
  portfolioUrl: z.string().optional(),
  portfolioPassword: z.string().optional(),
  linkedInUrl: z.string().optional(),
  cvUrl: z.string().optional(),
  totalHoursBilled: z.coerce.number().optional(),
  requirementsSectionTitle: z.string().optional(),
  requirementsSectionText: z.string().optional(),
  experienceSectionTitle: z.string().optional(),
  experienceSectionText: z.string().optional(),
  includeCustomQuestionReply: z.boolean().optional(),
});

export const createProposalBodySchema = z.object({
  teamAdvisorId: z.string(),
  currency: z.enum(['USD', 'EUR']),
  missionId: z.string(),
  roles: z.array(
    z.object({
      roleId: z.string(),
      aboutBuildersSection: z.string(),
    })
  ),
  teamMembers: z.array(teamMemberSchema),
});

export const createProposalResponseSchema = z.object({
  id: z.string(),
});

export type CreateProposalBodyDto = z.infer<typeof createProposalBodySchema>;
export type CreateProposalResponseDto = z.infer<typeof createProposalResponseSchema>;

// Get team advisors

export const getTeamAdvisorsResponseSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    pictureURL: z.string().nullish(),
  })
);

export type GetTeamAdvisorsResponseDto = z.infer<typeof getTeamAdvisorsResponseSchema>;
