import { z } from 'zod';

import { talentSkillSchema } from '../../missions/schemas';

export const proposalRoleSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  weeklyAvailability: z.number().optional(),
  locations: z.array(z.string()).optional(),
  requiredSkills: z.array(talentSkillSchema),
  preferredSkills: z.array(talentSkillSchema),
  aboutBuildersSection: z.string().optional(),
  builders: z.array(
    z.object({
      id: z.string(),
      pictureURL: z.string().optional(),
    })
  ),
});

export type ProposalRole = z.infer<typeof proposalRoleSchema>;

export const candidateSectionsDataSchema = z.object({
  title: z.string().nullish(),
  text: z.string().nullish(),
});
