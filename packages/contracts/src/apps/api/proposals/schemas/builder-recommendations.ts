import { z } from 'zod';

import { paginationSchema } from '../../../../lib/schemas/shared';

export const getBuilderRecommendationsQuerySchema = paginationSchema;

export const getBuilderRecommendationsParamsSchema = z.object({
  userId: z.string(),
});

export type GetBuilderRecommendationsParams = z.infer<typeof getBuilderRecommendationsParamsSchema>;

export type GetBuilderRecommendationsQuery = z.infer<typeof getBuilderRecommendationsQuerySchema>;

export const clientReviewSchema = z.object({
  id: z.string(),
  userId: z.string(),
  review: z.string().optional(),
  company: z
    .object({
      id: z.string(),
      name: z.string(),
      logo: z.string().optional(),
      industry: z.string().optional(),
    })
    .optional(),
  role: z
    .object({
      id: z.string(),
      name: z.string().optional(),
      startDate: z.date().optional(),
      endDate: z.date().optional(),
    })
    .optional(),
  createdAt: z.date(),
});

export const linkedinRecommendationSchema = z.object({
  id: z.string(),
  recommenderName: z.string(),
  companyLogo: z.string().optional(),
  companyName: z.string(),
  title: z.string(),
  occupation: z.string().optional(),
  recommendationText: z.string(),
  createdAt: z.date(),
});

const recommendationItemSchema = z.discriminatedUnion('source', [
  z.object({ source: z.literal('client') }).merge(clientReviewSchema),
  z.object({ source: z.literal('linkedin') }).merge(linkedinRecommendationSchema),
]);

export const getBuilderRecommendationsResponseSchema = z.object({
  count: z.number(),
  recommendations: z.array(recommendationItemSchema),
});

export type ClientReviewData = z.infer<typeof recommendationItemSchema>;
export type LinkedinRecommendationData = z.infer<typeof linkedinRecommendationSchema>;
export type RecommendationItemData = z.infer<typeof recommendationItemSchema>;
export type GetBuilderRecommendationsResponse = z.infer<typeof getBuilderRecommendationsResponseSchema>;
