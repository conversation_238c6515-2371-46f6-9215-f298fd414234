import { z } from 'zod';

import { candidateSectionsDataSchema, proposalRoleSchema } from './candidates';

// Get proposal

export const getProposalParamsSchema = z.object({
  proposalId: z.string(),
});

export type GetProposalParamsDto = z.infer<typeof getProposalParamsSchema>;

const proposalAdminReviewStatus = ['pending', 'approved', 'rejected'] as const;

export type ProposalAdminReviewStatus = (typeof proposalAdminReviewStatus)[number];

export const getProposalResponseSchema = z.object({
  id: z.string(),
  missionId: z.string(),
  accountId: z.string(),
  status: z.enum(proposalAdminReviewStatus),
  publicUntil: z.date().nullish(),
  roles: z.array(proposalRoleSchema),
  sharedBy: z
    .object({
      id: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    })
    .nullish(),
  teamAdvisor: z.object({
    id: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    pictureURL: z.string().optional(),
  }),
});
export type GetProposalResponseDto = z.infer<typeof getProposalResponseSchema>;

// Get proposal builders

export const getProposalBuildersParamsSchema = z.object({
  proposalId: z.string(),
});

export const getProposalBuildersQuerySchema = z.object({
  builderIds: z
    .string()
    .min(1)
    .transform((ids) => ids.split(',')),
});

export const proposalBuilderPortfolioSchema = z.object({
  url: z.string(),
  hasPassword: z.boolean(),
  password: z.string().nullish(),
});

export type ProposalBuilderPortfolioDto = z.infer<typeof proposalBuilderPortfolioSchema>;

export const getProposalBuildersResponseSchema = z.array(
  z.object({
    id: z.string(),
    username: z.string().nullish(),
    firstName: z.string().nullish(),
    lastName: z.string().nullish(),
    pictureURL: z.string().nullish(),
    cvUrl: z.string().nullish(),
    yearsOfExperience: z.number().nullish(),
    availableHoursPerWeek: z.number().nullish(),
    websites: z.object({
      linkedInUrl: z.string().nullish(),
      personalWebsite: z.string().nullish(),
      githubUrl: z.string().nullish(),
    }),
    portfolio: proposalBuilderPortfolioSchema.nullish(),
    cardSections: z
      .object({
        requirements: candidateSectionsDataSchema,
        experience: candidateSectionsDataSchema,
      })
      .nullish(),
    location: z
      .object({
        country: z.string(),
        countryShortName: z.string().nullish(),
        province: z.string().nullish(),
        city: z.string().nullish(),
      })
      .nullish(),
    skills: z.array(
      z.object({
        id: z.string(),
        name: z.string(),
      })
    ),
    clientHourlyRate: z.number().nullish(),
    clientMonthlyRate: z.number().nullish(),
    customQuestion: z
      .object({
        id: z.string(),
        questionText: z.string(),
        replyText: z.string(),
      })
      .nullish(),
    hoursWorked: z.number(),
    builderRecommendationsCount: z.number(),
    everHadClientInterview: z.boolean().optional(),
  })
);

export type GetProposalBuildersResponseDto = z.infer<typeof getProposalBuildersResponseSchema>;

export type ProposalBuilder = GetProposalBuildersResponseDto[number];
