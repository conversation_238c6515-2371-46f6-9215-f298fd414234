import { MISSION_PLANNED_START_VALUES, MISSION_ROLE_REMOVAL_REASONS, MISSION_ROLE_STATUSES, MISSION_STATUSES, MISSION_TIME_OVERLAP_VALUES } from './constants';

export type MissionStatus = (typeof MISSION_STATUSES)[number];
export type MissionRoleStatus = (typeof MISSION_ROLE_STATUSES)[number];
export type MissionPlannedStart = (typeof MISSION_PLANNED_START_VALUES)[number];
export type MissionTimeOverlap = (typeof MISSION_TIME_OVERLAP_VALUES)[number];
export type MissionRoleRemovalReason = (typeof MISSION_ROLE_REMOVAL_REASONS)[number];

export type MissionPlannedStartOption = {
  value: MissionPlannedStart;
  label: string;
};

export type MissionTimeOverlapOption = {
  value: MissionTimeOverlap;
  label: string;
};
