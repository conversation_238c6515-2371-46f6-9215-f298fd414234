import { MissionStatus } from './types';

export const MISSION_STATUSES = ['Spec', 'Formation', 'Created', 'Published', 'Pending', 'Running', 'ScheduledToEnd', 'Ended', 'Archived'] as const;

export const MISSION_EDITABLE_STATUSES: readonly MissionStatus[] = ['Spec', 'Formation'];

export const MISSION_APPLY_STATUSES = ['Collect', 'Review', 'Finalize', 'Deployed'] as const;

export const MISSION_BILLING_PERIOD_VALUES = ['Daily (Test)', 'Weekly', 'BiWeekly', 'Monthly'] as const;

export const MISSION_BILLING_PAYMENT_DUE_VALUES = ['Net0', 'Net15', 'Net30', 'Net45', 'Net60', 'Net90', 'Net120'] as const;

export const MISSION_MANAGERS_ACCESS_MODES = ['RoleView'] as const;

export const MISSION_ROLE_STATUSES = ['Open', 'Active', 'Canceled', 'ScheduledToEnd', 'Ended', 'Pending'] as const;

export const MISSION_ROLE_VISIBILITY_STATUSES = ['OnlyAdmin', 'All'] as const;

export const MISSION_PLANNED_START_VALUES = ['Immediately', 'Next month', 'Within 3 months', 'Exploring'] as const;

export const MISSION_TIME_OVERLAP_VALUES = ['no_overlap', '2h', '4h', '6h', '8h'] as const;

export const MISSION_ROLE_TIME_COMMITMENT_VALUES = ['5h', '10h', '20h', '30h', '40h'] as const;

export const MISSION_ROLE_TIME_BUDGET_TYPE = ['monthly', 'hourly'] as const;

export const MISSION_ROLE_REMOVAL_REASONS = ['Project scope change', 'Budget constraints', 'Found talent internally', 'Other'] as const;

export const MISSION_ROLE_REMOVAL_COMMENT_MIN_CHARS = 20;

export const MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS = 1000;

export const MISSION_PLANNED_START_OPTIONS = [
  { value: 'Immediately', label: 'We need people to start immediately' },
  { value: 'Next month', label: 'We need people to start next month' },
  { value: 'Within 3 months', label: 'We need people to start within 3 months' },
  { value: 'Exploring', label: "We're just exploring" },
] as const;

export const MISSION_TIME_OVERLAP_OPTIONS = [
  { value: 'no_overlap', label: 'No overlap' },
  { value: '2h', label: '2 hours' },
  { value: '4h', label: '4 hours' },
  { value: '6h', label: '6 hours' },
  { value: '8h', label: '8 hours' },
] as const;
