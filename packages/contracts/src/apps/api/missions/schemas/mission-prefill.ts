import { z } from 'zod';

export const createMissionPrefillSchema = z.object({
  userId: z.string(),
});

export type MissionPrefillValues = z.infer<typeof createMissionPrefillSchema>;

export const prefillRoleSchema = z.object({
  categoryId: z.string(),
  headline: z.string().nullable(),
  requiredSkills: z.array(z.string()).nullable(),
  preferredSkills: z.array(z.string()).nullable(),
  screeningQuestion: z.string().nullable(),
  timeCommitment: z.string().nullable(),
});

export const getMissionPrefillSchema = z.object({
  id: z.string(),
  userId: z.string(),
  missionName: z.string().nullable(),
  plannedStart: z.string().nullable(),
  companyDescription: z.string().nullable(),
  missionDescription: z.string().nullable(),
  timezone: z.string().nullable(),
  openRoles: z.array(prefillRoleSchema).nullable(),
});

export type GetMissionPrefillResponse = z.infer<typeof getMissionPrefillSchema>;
