import { z } from 'zod';

import { MISSION_ROLE_STATUSES, MISSION_ROLE_VISIBILITY_STATUSES } from '../helpers';

// Publish role
const roleSkillSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export const publishRoleSchema = z.object({
  rid: z.string(),
  status: z.enum(MISSION_ROLE_STATUSES),
  title: z.string(),
  visibilityStatus: z.enum(MISSION_ROLE_VISIBILITY_STATUSES),
  description: z.string().default(''),
  customQuestion: z
    .object({
      qid: z.string(),
      text: z.string(),
      isRequired: z.boolean().default(false),
      isVisible: z.boolean().default(false),
    })
    .optional(),
  isNiche: z.boolean().default(false),
  markup: z.number(),
  // hourly rates
  builderRateMin: z.number().optional(),
  builderRateMax: z.number().optional(),
  clientRateMin: z.number().optional(),
  clientRateMax: z.number().optional(),
  collectBuilderHourlyRate: z.boolean().default(false),
  showClientHourlyBudget: z.boolean().default(false),
  // monthly rates
  builderMonthlyRateMin: z.number().optional(),
  builderMonthlyRateMax: z.number().optional(),
  clientMonthlyRateMin: z.number().optional(),
  clientMonthlyRateMax: z.number().optional(),
  collectBuilderMonthlyRate: z.boolean().default(false),
  showClientMonthlyBudget: z.boolean().default(false),
  // additional role info
  minimumHoursPerWeek: z.number().optional(),
  timezone: z.string().optional(),
  workingHoursStartTime: z.number().optional(),
  workingHoursEndTime: z.number().optional(),
  hoursOverlapMinutes: z.number().optional(),
  requiredSkills: z.array(roleSkillSchema).default([]),
  preferredSkills: z.array(roleSkillSchema).default([]),
  allowedCountries: z.array(z.string()).default([]),
  automatedStatusesAsignmentDisabled: z.boolean().optional(),
  roleHiddenFromBuilders: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        pictureUrl: z.string().optional(),
      })
    )
    .default([]),
  readyForReview: z.boolean().default(false),
});

// Assign role
