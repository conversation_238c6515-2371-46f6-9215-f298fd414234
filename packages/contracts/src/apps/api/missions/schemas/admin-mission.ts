import { z } from 'zod';

import {
  MISSION_APPLY_STATUSES,
  MISSION_BILLING_PAYMENT_DUE_VALUES,
  MISSION_BILLING_PERIOD_VALUES,
  MISSION_MANAGERS_ACCESS_MODES,
  MISSION_STATUSES,
} from '../helpers';
import { publishRoleSchema } from './role-setting';

// Shared
const baseMissionIndustrySchema = z.object({
  id: z.string(),
  name: z.string(),
});

// Get mission industries
export const getMissionIndustriesDtoSchema = z.array(baseMissionIndustrySchema);
export type GetMissionIndustries = z.infer<typeof getMissionIndustriesDtoSchema>;

// Get tfs owners
export const getTfsOwnersDtoSchema = z.array(
  z.object({
    id: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    profilePictureURL: z.string().nullish(),
  })
);

export type GetTfsOwnersDto = z.infer<typeof getTfsOwnersDtoSchema>;

// Get mission role categories
export const getMissionRoleCategoriesDtoSchema = z.array(
  z.object({
    id: z.string(),
    title: z.string(),
  })
);

export type GetMissionRoleCategoriesDto = z.infer<typeof getMissionRoleCategoriesDtoSchema>;

// Get mission role skills
export const getMissionRoleSkillsDtoSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
  })
);

export type GetMissionRoleSkillsDto = z.infer<typeof getMissionRoleSkillsDtoSchema>;

// Get mission role skills
export const getUsersToExcludeQuerySchema = z.object({
  query: z.string(),
});

export const getUsersToExcludeDtoSchema = z.array(
  z.object({
    id: z.string(),
    name: z.string(),
    pictureUrl: z.string().optional(),
  })
);

export type GetUsersToExcludeDto = z.infer<typeof getUsersToExcludeDtoSchema>;

// Get admin settings
const baseAdminSettingsDtoSchema = z.object({
  missionStatus: z.enum(MISSION_STATUSES),
  applyStatus: z.enum(MISSION_APPLY_STATUSES).optional(),
  timesheetPeriods: z.enum(MISSION_BILLING_PERIOD_VALUES).optional(),
  automaticInvoicing: z.string().optional(),
  tfsOwner: z.string().optional(),
  invoicePO: z.string().optional(),
  billingAddress: z
    .object({
      line1: z.string().optional(),
      line2: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      country: z.string().optional(),
    })
    .optional(),
  paymentTerms: z.enum(MISSION_BILLING_PAYMENT_DUE_VALUES).optional(),
  generateContracts: z.boolean().default(false),
});

export const getAdminSettingsDtoSchema = baseAdminSettingsDtoSchema.extend({
  managers: z.array(
    z.object({
      accessMode: z.enum(MISSION_MANAGERS_ACCESS_MODES),
      username: z.string(),
      user: z.object({
        uid: z.string(),
        firstName: z.string(),
        lastName: z.string(),
        profilePictureURL: z.string().url().optional(),
      }),
    })
  ),
});

export type GetAdminSettingsDto = z.infer<typeof getAdminSettingsDtoSchema>;

// Update admin settings
export const updateAdminSettingsDtoSchema = baseAdminSettingsDtoSchema;
export type UpdateAdminSettingsDto = z.infer<typeof updateAdminSettingsDtoSchema>;

// Get mission settings
const missionSettingsDtoSchema = z.object({
  companyLogo: z.string().url().optional(),
  videoUrl: z.string().url().optional(),
  missionName: z.string(),
  companyDescription: z.string(),
  missionDescription: z.string(),
  isInternalMission: z.boolean().default(false),
  industries: z.array(baseMissionIndustrySchema).default([]),
  duration: z.coerce.number().min(1).max(24).default(1),
  isUnderWraps: z.boolean().default(false),
  missionResources: z.array(z.object({ title: z.string(), url: z.string().url() })).default([]),
});

export const getMissionSettingsDtoSchema = missionSettingsDtoSchema;
export type GetMissionSettingsDto = z.infer<typeof getMissionSettingsDtoSchema>;

// Update admin settings
export const updateMissionSettingsDtoSchema = getMissionSettingsDtoSchema;
export type UpdateMissionSettingsDto = z.infer<typeof updateMissionSettingsDtoSchema>;

// Get role settings
export const roleSettingDtoSchema = publishRoleSchema;
export type RoleSettingDto = z.infer<typeof roleSettingDtoSchema>;

const roleSettingsDtoSchema = z.object({
  openRoles: z.array(roleSettingDtoSchema),
  activeRoles: z.array(roleSettingDtoSchema),
  endedRoles: z.array(roleSettingDtoSchema),
  canceledRoles: z.array(roleSettingDtoSchema),
  missionStatus: z.enum(MISSION_STATUSES),
});

export const getRoleSettingsDtoSchema = roleSettingsDtoSchema;
export type GetRoleSettingsDto = z.infer<typeof getRoleSettingsDtoSchema>;

// Update role settings
export const updateRoleSettingsDtoSchema = roleSettingsDtoSchema;
export type UpdateRoleSettingsDto = z.infer<typeof updateRoleSettingsDtoSchema>;
