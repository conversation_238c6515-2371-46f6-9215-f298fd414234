import { z } from 'zod';

import {
  MISSION_PLANNED_START_VALUES,
  MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS,
  MISSION_ROLE_REMOVAL_COMMENT_MIN_CHARS,
  MISSION_ROLE_REMOVAL_REASONS,
  MISSION_STATUSES,
  MISSION_TIME_OVERLAP_VALUES,
} from '../helpers';
import { baseRoleSchema, confirmRoleSchema, draftRoleSchema } from './role';

export const draftMissionSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be 100 characters or less'),
  logoURL: z.string().url().nullable(),
  videoURL: z.string().url().nullable(),
  plannedStart: z.enum(MISSION_PLANNED_START_VALUES).optional(),
  timeOverlap: z.enum(MISSION_TIME_OVERLAP_VALUES).optional(),
  timezone: z.string().optional(),
  description: z.string().optional(),
  companyStory: z.string().optional(),
  roles: z.array(draftRoleSchema),
});

export type DraftMissionDto = z.infer<typeof draftMissionSchema>;

export const confirmMissionSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title must be 100 characters or less'),
  logoURL: z.string().url().nullable(),
  videoURL: z.string().url().nullable(),
  plannedStart: z.enum(MISSION_PLANNED_START_VALUES),
  timeOverlap: z.enum(MISSION_TIME_OVERLAP_VALUES),
  timezone: z.string(),
  description: z.string().min(20, 'Project description must be at least 20 characters').max(1000, 'Project description must be 1000 characters or less'),
  companyStory: z.string().min(20, 'Company description must be at least 20 characters').max(1000, 'Company description must be 1000 characters or less'),
  roles: z.array(confirmRoleSchema),
});

export type ConfirmMissionDto = z.infer<typeof confirmMissionSchema>;

export const publishMissionSchema = z.object({
  hubspotDealId: z.string(),
});

export type PublishMissionDto = z.infer<typeof publishMissionSchema>;

export const getMissionSchema = z.object({
  mid: z.string(),
  status: z.enum(MISSION_STATUSES),
  title: z.string(),
  logoURL: z.string().url().nullable(),
  videoURL: z.string().url().nullable(),
  description: z.string().nullable(),
  companyStory: z.string().nullable(),
  plannedStart: z.string().nullable(),
  overlapMinutes: z.number().nullable(),
  timezone: z.string().nullable(),
  roles: z.array(
    baseRoleSchema.extend({
      headline: z.string().nullable(),
      isFullTimeRetainer: z.boolean().nullable(),
      markup: z.number().nullable(),
      budget: z.number().nullable(),
      requiredSkills: z.array(
        z.object({
          talentSkillId: z.string(),
        })
      ),
      preferredSkills: z.array(
        z.object({
          talentSkillId: z.string(),
        })
      ),
      locations: z.array(z.string()),
      availability: z
        .object({
          weeklyHoursAvailable: z.number(),
          date: z.date().nullable(),
        })
        .nullable(),
      customQuestions: z
        .array(
          z.object({
            text: z.string(),
          })
        )
        .optional(),
    })
  ),
});

export type GetMissionResponse = z.infer<typeof getMissionSchema>;

export const requestRoleRemovalSchema = z.object({
  reason: z.enum(MISSION_ROLE_REMOVAL_REASONS),
  comment: z.string().min(MISSION_ROLE_REMOVAL_COMMENT_MIN_CHARS).max(MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS).optional(),
});

export type RequestRoleRemovalSchemaDto = z.infer<typeof requestRoleRemovalSchema>;

export const queryRateGuidanceSchema = z.object({
  categoryId: z.string(),
  countries: z.string().optional(),
  markupPercentage: z.string().optional(),
});

export type QueryRateGuidaceSchemaDto = z.infer<typeof queryRateGuidanceSchema>;

export const getRateGuidanceSchema = z.object({
  lowerBound: z.number(),
  upperBound: z.number(),
});

export type GetRateGuidaceSchemaDto = z.infer<typeof getRateGuidanceSchema>;
