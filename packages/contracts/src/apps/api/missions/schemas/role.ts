import { z } from 'zod';

import { MISSION_ROLE_STATUSES, MISSION_ROLE_TIME_BUDGET_TYPE, MISSION_ROLE_TIME_COMMITMENT_VALUES } from '../helpers';

export const roleCateorySchema = z.object({
  id: z.string(),
  title: z.string(),
});

export const talentSkillSchema = z.object({
  id: z.string(),
  name: z.string(),
});

const assignedUserSchema = z.object({
  id: z.string(),
  firstName: z.string(),
  lastName: z.string().nullable(),
  pictureURL: z.string().url().nullable(),
});

export type AssignedUserDto = z.infer<typeof assignedUserSchema>;

export const baseRoleSchema = z.object({
  id: z.string(),
  status: z.enum(MISSION_ROLE_STATUSES),
  categoryId: z.string(),
  assignedUser: assignedUserSchema.optional(),
});

export type BaseRoleDto = z.infer<typeof baseRoleSchema>;

export const draftRoleSchema = baseRoleSchema.extend({
  headline: z.string().optional(),
  timeCommitment: z.enum(MISSION_ROLE_TIME_COMMITMENT_VALUES).optional(),
  budgetType: z.enum(MISSION_ROLE_TIME_BUDGET_TYPE).optional(),
  budget: z.number().optional(),
  markupPercentage: z.number().optional(),
  screeningQuestion: z.string().optional(),
  requiredSkills: z.array(z.string()).optional(),
  preferredSkills: z.array(z.string()).optional(),
  locations: z.array(z.string()).optional(),
});

export type DraftRoleDto = z.infer<typeof draftRoleSchema>;

export const confirmRoleSchema = baseRoleSchema.extend({
  headline: z.string().min(20, 'Role description must be at least 20 characters').max(100, 'Role headline must be 100 characters or less'),
  timeCommitment: z.enum(MISSION_ROLE_TIME_COMMITMENT_VALUES),
  budgetType: z.enum(MISSION_ROLE_TIME_BUDGET_TYPE),
  budget: z.number(),
  markupPercentage: z.number().min(0).max(100),
  screeningQuestion: z.string().optional(),
  requiredSkills: z.array(z.string()),
  preferredSkills: z.array(z.string()),
  locations: z.array(z.string()),
});

export type ConfirmRoleDto = z.infer<typeof confirmRoleSchema>;
