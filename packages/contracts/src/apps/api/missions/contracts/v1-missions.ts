import { initContract } from '@ts-rest/core';
import { z } from 'zod';

import { authHeadersSchema, internalServerErrorSchema } from '../../../../lib/schemas/shared';

import {
  badRequestSchema,
  confirmMissionSchema,
  confirmRoleSchema,
  draftMissionSchema,
  forbiddenSchema,
  getHubspotDealSchema,
  getMissionSchema,
  getRateGuidanceSchema,
  notFoundSchema,
  publishMissionSchema,
  queryRateGuidanceSchema,
  requestRoleRemovalSchema,
  roleCateorySchema,
  talentSkillSchema,
} from '../schemas';

const c = initContract();

export const v1MissionsContract = c.router(
  {
    getMission: {
      method: 'GET',
      path: '/:id',
      responses: {
        200: getMissionSchema,
      },
      summary: 'Get mission by ID',
      strictStatusCodes: true,
    },
    getRoleCategories: {
      method: 'GET',
      path: '/role/categories',
      responses: {
        200: z.array(roleCateorySchema),
      },
      summary: 'Get role categories',
      strictStatusCodes: true,
    },
    getTalentSkills: {
      method: 'GET',
      path: '/talent/skills',
      responses: {
        200: z.array(talentSkillSchema),
      },
      summary: 'Get talent skills',
      strictStatusCodes: true,
    },
    getHubspotDeal: {
      method: 'GET',
      path: '/hubspot/deal/:dealId',
      responses: {
        200: getHubspotDealSchema,
        404: null,
      },
      summary: "Gets the hubspot deal by id. If the deal isn't found, null is returned",
      strictStatusCodes: true,
    },
    createMission: {
      method: 'POST',
      path: '/',
      body: draftMissionSchema,
      responses: {
        201: z.object({
          mid: z.string(),
        }),
        400: badRequestSchema,
      },
      summary: 'Create a new mission',
      strictStatusCodes: true,
    },
    updateMission: {
      method: 'PUT',
      path: '/:id',
      body: draftMissionSchema,
      responses: {
        200: c.noBody(),
        400: badRequestSchema,
        422: badRequestSchema,
      },
      summary: 'Update an existing mission',
      strictStatusCodes: true,
    },
    confirmMission: {
      method: 'POST',
      path: '/:id/confirm',
      body: confirmMissionSchema,
      responses: {
        200: c.noBody(),
        400: badRequestSchema,
      },
      summary: 'Confirm an existing mission',
      strictStatusCodes: true,
    },
    publishMission: {
      method: 'POST',
      path: '/:id/publish',
      body: publishMissionSchema,
      responses: {
        200: c.noBody(),
        400: badRequestSchema,
      },
      summary: 'Publish a confirmed mission',
      strictStatusCodes: true,
    },
    requestNewRole: {
      method: 'POST',
      path: '/:id/role',
      body: confirmRoleSchema,
      responses: {
        200: c.noBody(),
        422: badRequestSchema,
      },
      summary: 'Request a new role on a mission',
      strictStatusCodes: true,
    },
    approveRoleRequest: {
      method: 'POST',
      path: '/:id/role/:roleId/approve',
      body: null,
      responses: {
        200: c.noBody(),
      },
      summary: 'Approve a role request on a mission',
      strictStatusCodes: true,
    },
    rejectRoleRequest: {
      method: 'POST',
      path: '/:id/role/:roleId/reject',
      body: null,
      responses: {
        200: c.noBody(),
      },
      summary: 'Reject a role request on a mission',
      strictStatusCodes: true,
    },
    requestRoleRemoval: {
      method: 'POST',
      path: '/:id/role/removal/:roleId',
      body: requestRoleRemovalSchema,
      responses: {
        200: c.noBody(),
        422: badRequestSchema,
      },
      summary: 'Request role removal on a mission',
      strictStatusCodes: true,
    },
    getRoleRateGuidance: {
      method: 'GET',
      path: '/role/rate-guidance',
      query: queryRateGuidanceSchema,
      responses: {
        200: getRateGuidanceSchema,
        404: c.noBody(),
      },
      summary: 'Get mission role rate guidance',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/missions/v1',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      403: forbiddenSchema,
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1MissionsContract;
