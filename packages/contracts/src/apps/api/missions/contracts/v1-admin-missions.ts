import { initContract } from '@ts-rest/core';

import { notFoundSchema } from '../../../../lib/schemas/shared';
import { internalServerErrorSchema } from '../../../../lib/schemas/shared';

import {
  updateAdminSettingsDtoSchema,
  getMissionIndustriesDtoSchema,
  getTfsOwnersDtoSchema,
  getAdminSettingsDtoSchema,
  getMissionSettingsDtoSchema,
  updateMissionSettingsDtoSchema,
  getRoleSettingsDtoSchema,
  updateRoleSettingsDtoSchema,
  getMissionRoleCategoriesDtoSchema,
  getMissionRoleSkillsDtoSchema,
  getUsersToExcludeQuerySchema,
  getUsersToExcludeDtoSchema,
} from '../schemas/admin-mission';

const c = initContract();

const v1AdminMissionsContract = c.router(
  {
    getMissionIndustries: {
      method: 'GET',
      path: '/industries',
      responses: {
        200: getMissionIndustriesDtoSchema,
      },
      summary: 'Gets the mission industries',
      strictStatusCodes: true,
    },
    getTfsOwners: {
      method: 'GET',
      path: '/tfs-owners/all',
      responses: {
        200: getTfsOwnersDtoSchema,
      },
      summary: 'Gets the tfs users and their required info',
      strictStatusCodes: true,
    },
    getMissionRoleCategories: {
      method: 'GET',
      path: '/role-categories',
      responses: {
        200: getMissionRoleCategoriesDtoSchema,
      },
      summary: 'Gets the mission role categories',
      strictStatusCodes: true,
    },
    getMissionRoleSkills: {
      method: 'GET',
      path: '/role-skills',
      responses: {
        200: getMissionRoleSkillsDtoSchema,
      },
      summary: 'Gets the mission role skills',
      strictStatusCodes: true,
    },
    getUsersToExclude: {
      method: 'GET',
      path: '/users-to-exclude',
      query: getUsersToExcludeQuerySchema,
      responses: {
        200: getUsersToExcludeDtoSchema,
      },
      summary: 'Gets the builders based on the query to exclude',
      strictStatusCodes: true,
    },
    getAdminSettings: {
      method: 'GET',
      path: '/:id/admin-settings',
      responses: {
        200: getAdminSettingsDtoSchema,
      },
      summary: 'Get admin settings for a particular mission',
      strictStatusCodes: true,
    },
    updateAdminSettings: {
      method: 'PUT',
      path: '/:id/admin-settings',
      body: updateAdminSettingsDtoSchema,
      responses: {
        200: c.noBody(),
      },
      summary: 'Update admin settings for a particular mission',
      strictStatusCodes: true,
    },
    getMissionSettings: {
      method: 'GET',
      path: '/:id/mission-settings',
      responses: {
        200: getMissionSettingsDtoSchema,
      },
      summary: 'Get mission settings for a particular mission',
      strictStatusCodes: true,
    },
    updateMissionSettings: {
      method: 'PUT',
      path: '/:id/mission-settings',
      body: updateMissionSettingsDtoSchema,
      responses: {
        200: c.noBody(),
      },
      summary: 'Update mission settings for a particular mission',
      strictStatusCodes: true,
    },
    getRoleSettings: {
      method: 'GET',
      path: '/:id/role-settings',
      responses: {
        200: getRoleSettingsDtoSchema,
      },
      summary: 'Get role settings for a particular mission',
      strictStatusCodes: true,
    },
    updateRoleSettings: {
      method: 'PUT',
      path: '/:id/role-settings',
      body: updateRoleSettingsDtoSchema,
      responses: {
        200: c.noBody(),
      },
      summary: 'Update role settings for a particular mission',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/admin-missions/v1',
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1AdminMissionsContract;
