import { initContract } from '@ts-rest/core';

import { authHeadersSchema } from '../../../../lib/schemas/shared';

import { getMissionPrefillSchema, notFoundSchema } from '../schemas';

const c = initContract();

export const v1MissionsPrefillContract = c.router(
  {
    getMissionPrefill: {
      method: 'GET',
      path: '/',
      responses: {
        200: getMissionPrefillSchema,
      },
      summary: 'Get mission prefill for the authenticated user',
      strictStatusCodes: true,
    },
    generateMissionPrefill: {
      method: 'POST',
      path: '/',
      responses: {
        201: getMissionPrefillSchema,
      },
      strictStatusCodes: true,
      summary: 'Generate mission prefill for the authenticated user. If the mission prefill already exists for this user, it will be updated.',
      body: c.noBody(),
    },
  },
  {
    pathPrefix: '/missions-prefill/v1',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: notFoundSchema,
    },
  }
);

export default v1MissionsPrefillContract;
