import { initContract } from '@ts-rest/core';
import { z } from 'zod';

import { accountAuthHeadersSchema, apiKeyAuthHeadersSchema, authHeadersSchema, internalServerErrorSchema } from '../../../../lib/schemas/shared';

import { notFoundSchema, badRequestSchema } from '../../../api';
import {
  agreeToTermsOfServiceBodySchema,
  agreeToTermsOfServiceResponseSchema,
  createCustomContractsBodyDtoSchema,
  createCustomContractsResponseDtoSchema,
  deleteContractBodySchema,
  deleteContractResponseSchema,
  getContractResponseDtoSchema,
  getContractsQueryDtoSchema,
  getContractsResponseDtoSchema,
  hubspotDealWebhookRequestBodyDtoSchema,
  hubspotDealWebhookResponseDtoSchema,
  pandadocWebhookRequestBodyDtoSchema,
  pandadocWebhookResponseDtoSchema,
  updateContractBodySchema,
  updateContractResponseSchema,
} from '../schemas/index';

const c = initContract();

export const contractsContract = c.router(
  {
    pandadocWebhookHandler: {
      method: 'POST',
      path: '/webhook/pandadoc',
      responses: {
        201: pandadocWebhookResponseDtoSchema,
      },
      body: pandadocWebhookRequestBodyDtoSchema,
      summary: 'Webhook to call when document is created on pandadoc',
      strictStatusCodes: true,
    },
    hubspotDealWebhook: {
      method: 'POST',
      path: '/webhook/hubspot/deal',
      responses: {
        200: hubspotDealWebhookResponseDtoSchema,
        400: badRequestSchema,
        404: notFoundSchema,
      },
      body: hubspotDealWebhookRequestBodyDtoSchema,
      summary: "Webhook to call when hubspot deal is updated to update deal's contract properties",
      strictStatusCodes: true,
      headers: apiKeyAuthHeadersSchema,
    },
    getContracts: {
      method: 'GET',
      path: '/',
      responses: {
        200: getContractsResponseDtoSchema,
      },
      query: getContractsQueryDtoSchema,
      summary: 'Fetch contracts',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    getContract: {
      method: 'GET',
      path: '/:id',
      responses: {
        200: getContractResponseDtoSchema,
      },
      summary: 'Get contract',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    createCustomContracts: {
      method: 'POST',
      path: '/custom',
      responses: {
        201: createCustomContractsResponseDtoSchema,
      },
      body: createCustomContractsBodyDtoSchema,
      summary: 'Create custom contracts',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    getPandadocPdf: {
      method: 'GET',
      path: '/pdf/:pandadocId/raw',
      responses: {
        200: c.otherResponse({ contentType: 'application/pdf', body: z.any() }),
      },
      summary: 'Get pdf from pandadoc',
      strictStatusCodes: true,
      headers: authHeadersSchema,
    },
    updateContract: {
      method: 'PUT',
      path: '/:id',
      responses: {
        200: updateContractResponseSchema,
        404: notFoundSchema,
      },
      body: updateContractBodySchema,
      summary: 'Update contract',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    deleteContract: {
      method: 'DELETE',
      path: '/:id',
      responses: {
        200: deleteContractResponseSchema,
      },
      body: deleteContractBodySchema,
      summary: 'Delete contract',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
    agreeToTermsOfService: {
      method: 'POST',
      path: '/tos/accept',
      responses: {
        200: agreeToTermsOfServiceResponseSchema,
      },
      body: agreeToTermsOfServiceBodySchema,
      summary: 'Agree to terms of service',
      strictStatusCodes: true,
      headers: accountAuthHeadersSchema,
    },
  },
  {
    pathPrefix: '/contracts/v1',
    commonResponses: {
      400: badRequestSchema,
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default contractsContract;
