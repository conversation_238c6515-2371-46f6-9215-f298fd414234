import { z } from 'zod';

export const bySchema = z.object({
  id: z.string(),
  email: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  membership_id: z.string(),
});

export const grandTotalSchema = z.object({
  amount: z.string(),
  currency: z.string(),
});

export const linkedObjectProviderSchema = z.literal('hubspot');

export const linkedObjectEntityTypeSchema = z.union([z.literal('deal'), z.literal('company'), z.literal('contact')]);

export const linkedObjectSchema = z.object({
  id: z.string(),
  provider: linkedObjectProviderSchema,
  entity_type: linkedObjectEntityTypeSchema,
  entity_id: z.string(),
});

export type PandadocLinkedObjectSchema = z.infer<typeof linkedObjectSchema>;

export const metadataSchema = z.object({
  document__creation_source: z.string().optional(),
});

export const recipientTypeSchema = z.union([z.literal('signer'), z.literal('CC')]);
export type RecipientType = z.infer<typeof recipientTypeSchema>;

export const recipientSchema = z.object({
  type: z.string(),
  id: z.string(),
  first_name: z.string().nullable(),
  last_name: z.string().nullable(),
  email: z.string(),
  phone: z.string(),
  recipient_type: recipientTypeSchema,
  has_completed: z.boolean(),
  role: z.string(),
  roles: z.array(z.string()).optional(),
  signing_order: z.number().nullable(),
  contact_id: z.string(),
  shared_link: z.string(),
});

export type PandadocRecipientSchema = z.infer<typeof recipientSchema>;

export const tokenSchema = z.object({
  'Client.Company': z.string().optional().nullable(),
  'Client.FirstName': z.string().optional().nullable(),
  'Client.LastName': z.string().optional().nullable(),
  'Sender.Company': z.string().optional().nullable(),
  'Sender.FirstName': z.string().optional().nullable(),
  'Sender.LastName': z.string().optional().nullable(),
});

export const AssigneeDetailsSchema = z.object({
  type: z.string(),
  id: z.string(),
  first_name: z.string(),
  last_name: z.string(),
  email: z.string(),
  phone: z.string(),
});
export type AssigneeDetails = z.infer<typeof AssigneeDetailsSchema>;

export const ValueSchema = z.unknown();
export type Value = z.infer<typeof ValueSchema>;

export const fieldTypeSchema = z.union([z.literal('signature'), z.literal('text'), z.literal('date'), z.string()]);

export const fieldNames = ['billingUserName', 'billingEmail', 'billingEntityName', 'billingFederalID', 'billingPaymentTerms'] as const;

export type PandadocFieldNames = (typeof fieldNames)[number];

export const fieldSchema = z.object({
  field_id: z.string(),
  uuid: z.string(),
  name: z.string(),
  title: z.string(),
  placeholder: z.string(),
  value: ValueSchema,
  assignee: z.string(),
  assignee_details: AssigneeDetailsSchema,
  type: fieldTypeSchema,
});

export type PandadocFieldSchema = z.infer<typeof fieldSchema>;

export const templateSchema = z.object({
  id: z.string(),
  name: z.string(),
});

export const dataSchema = z.object({
  id: z.string(),
  name: z.string(),
  date_created: z.coerce.date(),
  date_modified: z.coerce.date(),
  expiration_date: z.coerce.date().nullable(),
  autonumbering_sequence_name: z.null(),
  created_by: bySchema,
  metadata: metadataSchema.optional(),
  tokens: z.array(tokenSchema).optional(),
  fields: z.array(fieldSchema).optional(),
  status: z.string(),
  recipients: z.array(recipientSchema),
  sent_by: bySchema.nullable(),
  grand_total: grandTotalSchema.optional(),
  version: z.string(),
  linked_objects: z.array(linkedObjectSchema).optional(),
  template: templateSchema.optional(),
});

export type PandadocContractData = z.infer<typeof dataSchema>;

export const pandadocWebhookEventSchema = z.enum(['document_state_changed', 'document_completed_pdf_ready', 'recipient_completed']);

export const pandadocDocumentWebhookSchema = z.object({
  event: pandadocWebhookEventSchema,
  data: dataSchema,
});

export type PandadocDocumentWebhookPayload = z.infer<typeof pandadocDocumentWebhookSchema>;
