import { z } from 'zod';

import { paginationSchema } from '../../../../lib/schemas/shared';

import { pandadocDocumentWebhookSchema } from './pandadoc';

export * from './pandadoc';

// Pandadoc webhook
export const pandadocWebhookRequestBodyDtoSchema = z.array(pandadocDocumentWebhookSchema);

export const pandadocWebhookResponseDtoSchema = z.object({
  id: z.string(),
});

// Hubspot deal webhook
export const hubspotDealWebhookRequestBodyDtoSchema = z.object({
  id: z.number(),
  roleId: z.string(),
  missionId: z.string(),
  hasBillingForm: z.boolean().nullable(),
});
export type HubspotDealWebhookBody = z.infer<typeof hubspotDealWebhookRequestBodyDtoSchema>;

export const hubspotDealWebhookResponseDtoSchema = z.object({
  dealId: z.number(),
});
export type HubspotDealWebhookResponse = z.infer<typeof hubspotDealWebhookResponseDtoSchema>;

// Fetch contracts
export const getContractsQueryDtoSchema = paginationSchema;

export type GetContractsQueryDto = z.infer<typeof getContractsQueryDtoSchema>;

export const contractTypes = ['MissionAgreement', 'TermsOfService', 'ClientContract', 'ServiceOrder', 'ScopeOfWork', 'MasterServicesAgreement'] as const;
export type ContractType = (typeof contractTypes)[number];

export const contractStatuses = ['Created', 'Completed'] as const;
export type ContractStatus = (typeof contractStatuses)[number];

export const getContractsResponseDtoSchema = z.object({
  count: z.number(),
  data: z.array(
    z.object({
      id: z.string(),
      type: z.enum(contractTypes),
      status: z.enum(contractStatuses),
      documentTitle: z.string().optional(),
      isSigned: z.boolean(),
      signedAt: z.date().optional(),
      isDocumentSent: z.boolean(),
      userCanSign: z.boolean(),
    })
  ),
});

export type GetContractsResponseDto = z.infer<typeof getContractsResponseDtoSchema>;

// Get contract
export const getContractResponseDtoSchema = z.object({
  id: z.string(),
  downloadURL: z.string().optional(),
  signingURL: z.string().optional(),
  documentTitle: z.string().nullish(),
  type: z.enum(contractTypes),
});
export type GetContractResponseDto = z.infer<typeof getContractResponseDtoSchema>;

// Create custom contracts
export const createCustomContractsBodyDtoSchema = z.array(
  z.object({
    fileName: z.string(),
    url: z.string(),
    type: z.enum(contractTypes),
  })
);

export type CreateCustomContractsBodyDto = z.infer<typeof createCustomContractsBodyDtoSchema>;

export const createCustomContractsResponseDtoSchema = z.object({ message: z.string() });

// Update contract

export const updateContractBodySchema = z.object({
  documentTitle: z.string().optional(),
  signedAt: z.coerce.date().optional(),
  type: z.enum(contractTypes).optional(),
});

export type UpdateContractBodyDto = z.infer<typeof updateContractBodySchema>;

export const updateContractResponseSchema = z.object({ id: z.string() });

// Delete contract

export const deleteContractBodySchema = z.object({});

export const deleteContractResponseSchema = z.object({ id: z.string() });

// Agree to terms of service

export const agreeToTermsOfServiceBodySchema = z.object({});

export const agreeToTermsOfServiceResponseSchema = z.object({ message: z.string() });
