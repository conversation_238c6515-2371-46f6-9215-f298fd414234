import { z } from 'zod';

export const createEventRequestSchema = z.object({
  userId: z.string(),
  eventTypeId: z.string(),
  start: z.string(),
  end: z.string(),
  /**
   * This is interview requester's timezone.
   * In case of a client facing interviews, it would be client's Timezone (Discover, Proposal)
   * In case of a builder facing interviews, it would be builder's timezone (Evaluation)
   */
  timezone: z.string(),
  attendee: z.object({
    name: z.string(),
    email: z.string().email(),
    emailFrom: z.string().optional(),
    emailCC: z.string().optional(),
    guests: z.array(z.string()).default([]),
    recordingOptOut: z.boolean().default(false),
  }),
  metadata: z
    .object({
      message: z.string(),
      companyName: z.string().optional(),
      interviewRoleDescription: z.string().optional(),
      roleAndMissionTitle: z.string().optional(),
      proposalId: z.string().optional(),
    })
    .optional(),
});

export const createOrUpdateCalendarEventResponseSchema = z.object({
  id: z.string(),
  calendarEventId: z.string().nullable(),
  eventTypeId: z.string(),
  eventOwnerId: z.string(),
  calendarId: z.string(),
  title: z.string().nullable(),
  description: z.string().nullable(),
  startTime: z.string(),
  endTime: z.string(),
  status: z.enum(['created', 'requested', 'accepted', 'rejected', 'canceled']),
  organizer: z.object({
    id: z.string(),
    name: z.string().optional(),
    email: z.string().email(),
    timezone: z.string(),
    recordingOptOut: z.boolean().default(false),
  }),
  attendees: z.array(
    z.object({
      name: z.string().nullable(),
      email: z.string().email(),
      emailFrom: z.string().nullable(),
      emailCC: z.string().nullable(),
      timezone: z.string().nullable(),
      recordingOptOut: z.boolean().nullable().default(false),
      isGuest: z.boolean().nullable().default(false),
    })
  ),
  metadata: z
    .object({
      message: z.string(),
      companyName: z.string().nullable(),
      interviewRoleDescription: z.string().nullable(),
      roleAndMissionTitle: z.string().nullable(),
      proposalId: z.string().nullable(),
    })
    .nullable(),
  videoCallData: z
    .object({
      id: z.string(),
      type: z.string(),
      url: z.string(),
      // in future we'll might add password protection by adding a simple jwt with expiry
      password: z.string().nullable(),
    })
    .nullable(),
  cancellationReason: z.string().nullable(),
  rescheduleStartTime: z.string().nullable(),
  rescheduleEndTime: z.string().nullable(),
  rescheduleReason: z.string().nullable(),
  rescheduledBy: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type CreateCalendarEventRequestDto = z.infer<typeof createEventRequestSchema>;
export type CreateCalendarEventResponseDto = z.infer<typeof createOrUpdateCalendarEventResponseSchema>;

export type CreateCalendarEventRequest = {
  summary: string;
  description: string | null;
  start: {
    dateTime: string;
    timezone: string;
  };
  end: {
    dateTime: string;
    timezone: string;
  };
  organizer: {
    displayName: string;
    email: string;
  };
  attendees: {
    displayName: string | null;
    email: string;
    organizer: boolean;
  }[];
};

export type WebhookTriggerEvent =
  | 'booking.requested'
  | 'booking.created'
  | 'booking.accepted'
  | 'booking.rescheduled'
  | 'booking.canceled'
  | 'booking.rejected'
  | 'recording.ready';

export type InterviewWebhookPayload = {
  triggerEvent: WebhookTriggerEvent;
  payload: z.infer<typeof createOrUpdateCalendarEventResponseSchema>;
};
