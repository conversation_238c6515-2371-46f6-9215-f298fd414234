import { z } from 'zod';

export const userAvailabilitySlotsSchema = z.object({
  timezone: z.string(),
  availabilities: z.array(
    z.object({
      utcStart: z.string(),
      utcEnd: z.string(),
    })
  ),
});

export const calendarAvailabilitySchema = z.object({
  id: z.string(),
  userId: z.string(),
  eventTypeId: z.string(),
  days: z.array(z.number()).default([]),
  date: z.date().nullable().default(null),
  startTime: z.string(),
  endTime: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const calendarAvailabilitiesSchema = z.array(calendarAvailabilitySchema);

export const createOrUpdateCalendarAvailabilitySchema = z.object({
  days: z.array(z.number()).default([]),
  date: z.string().nullable().default(null),
  startTime: z.string(),
  endTime: z.string(),
});

export type CreateOrUpdateCalendarAvailabilityDto = z.infer<typeof createOrUpdateCalendarAvailabilitySchema>;
