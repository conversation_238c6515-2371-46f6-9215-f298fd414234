import { initContract } from '@ts-rest/core';

import { authHeadersSchema, notFoundSchema, internalServerErrorSchema } from '../../../../lib/schemas/shared';

import { badRequestSchema } from '../../missions';
import { createEventRequestSchema, createOrUpdateCalendarEventResponseSchema } from '../schemas/calendar-events';

const c = initContract();

export const v1CalendarEventsContract = c.router(
  {
    createCalendarEvent: {
      method: 'POST',
      path: '/',
      responses: {
        201: createOrUpdateCalendarEventResponseSchema,
        400: badRequestSchema,
      },
      body: createEventRequestSchema,
      summary: `Create a calendar event for the given user id and event type`,
      strictStatusCodes: true,
    },
    acceptCalendarEvent: {
      method: 'PATCH',
      path: '/:id/accept',
      responses: {
        200: createOrUpdateCalendarEventResponseSchema,
        400: badRequestSchema,
      },
      body: c.noBody(),
      summary: `Accept a calendar event for the given id`,
      strictStatusCodes: true,
    },
    rescheduleCalendarEvent: {
      method: 'PATCH',
      path: '/:id/reschedule',
      responses: {
        200: createOrUpdateCalendarEventResponseSchema,
        400: badRequestSchema,
      },
      body: c.noBody(),
      summary: `Reschedule a calendar event for the given id`,
      strictStatusCodes: true,
    },
    rejectCalendarEvent: {
      method: 'PATCH',
      path: '/:id/reject',
      responses: {
        200: createOrUpdateCalendarEventResponseSchema,
        400: badRequestSchema,
      },
      body: c.noBody(),
      summary: `Reject a calendar event for the given id`,
      strictStatusCodes: true,
    },
    cancelCalendarEvent: {
      method: 'PATCH',
      path: '/:id/cancel',
      responses: {
        200: createOrUpdateCalendarEventResponseSchema,
        400: badRequestSchema,
      },
      body: c.noBody(),
      summary: `Cancel a calendar event for the given id`,
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/interview-scheduling/v1/events',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1CalendarEventsContract;
