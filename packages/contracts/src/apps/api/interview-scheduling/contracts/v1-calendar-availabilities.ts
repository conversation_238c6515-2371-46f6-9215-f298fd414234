import { initContract } from '@ts-rest/core';
import { z } from 'zod';

import { authHeadersSchema, notFoundSchema, internalServerErrorSchema, badRequestSchema } from '../../../../lib/schemas/shared';

import { calendarAvailabilitiesSchema, calendarAvailabilitySchema, createOrUpdateCalendarAvailabilitySchema, userAvailabilitySlotsSchema } from '../schemas';

const c = initContract();

export const v1CalendarAvailabilitiesContract = c.router(
  {
    getUserAvailabilitySlots: {
      method: 'GET',
      path: '/:userId/:eventTypeId/free-slots',
      responses: {
        200: userAvailabilitySlotsSchema,
      },
      query: z.object({
        utcStartTimeString: z.string(),
        utcEndTimeString: z.string(),
      }),
      summary: `Get user's calendar availability slots for the given user id and event type within the range`,
      strictStatusCodes: true,
    },
    createCalendarAvailability: {
      method: 'POST',
      path: '/:userId/:eventTypeId',
      responses: {
        201: calendarAvailabilitySchema,
        400: badRequestSchema,
      },
      body: createOrUpdateCalendarAvailabilitySchema,
      summary: `Create user's calendar availability preference for the given user id and event type`,
      strictStatusCodes: true,
    },
    getCalendarAvailability: {
      method: 'GET',
      path: '/:id',
      responses: {
        200: calendarAvailabilitySchema,
      },
      summary: `Get user's calendar availability preference for the given id`,
      strictStatusCodes: true,
    },
    getCalendarAvailabilityByUserAndEventTypeId: {
      method: 'GET',
      path: '/:userId/:eventTypeId',
      responses: {
        200: calendarAvailabilitiesSchema,
      },
      summary: `Get user's calendar availability preferences for the given user and event type id`,
      strictStatusCodes: true,
    },
    updateCalendarAvailability: {
      method: 'PUT',
      path: '/:id',
      responses: {
        204: c.noBody(),
        400: badRequestSchema,
      },
      body: createOrUpdateCalendarAvailabilitySchema,
      summary: `Update user's calendar availability preference for the given id`,
      strictStatusCodes: true,
    },
    deleteCalendarAvailability: {
      method: 'DELETE',
      path: '/:id',
      responses: {
        204: c.noBody(),
        400: badRequestSchema,
      },
      body: c.noBody(),
      summary: `Delete user's calendar availability preference for the given id`,
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/interview-scheduling/v1/availabilities',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1CalendarAvailabilitiesContract;
