import { initContract } from '@ts-rest/core';
import { z } from 'zod';

import { authHeadersSchema, notFoundSchema, internalServerErrorSchema } from '../../../../lib/schemas/shared';

import { unauthorizedSchema } from '../../auth';
import { badRequestSchema } from '../../missions';
import { calendarStatusSchema, googleAuthInitiateResponseSchema } from '../schemas/calendars';

const c = initContract();

export const v1CalendarsContract = c.router(
  {
    initiateGoogleAuth: {
      method: 'POST',
      path: '/auth/google/initiate',
      responses: {
        200: googleAuthInitiateResponseSchema,
      },
      body: c.noBody(),
      summary: 'Initiate Google Calendar authentication',
      strictStatusCodes: true,
    },
    googleAuthCallback: {
      method: 'GET',
      path: '/auth/google/callback',
      query: z.object({
        code: z.string(),
        state: z.string(),
      }),
      responses: {
        200: c.noBody(),
        400: badRequestSchema,
        401: unauthorizedSchema,
      },
      summary: 'Handle Google Calendar authentication callback',
      strictStatusCodes: true,
    },
    getStatus: {
      method: 'GET',
      path: '/status',
      responses: {
        200: calendarStatusSchema,
        404: notFoundSchema,
      },
      summary: 'Get calendar connection status',
      strictStatusCodes: true,
    },
    disconnect: {
      method: 'PATCH',
      path: '/disconnect',
      responses: {
        200: calendarStatusSchema,
        404: notFoundSchema,
      },
      body: c.noBody(),
      summary: 'Disconnect calendar',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/interview-scheduling/v1/calendars',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1CalendarsContract;
