import { z } from 'zod';

// Get account team advisor

export const getAccountTeamAdvisorByAccountIdParamsSchema = z.object({
  accountId: z.string(),
});

// Get account team collaborators

export const getAccountCollaboratorsByAccountIdParamsSchema = z.object({
  accountId: z.string(),
});

// Invite mission collaborator

export const inviteMissionCollaboratorParamsSchema = z.object({
  missionId: z.string(),
});

export const inviteMissionCollaboratorBodySchema = z.object({
  fullName: z.string(),
  email: z.string(),
  role: z.enum(['missionadmin', 'missionmember']),
  skipEmail: z.boolean(),
});

export type InviteMissionCollaboratorBody = z.infer<typeof inviteMissionCollaboratorBodySchema>;

export const inviteMissionCollaboratorResponseSchema = z.object({
  message: z.string(),
});
