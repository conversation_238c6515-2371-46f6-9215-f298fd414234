import { z } from 'zod';

export const accountAccessLevels = ['bdAdmin', 'bdManager', 'clientAdmin'] as const;

export type AccountAccessLevel = (typeof accountAccessLevels)[number];

export const collaboratorSchema = z.object({
  id: z.string(),
  email: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  pictureURL: z.string().nullable(),
  accessLevel: z.enum(accountAccessLevels),
  username: z.string(),
});

export type Collaborator = z.infer<typeof collaboratorSchema>;

// Get account team advisor

export const getAccountTeamAdvisorParamsSchema = z.object({
  accountId: z.string(),
});

export const getAccountTeamAdvisorSchema = collaboratorSchema;

export type GetAccountTeamAdvisor = z.infer<typeof getAccountTeamAdvisorSchema>;

// Get account team collaborators

export const getAccountCollaboratorsParamsSchema = z.object({
  accountId: z.string(),
});

export const getAccountCollaboratorsSchema = z.array(collaboratorSchema);

export type GetAccountCollaborators = z.infer<typeof getAccountCollaboratorsSchema>;
