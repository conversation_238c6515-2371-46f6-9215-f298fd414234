import { initContract } from '@ts-rest/core';

import { accountAuthHeadersSchema, internalServerErrorSchema, notFoundSchema } from '../../../../lib/schemas/shared';

import {
  getAccountTeamAdvisorSchema,
  getAccountCollaboratorsSchema,
  getAccountTeamAdvisorByAccountIdParamsSchema,
  getAccountCollaboratorsByAccountIdParamsSchema,
  inviteMissionCollaboratorBodySchema,
  inviteMissionCollaboratorParamsSchema,
  inviteMissionCollaboratorResponseSchema,
} from '../schemas';

const c = initContract();

export const v1AdminAccountsContract = c.router(
  {
    getAccountTeamAdvisorByAccountId: {
      method: 'GET',
      path: '/team-advisor/:accountId',
      pathParams: getAccountTeamAdvisorByAccountIdParamsSchema,
      responses: {
        200: getAccountTeamAdvisorSchema,
      },
      summary: 'Gets the account team advisor',
      strictStatusCodes: true,
    },
    getAccountCollaboratorsByAccountId: {
      method: 'GET',
      path: '/collaborators/:accountId',
      pathParams: getAccountCollaboratorsByAccountIdParamsSchema,
      responses: {
        200: getAccountCollaboratorsSchema,
      },
      summary: 'Gets the account collaborators',
      strictStatusCodes: true,
    },
    inviteMissionCollaborator: {
      method: 'POST',
      path: '/invite-mission-collaborator/:missionId',
      pathParams: inviteMissionCollaboratorParamsSchema,
      body: inviteMissionCollaboratorBodySchema,
      responses: {
        200: inviteMissionCollaboratorResponseSchema,
      },
      summary: 'Invites a mission collaborator',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/admin-accounts/v1',
    baseHeaders: accountAuthHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1AdminAccountsContract;
