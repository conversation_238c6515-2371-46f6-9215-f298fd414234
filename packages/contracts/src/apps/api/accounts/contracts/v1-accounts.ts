import { initContract } from '@ts-rest/core';

import { authHeadersSchema, internalServerErrorSchema, notFoundSchema } from '../../../../lib/schemas/shared';

import {
  getAccountTeamAdvisorSchema,
  getAccountCollaboratorsSchema,
  getAccountTeamAdvisorParamsSchema,
  getAccountCollaboratorsParamsSchema,
} from '../schemas/accounts';

const c = initContract();

export const v1AccountsContract = c.router(
  {
    getAccountTeamAdvisor: {
      method: 'GET',
      path: '/team-advisor/:accountId',
      pathParams: getAccountTeamAdvisorParamsSchema,
      responses: {
        200: getAccountTeamAdvisorSchema,
      },
      summary: "Get the authenticated user's account team advisor",
      strictStatusCodes: true,
    },
    getAccountCollaborators: {
      method: 'GET',
      path: '/collaborators/:accountId',
      pathParams: getAccountCollaboratorsParamsSchema,
      responses: {
        200: getAccountCollaboratorsSchema,
      },
      summary: 'Get the account collaborators',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/accounts/v1',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
      500: internalServerErrorSchema,
    },
  }
);

export default v1AccountsContract;
