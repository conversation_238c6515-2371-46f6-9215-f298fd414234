import { initContract } from '@ts-rest/core';

import { authHeadersSchema, notFoundSchema, internalServerErrorSchema, badRequestSchema } from '../../../../lib/schemas/shared';

import { authenticatedUserSchema, signInResponseSchema, signInWithPasswordSchema, unauthorizedSchema } from '../schemas';

const c = initContract();

export const v1AuthContract = c.router(
  {
    getMe: {
      method: 'GET',
      path: '/me',
      responses: {
        200: authenticatedUserSchema,
        401: unauthorizedSchema,
        404: notFoundSchema,
        500: internalServerErrorSchema,
      },
      summary: "Returns the authenticated user's profile",
      strictStatusCodes: true,
    },
    signInWithPassword: {
      method: 'POST',
      path: '/sign-in',
      body: signInWithPasswordSchema,
      responses: {
        200: signInResponseSchema,
        400: badRequestSchema,
        401: unauthorizedSchema,
        404: notFoundSchema,
        500: internalServerErrorSchema,
      },
      summary: 'Sign in with password',
      strictStatusCodes: true,
    },
  },
  {
    pathPrefix: '/auth/v1',
    baseHeaders: authHeadersSchema,
    commonResponses: {
      404: notFoundSchema,
    },
  }
);

export default v1AuthContract;
