import { z } from 'zod';

export const signInWithPasswordSchema = z.object({
  email: z.string(),
  password: z.string(),
});

export type SignInWithPasswordSchemaDto = z.infer<typeof signInWithPasswordSchema>;

export const authenticatedUserSchema = z.object({
  id: z.string(),
  email: z.string(),
  isAdmin: z.boolean(),
  firstName: z.string().nullable(),
  lastName: z.string().nullable(),
  fullName: z.string().nullable(),
});

export type AuthenticatedUser = z.infer<typeof authenticatedUserSchema>;

export const signInResponseSchema = z.object({
  user: authenticatedUserSchema,
  token: z.string(),
  refreshToken: z.string(),
});

export type SignInResponse = z.infer<typeof signInResponseSchema>;

/** Used to return a message when user auth errors */
export const unauthorizedSchema = z.object({
  message: z.string(),
});
