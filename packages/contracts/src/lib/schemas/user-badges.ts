import { z } from 'zod';

export const USER_BADGES = [
  'SelectionTeam',
  'BeenOnMission',
  'ExceptionalATeamer',
  'ATeamerResidence',
  'ATeamer',
  'VettingScheduled',
  'VettingInterviewDate',
  'HighPotential',
  'Unvetted',
  'NotScrubbed',
  'UnqualifiedUser',
  'LimitedAccess',
] as const;

export const userBadgesSchema = z.enum(USER_BADGES);

export type UserBadge = z.infer<typeof userBadgesSchema>;
