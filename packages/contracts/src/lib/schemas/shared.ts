import { z } from 'zod';

export const authHeadersSchema = z.object({ authorization: z.string().optional() });

export const accountAuthHeadersSchema = authHeadersSchema.extend({
  'x-account-token': z.string().optional(),
});

export const apiKeyAuthHeadersSchema = z.object({
  'x-api-key': z.string(),
});

export const notFoundSchema = z.object({
  message: z.string(),
});

export const badRequestSchema = z.object({
  message: z.string(),
});

export const internalServerErrorSchema = z.object({
  message: z.string(),
});

// pagination
export const paginationSchema = z.object({
  page: z.preprocess(Number, z.number().int().min(0)),
  pageSize: z.preprocess(Number, z.number().int().min(1).max(100)),
});

export type Pagination = z.infer<typeof paginationSchema>;
