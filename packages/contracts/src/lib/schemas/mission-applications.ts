import { z } from 'zod';

export const MISSION_APPLICATION_STAGES = [
  'Accepted',
  'Rejected',
  'NotAvailable',
  'LowCompetitiveness',
  'OpportunityToUpdate',
  'ProposedButNotShared',
  'Proposed',
  'ShortlistGood',
  'ShortlistStrong',
  'New',
  'Interviewing',
  'VettingScheduled',
  'Exclusive',
  'OnHold',
] as const;

export const missionApplicationStagesSchema = z.enum(MISSION_APPLICATION_STAGES);

export type MissionApplicationStage = z.infer<typeof missionApplicationStagesSchema>;
