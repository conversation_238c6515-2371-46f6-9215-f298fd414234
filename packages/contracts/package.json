{"name": "@packages/contracts", "version": "0.0.0", "private": true, "scripts": {"dev": "pnpm build --watch", "build": "tsc && tsc --module ESNext --outDir dist/esm", "lint": "eslint \"**/*.ts\"", "type-check": "tsc --noEmit", "test": ""}, "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "publishConfig": {"access": "public"}, "dependencies": {"@packages/eslint-config": "workspace:*", "@ts-rest/core": "3.51.0", "zod": "3.23.8"}, "devDependencies": {"@types/node": "^20.3.1", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "typescript": "^5.1.3", "typescript-eslint": "^8.2.0"}}