# Core platform

- Repository to hold the core platform services used here at A.Team.
- All services are defined in the `apps` directory.
- All internal libraries are defined in the `packages` directory.

# Running the services

- This section outlines everything needed to have the services up and running locally.

### System dependencies

1. Mandatory: pnpm installed in your local machine (`brew install pnpm` for MacOS with Homebrew)
2. Mandatory: node v20.0.0 or higher (currently using `v20.16.0`)
3. Optional: nvm to switch node versions more easily (`nvm use`)

### Working with local .env files

- To decrypt all local `.env` files, run the following command:

```
sh .scripts/decrypt.sh local
```

- To encrypt a specific local `.env` file (in case of updates), run the following command:

```
sh .scripts/encrypt.sh local <app>
```

### Working with service .env files

- To decrypt specific service `.env` files, run the following command:

```
sh .scripts/decrypt.sh <app>
```

- To encrypt a specific service `.env` file (in case of updates), run the following command:

```
sh .scripts/encrypt.sh <app> <dev|prod>
```

- When making changes for service/app config secrets (manifest files in kubernetes), those need to be `base64` encoded for kubernetes to understand them.

### Quick start

1. Install system dependencies (above)
2. Install repo dependencies `pnpm install`
3. Decrypt the environment variables `sh .scripts/decrypt.sh local`
4. Run the monorepo services `pnpm dev`

### Working with vscode

- Add the following `settings.json` file in `.vscode` folder:

```
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit"
  },
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "jest.runMode": "on-demand",
  "jest.virtualFolders": [{ "name": "api", "rootPath": "apps/api" }]
}
```

- Recommended extensions:
  - Add jsdoc comments
  - Tailwind
  - Prettier
  - Eslint
  - Jest

# Deployment versioning

- This section outlines everything needed to know to define the release cutting version.

### Rules

- Given a version pattern `MAJOR.MINOR.PATCH`, incrementing and releasing should be done as:
  - `MAJOR` version must be increased when incompatible service/code changes are made,
  - `MINOR` version must be increased when functionality in a backwards-compatible manner is made,
  - `PATCH` version must be increased when backwards-compatible bug fixes (for existing features) are made.

# Deploying the services

- This section outlines everything needed to deploy new versions of services defined in this repository.

### Dev deployment

- Pull the latest changes on the `main` branch and push the following tag to the repository:

  ```bash
  git tag v<major>.<minor>.<patch>-dev-1 # matches v0.0.1-dev-N
  git push origin tag v<major>.<minor>.<patch>-dev-1 # runs the dev cd process
  ```

- This tag creates a new deployment towards the `dev` envoirnment, taking the commit hash/image from the `main` branch (release cutting).

### Production deployment

- Pull the latest changes on the `main` branch and push the following tag to the repository:

  ```bash
  git tag v<major>.<minor>.<patch> # matches v0.0.1
  git push origin tag v<major>.<minor>.<patch> # runs the prod cd process
  ```

- This tag creates a new deployment towards the `production` envoirnment, taking the commit hash/image from the `main` branch (release cutting),
- A `release` on the `github` repository is generated,
- A slack notification is sent to the required channel with the release changes.
