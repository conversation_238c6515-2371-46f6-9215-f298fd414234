---
description: Master ruleset for AI Docs (.mdc) files—defines mandatory structure, metadata, and formatting so multiple AI tools (Cursor, Claude-Code, Windsurf, OpenAI, etc.) can reliably parse and extend docs.
globs: **/*.mdc
alwaysApply: false
---
# AI Docs Ruleset

@context {
  "type": "ruleset",
  "format_version": "1.0.1",
  "tool_compatibility": ["cursor", "anthropic/claude-code", "windsurf", "openai"],
  "purpose": "Ensure all .mdc docs share a predictable schema so AI assistants generate boiler-plate-free, convention-compliant code.",
  "ai_processing_hints": {
    "validation_priority": "Enforce all rules with 'severity: error' strictly. Report 'severity: warning' violations. Log 'severity: info' for informational checks or recommendations.",
    "generation_default_source": "When generating new MDC files, use the template in §1.1 as a starting point. Populate fields based on rules in §1.1 and examples in §6.",
    "conflict_resolution_preference": "If rules within this document appear to conflict, rules with a more restrictive 'severity' (error > warning > info) take precedence. If ambiguity persists, seek clarification.",
    "annotation_json_validation": "For all @annotations, the provided JSON content must be syntactically valid. For common annotation types listed in §1.2 ('common_types'), their structure should generally follow the examples in §6.2 unless a more specific @structure rule applies to the current document type."
  }
}

---

## 1 • Document Anatomy
@structure {
  "required_sections": [
    "frontmatter",
    "title",
    "context",
    "content_sections",
    "document_meta_block"
  ],
  "optional_sections": [
    "examples",
    "implementations"
  ],
  "recommended_sections": [
    "practical_examples",
    "common_patterns",
    "type_definitions",
    "changelog"
  ]
}

### 1.1 Frontmatter
@frontmatter_rules [
  { "id": "position",        "rule": "Must be the first block in file (between --- delimiters).", "severity": "error" },
  { "id": "description",     "rule": "Single-sentence summary for the document. Critical for AI understanding and used by AI tools.", "severity": "error" },
  { "id": "globs",           "rule": "An array of string file-glob patterns. Used by Cursor and other AI tools for AutoAttached rules and by AI for scoping document relevance.", "severity": "error" },
  { "id": "alwaysApply",     "rule": "Optional boolean. If true, Cursor always applies this rule. This field is primarily for Cursor's direct processing of .mdc rule files. For general MDC documents, conceptual rule application type is defined in @context.rule_type (see §1.4).", "severity": "info" }
]

> **YAML Frontmatter Template**
> ```yaml
> ---
> description: Concise purpose statement for the document.
> globs: ["relevant/path/**/*.ts", "another/path/*.js"]
> alwaysApply: false # Or true, if this MDC file is a rule intended for Cursor to always apply.
> ---
> ```

### 1.1.1 Document Metadata Block (@document_meta)
This annotation block is REQUIRED and should be placed at the very end of every MDC file (just before any final `---` if present, or as the absolute last content). It houses essential metadata not included in the Cursor-compatible YAML frontmatter.

@document_meta_rules [
  { "id": "position",         "rule": "Must be the last significant content block in the file.", "severity": "error" },
  { "id": "block_name",       "rule": "The annotation block must be exactly named '@document_meta'.", "severity": "error" },
  { "id": "content_type",     "rule": "Must be a valid JSON object.", "severity": "error" },
  { 
    "id": "version",          
    "rule": "Semantic version of this specific MDC document (e.g., 1.0.0). Indicates changes to this document's content/rules.", 
    "schema": { "type": "string", "pattern": "^[0-9]+\\.[0-9]+\\.[0-9]+$" }, 
    "severity": "warning" 
  },
  { 
    "id": "author",           
    "rule": "GitHub handle or name of the primary author or maintainer of this document.", 
    "schema": { "type": "string" }, 
    "severity": "info" 
  },
  { 
    "id": "last_updated",     
    "rule": "Date of the last significant update to this document's content (YYYY-MM-DD).", 
    "schema": { "type": "string", "pattern": "^[0-9]{4}-[0-9]{2}-[0-9]{2}$" }, 
    "severity": "warning" 
  },
  { 
    "id": "keywords",         
    "rule": "An array of descriptive string keywords (3-7 recommended) for discoverability.", 
    "schema": { "type": "array", "items": { "type": "string" }, "minItems": 3, "maxItems": 7 }, 
    "severity": "info" 
  },
  { 
    "id": "related_docs",     
    "rule": "Optional array of paths to other relevant MDC files or documentation. Referenced files should exist.", 
    "schema": { "type": "array", "items": { "type": "string" } }, 
    "severity": "info" 
  },
  { 
    "id": "file_validation_meta", 
    "rule": "File paths provided in 'related_docs' within @document_meta should exist in the workspace.", 
    "severity": "warning" 
  }
]

> **@document_meta JSON Block Template**
> ```json
> @document_meta {
>   "version": "1.0.0",
>   "author": "githubhandle",
>   "last_updated": "YYYY-MM-DD",
>   "keywords": ["keyword1", "keyword2", "keyword3"],
>   "related_docs": ["path/to/another-doc.md"] 
> }
> ```

### 1.2 Annotations
@annotations {
  "syntax": "@name <JSON>",
  "valid_json": true,
  "placement": "Above described section",
  "common_types": {
    "context": "Defines the project, document, or section context (e.g., type, purpose, version).",
    "rules": "Specifies a list of rules, requirements, or validation criteria.",
    "options": "Lists available options, configurations, or parameters.",
    "examples": "Provides illustrative code or usage examples.",
    "implementations": "Shows detailed implementation snippets or references.",
    "related": "Links to related documentation, code sections, or external resources.",
    "structure": "Defines the expected structure of a document or section (e.g., required/optional sections).",
    "validation": "Outlines validation criteria or checks to be performed."
  },
  "lint": "CI (or AI Tool) validates JSON + keys",
  { "id": "file_validation", "rule": "Referenced paths in rule content (e.g. @filename.ext) must exist", "severity": "error" }
}

### 1.3 Content Sections
@content_rules {
  "headings": { "h1": "Exactly one","h2": "Major sections","h3": "Sub-sections","h4": "Details" },
  "code_blocks": {
    "language_specification": "Always specify language (e.g., ```yaml or ```typescript)",
    "max_line_width": "≤120 characters for readability",
    "commenting_style": "Prefer inline comments for direct association with code; use block comments for longer explanations only if necessary",
    "include_examples": "When appropriate, include practical, runnable examples",
    "provide_context": "Add explanatory comments or surrounding text to clarify the purpose/use of the code block"
  },
  "implementation_blocks": {
    "logical_grouping": "Group related snippets or implementation details together",
    "inline_documentation": "Include explanatory comments within the code for clarity",
    "type_information": "Specify type information where relevant (e.g., for function signatures, variable declarations)",
    "validation_rules": "Show guards, assertions, or validation logic if applicable to the implementation"
  }
}

#### 1.3.1 Referencing Files in Rule Content
@file_referencing_in_content {
  "syntax": "@filename.ext",
  "purpose": "Embeds the content of the specified file directly into the rule context when the rule is applied.",
  "validation": "Ensure the referenced file exists at the specified path relative to the rule file or project root.",
  "example": "`@service-template.ts` would include the content of 'service-template.ts' when the rule is processed."
}

### 1.4 Rule Types (Cursor-compatible)
@rule_types [
  { "id": "Always",         "description": "Always included in context. Corresponds to Cursor's 'alwaysApply: true' if directly used by Cursor." },
  { "id": "AutoAttached",   "description": "Auto when glob matches. `globs` frontmatter field must be populated." },
  { "id": "AgentRequested", "description": "AI may pull in when helpful. Frontmatter `description` is crucial." },
  { "id": "Manual",         "description": "Only via explicit invocation (e.g., @ruleName)." }
]
*Set `rule_type` in @context.*

---

## 2 • Best Practices
@best_practices {
  "document_lifecycle": {
    "human_review": "Every .mdc change needs engineer approval (see Review Workflow §4)",
    "versioning": "Use semantic versioning. Minor = non-breaking, Major = breaking changes to the ruleset itself.",
    "changelogs": "Add a ## Changelog section for notable edits to keep users informed."
  },
  "content_quality": {
    "clarity_and_conciseness": "Write rules the way you would write a clear internal doc. Avoid ambiguity that triggers AI hallucination.",
    "size": "Keep files under ≈500 lines for better manageability and AI processing.",
    "composability": "Split large concepts into multiple, focused, and composable rules.",
    "examples": "Prefer runnable, self-contained code examples where applicable."
  },
  "mdc_specifics": {
    "annotation_placement": "Place annotations immediately before the content they describe.",
    "semantic_annotation_names": "Use clear and semantic names for custom fields within JSON annotations.",
    "context_scope": "Ensure the @context annotation provides a clear scope and purpose for the document."
  }
}

---

## 3 • Validation Matrix
@validation {
  "must": [
    "A Cursor-Compatible YAML Frontmatter (--- block) must be the first block in the file and include all fields marked with `severity: error` in @frontmatter_rules (§1.1) (e.g., `description`, `globs`).",
    "A @document_meta JSON annotation block must be the last significant content block and include all fields marked with `severity: error` in @document_meta_rules (§1.1.1).",
    "A single H1 title must be present as per @content_rules (§1.3).",
    "JSON content within all @annotations (including @document_meta) must be syntactically valid (see §1.2).",
    "At least one H2 section (major content section) must be present.",
    "All file paths referenced in rule content (e.g. `@filename.ext`) must exist, as per the `file_validation` rule in @annotations (§1.2).",
    "File paths in `@document_meta.related_docs` should exist, as per `file_validation_meta` rule in @document_meta_rules (§1.1.1)."
  ],
  "should": [
    "The @document_meta block (§1.1.1) should include `version` and `last_updated` fields as specified in its rules.",
    "Code examples should be runnable and self-contained where applicable (see @best_practices §2).",
    "Lines in code blocks should ideally not exceed 120 characters (see @content_rules §1.3).",
    "Documents should aim to include practical examples and common patterns as recommended in @structure (§1)."
  ],
  "consider": [
    "Reviewing optional fields in both YAML frontmatter (e.g. `alwaysApply` in §1.1) and @document_meta (e.g., `author`, `keywords`, `related_docs` in §1.1.1) for completeness.",
    "Adhering to all guidelines in @best_practices (§2) for optimal document quality and AI utility."
  ]
}

---

## 4 • Review Workflow
@review_process {
  "step_1": "Open PR with new/updated .mdc",
  "step_2": "CI linter or AI tool checks structure & JSON",
  "step_3": "Human reviewer verifies accuracy",
  "step_4": "Upon approvals, .mdc file gets merged into repo in /ai-docs"
}

---

## 5 • Quick Reference
| Task | Location |
|------|----------|
| Frontmatter template | §1.1 |
| Rule types           | §1.4 |
| Annotation syntax    | §1.2 |
| Validation checklist | §3   |
| Review workflow      | §4   |

---

## 6 • Examples & Common Pitfalls

### 6.1 Minimal Valid MDC File
This example shows the bare minimum required to create a valid `.mdc` file according to this ruleset, including the Cursor-compatible YAML frontmatter and the required `@document_meta` block at the end.

```markdown
---
description: A minimal example MDC file.
globs: ["**/*.example.mdc"]
alwaysApply: false
---

# Minimal Example Rule Title

@context {
  "type": "example",
  "purpose": "Demonstrate a minimal valid MDC file structure with split metadata.",
  "rule_type": "Manual"
}

## Section One: Basic Content

This section contains minimal content for demonstration.

@document_meta {
  "version": "0.1.0",
  "author": "exampleuser",
  "last_updated": "YYYY-MM-DD",
  "keywords": ["minimal", "example", "mdc"]
}
```

### 6.2 Common Annotation Block Examples

#### Context Annotation
```markdown
@context {
  "type": "ruleset", // E.g., ruleset, documentation, guidelines, implementation_guide
  "format_version": "1.0.1", // Version of the MDC schema itself
  "tool_compatibility": ["cursor", "claude", "openai"], // Target AI tools
  "purpose": "A clear statement about what this MDC file aims to achieve.",
  "scope": "project-wide" // Optional: e.g., project-wide, module-specific, frontend-only
  "rule_type": "Always" // Optional: Defines how this rule is applied (Always, AutoAttached, AgentRequested, Manual)
}
```

#### Rules Annotation
```markdown
@rules [
  {
    "id": "FR001", // Unique identifier for the rule
    "severity": "error", // error | warning | info
    "description": "All API endpoints must implement request validation using Zod.",
    "rationale": "Ensures type safety and data integrity at the API boundary.", // Optional: Why this rule exists
    "example_compliant": "See /examples/compliant-endpoint.ts", // Optional: Link to compliant code
    "example_non_compliant": "See /examples/non-compliant-endpoint.ts" // Optional: Link to non-compliant code
  },
  {
    "id": "STYLE002",
    "severity": "warning",
    "description": "Prefer arrow functions for React component definitions."
  }
]
```

#### Structure Annotation
```markdown
@structure {
  "required_sections": ["frontmatter", "title", "introduction", "api_reference", "error_handling"],
  "optional_sections": ["changelog", "examples"],
  "section_order": ["frontmatter", "title", "introduction", "api_reference", "error_handling", "examples", "changelog"],
  "enforce_order_severity": "warning"
}
```

### 6.3 Common Mistakes to Avoid

@mistakes [
  {
    "id": "missing_or_malformed_frontmatter",
    "summary": "Frontmatter is missing, incomplete, or not valid YAML.",
    "details": "The frontmatter block (between --- lines) must be at the very beginning and contain all required fields like 'description', 'globs', 'version', and 'last_updated'.",
    "severity_impact": "High (Prevents correct parsing and rule application)",
    "how_to_fix": "Ensure a valid YAML frontmatter block is the first content in the file with all mandatory fields populated correctly."
  },
  {
    "id": "invalid_json_in_annotations",
    "summary": "JSON content within annotations (e.g., @context {...}, @rules [...]) is not well-formed.",
    "details": "JSON requires double quotes for keys and string values. Trailing commas are not allowed. Ensure all brackets and braces are correctly matched.",
    "severity_impact": "High (Prevents parsing of the specific annotation and its rules)",
    "how_to_fix": "Validate JSON using a linter or online validator. Pay close attention to quotes and commas."
  },
  {
    "id": "incorrect_heading_structure",
    "summary": "Headings do not follow the prescribed H1 (single), H2 (major sections), H3 (sub-sections) hierarchy.",
    "details": "There should be exactly one H1 title. Major logical sections should use H2. Sub-points of H2 sections should use H3, and so on.",
    "severity_impact": "Medium (Affects document readability and potentially AI interpretation of structure)",
    "how_to_fix": "Review document outline and adjust heading levels to conform to the H1 -> H2 -> H3 hierarchy."
  },
  {
    "id": "nonexistent_file_references",
    "summary": "Paths referenced in 'related_docs' (frontmatter) or via @filename.ext syntax in content do not exist.",
    "details": "All file paths provided must be valid and point to existing files within the workspace/project.",
    "severity_impact": "Medium to High (Can lead to broken links or incomplete context for AI)",
    "how_to_fix": "Verify all referenced file paths. Ensure files are committed and correctly named."
  },
  {
    "id": "missing_required_sections",
    "summary": "A document is missing one of the sections declared as 'required_sections' in @structure.",
    "details": "If @structure defines certain sections as required, they must be present in the MDC file.",
    "severity_impact": "Medium (Document may be considered incomplete or invalid by processing tools)",
    "how_to_fix": "Add the missing sections with appropriate content as defined by the @structure annotation."
  }
]

---

## 7 • Changelog

### v0.0.1 (2025-05-21)
- Initial comprehensive definition of the AI Docs Ruleset.

---

@document_meta {
  "version": "0.0.1",
  "author": "jimcamut",
  "last_updated": "2025-05-21",
  "keywords": ["ai-docs","mdc","cursor-rule","documentation","ruleset","schema"],
}
