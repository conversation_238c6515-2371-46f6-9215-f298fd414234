#!/bin/bash

if [ $# -eq 0 ]; then
    echo "Usage: $0 <local|app_name>"
    exit 1
fi

mode="$1"

if [ "$mode" == "local" ]; then
    files=(apps/**/enc.env)
else
    if [ ! -d ".kube/$mode" ]; then
        echo "App '$mode' not found in .kube/"
        exit 1
    fi

    files=($(find .kube/"$mode"/overlays/dev .kube/"$mode"/overlays/prod -type f \( -iname "*config*.env" -o -iname "*config*.yaml" \) ! -iname "*dec.env" ! -iname "*dec.yaml"))
fi

for ((i = 0; i < ${#files[@]}; i++)); do
    file="${files[i]}"

    if [ -f "$file" ]; then
        if [ "$mode" == "local" ]; then
            output="${file/enc.env/.env}"
        else
            if [[ "$file" == *.env ]]; then
                output="${file/.env/.dec.env}"
            elif [[ "$file" == *.yaml ]]; then
                output="${file/.yaml/.dec.yaml}"
            else
                continue
            fi
        fi
        
        echo "Decrypting $file to $output"
        sops --decrypt "$file" > "$output"

        if [ $? -eq 0 ]; then
            echo "Successfully decrypted $file to $output"
        else
            echo "Failed to decrypt $file"
        fi

        if [ $i -lt $((${#files[@]} - 1)) ]; then
            echo
        fi
    fi
done