#!/bin/bash

if [ -z "$1" ]; then
    echo "Usage: ./encrypt.sh <local|app> [<app_name|dev|prod>]"
    exit 1
fi

MODE=$1

if [ "$MODE" = "local" ]; then
    if [ -z "$2" ]; then
        echo "Usage: ./encrypt.sh local <app_name>"
        exit 1
    fi

    APP_NAME=$2
    APP_DIR="apps/$APP_NAME"

    if [ ! -d "$APP_DIR" ]; then
        echo "Error: Folder for app '$APP_NAME' not found in 'apps/' directory."
        exit 1
    fi

    ENV_FILE="$APP_DIR/.env"

    if [ -f "$ENV_FILE" ]; then
        echo "Encrypting $ENV_FILE to $APP_DIR/enc.env..."
        sops --encrypt "$ENV_FILE" > "$APP_DIR/enc.env"

        if [ $? -eq 0 ]; then
            echo "Encryption successful! Encrypted file saved as $APP_DIR/enc.env."
        else
            echo "Encryption failed for $ENV_FILE. Please check your sops configuration."
        fi
    else
        echo "No .env file found in $APP_DIR."
    fi
else
    if [ -z "$2" ]; then
        echo "Usage: ./encrypt.sh <app_name> <dev|prod>"
        exit 1
    fi

    APP_NAME=$1
    ENV_TYPE=$2
    APP_DIR=".kube/$APP_NAME"

    if [ ! -d "$APP_DIR" ]; then
        echo "Error: Folder for app '$APP_NAME' not found in '.kube/' directory."
        exit 1
    fi

    DEC_FILES=($(find "$APP_DIR/overlays/$ENV_TYPE" -type f \( -iname "*config*.dec.env" -o -iname "*config*.dec.yaml" \)))

    if [ $? -ne 0 ]; then
        echo "Error finding files in '$APP_DIR'."
        exit 1
    fi

    if [ ${#DEC_FILES[@]} -eq 0 ]; then
        echo "No files found to encrypt for '$APP_NAME' with type '$ENV_TYPE'."
        exit 1
    fi

    for file in "${DEC_FILES[@]}"; do
        if [[ "$file" == *.dec.env ]]; then
            output="${file/.dec.env/.env}"
        elif [[ "$file" == *.dec.yaml ]]; then
            output="${file/.dec.yaml/.yaml}"
        fi

        echo "Encrypting $file to $output..."
        sops --encrypt "$file" > "$output"

        if [ $? -eq 0 ]; then
            echo "Successfully encrypted $file to $output."
        else
            echo "Failed to encrypt $file."
        fi
    done
fi
