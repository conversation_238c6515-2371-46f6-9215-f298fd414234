#!/bin/bash

# List of collections to exclude from dumping, 
# Feel free to change as needed
EXCLUDED_COLLECTIONS=(
    "emailAnalyticEvents"
    "builderLinkedInData"
    "targeterQueries"
    "aiJobs"
    "profileEnhancementSuggestion"
    "team-formations"
    "teamengine-stale-indices"
    "profileEnhancementSuggestions"
    "userSearches"
    "profile-index"
    "user-mission-notification-logs"
    "analyticsData"
    "experiences_old"
    "builderLoginHistory"
    "team-pulse-surveys"
)

check_mongodb_tools() {
    if ! command -v mongodump &> /dev/null || ! command -v mongorestore &> /dev/null; then
        echo "MongoDB Database Tools are not installed."
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            echo "On macOS, install using Homebrew:"
            echo "  brew install mongodb/brew/mongodb-database-tools"
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "On Ubuntu/Debian, install using:"
            echo "  sudo apt-get install mongodb-database-tools"
        else
            echo "Please download MongoDB Database Tools from:"
            echo "  https://www.mongodb.com/try/download/database-tools"
        fi
        
        exit 1
    fi
}

create_backup() {
    local uri="$1"
    local backup_dir="./.mongo"
    
    if [ -d "$backup_dir" ] && [ "$(ls -A $backup_dir)" ]; then
        echo "Removing previous backup..."
        rm -rf "$backup_dir"
    fi

    mkdir -p "$backup_dir"
    
    echo "Starting backup process..."
    
    if [[ "$uri" =~ /([a-zA-Z]+)(\?|$) ]]; then
        db_name="${BASH_REMATCH[1]}"

        if [ -z "$db_name" ]; then
            echo "Error: No database name found in URI."
            echo "Please use a URI with a database name: mongodb://host:port/dbname"
            exit 1
        else
            echo "Detected database name: $db_name"
            
            EXCLUDE_PARAMS=""

            for collection in "${EXCLUDED_COLLECTIONS[@]}"; do
                EXCLUDE_PARAMS="$EXCLUDE_PARAMS --excludeCollection=$collection"
            done
            
            mongodump --uri="$uri" --gzip --out="$backup_dir" --db="$db_name" $EXCLUDE_PARAMS
        fi
    else
        echo "Error: Could not detect database name from URI."
        echo "Please use a URI with a database name: mongodb://host:port/dbname"
        exit 1
    fi
    
    if [ $? -eq 0 ]; then
        echo "Backup completed successfully!"
        echo "Backup location: $backup_dir"
    else
        echo "Backup failed!"
        rm -rf "$backup_dir"
        exit 1
    fi
}

restore_backup() {
    local uri="$1"
    local backup_path="./.mongo"
    
    if [ ! -d "$backup_path" ]; then
        echo "Backup directory does not exist: $backup_path"
        exit 1
    fi
    
    if [ -z "$(ls -A $backup_path)" ]; then
        echo "Backup directory is empty: $backup_path"
        exit 1
    fi
    
    echo "Starting restore process..."
    
    mongorestore --uri="$uri" --gzip --dir="$backup_path"
    
    if [ $? -eq 0 ]; then
        echo "Restore completed successfully!"
    else
        echo "Restore failed!"
        exit 1
    fi
}

usage() {
    echo "Usage: $0 <command> <mongodb_uri>"
    echo "Commands:"
    echo "  backup  - Create a backup from the specified MongoDB URI"
    echo "  restore - Restore a backup to the specified MongoDB URI"
    echo "Examples:"
    echo "  $0 backup \"mongodb+srv://username:<EMAIL>/myDatabase\""
    echo "  $0 restore \"mongodb://localhost:27017/myLocalDB\""
    exit 1
}

check_mongodb_tools

if [ $# -lt 2 ]; then
    usage
fi

command="$1"
uri="$2"

case "$command" in
    backup)
        create_backup "$uri"
        ;;
    restore)
        restore_backup "$uri"
        ;;
    *)
        echo "Unknown command: $command"
        usage
        ;;
esac

exit 0