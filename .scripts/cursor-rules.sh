#!/bin/bash

combine() {
  echo "Combining .mdc files into cursor.md..."
  
  > cursor.md
  
  file_count=0
  total_files=$(ls -1 .cursor/rules/*.mdc 2>/dev/null | wc -l)

  for file in .cursor/rules/*.mdc; do    
    filename=$(basename "$file")
    file_count=$((file_count + 1))
    
    echo "## $filename" >> cursor.md
    
    cat "$file" >> cursor.md
    
    if [ $file_count -lt $total_files ]; then
      echo "" >> cursor.md
      echo "---" >> cursor.md
      echo "" >> cursor.md
    fi
  done
  
  echo "Combine complete. $file_count files merged into cursor.md"
}

extract() {
  echo "Extracting sections from cursor.md..."
  
  mkdir -p .cursor/rules
  
  if [ ! -f cursor.md ]; then
    echo "Error: cursor.md not found!"
    exit 1
  fi
  
  grep -n "^## .*\.mdc$" cursor.md > /tmp/sections.txt
  
  total_lines=$(wc -l < cursor.md)
  section_starts=$(cut -d: -f1 /tmp/sections.txt)
  
  while IFS=: read -r line_num header; do
    filename=$(echo "$header" | sed 's/^## //')
    
    content_start=$((line_num + 1))
    
    next_section_start=$(awk -v current="$line_num" '$1 > current {print $1; exit}' <<< "$section_starts")
    if [ -z "$next_section_start" ]; then
      content_end=$total_lines
    else
      content_end=$((next_section_start - 3))
    fi
    
    sed -n "${content_start},${content_end}p" cursor.md | sed -e :a -e '/^\n*$/{$d;N;ba' -e '}' > ".cursor/rules/$filename"
  done < /tmp/sections.txt
  
  rm /tmp/sections.txt
  
  file_count=$(ls -1 .cursor/rules/*.mdc 2>/dev/null | wc -l)
  echo "Extraction complete. $file_count files created in .cursor/rules directory."
}

case "$1" in
  combine)
    combine
    ;;
  extract)
    extract
    ;;
  *)
    echo "Usage: $0 {combine|extract}"
    echo "  combine - Combines .cursor/rules/*.mdc files into cursor.md"
    echo "  extract - Extracts sections from cursor.md into .cursor/rules/*.mdc files"
    exit 1
    ;;
esac

exit 0