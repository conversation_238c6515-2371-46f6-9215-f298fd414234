name: Build & push service image

on:
  workflow_call:
    inputs:
      aws_ecr_repo:
        required: true
        type: string
      project:
        required: true
        type: string
      type:
        required: true
        type: string

env:
  AWS_REGION: 'us-east-2'
  AWS_PROFILE_NAME: 'ateams'
  TYPE: ${{ inputs.type }}
  PROJECT: ${{ inputs.project }}
  AWS_ECR_REPO: ${{ inputs.aws_ecr_repo }}
  NPM_TOKEN: ${{ secrets.NPM_TOKEN }}

jobs:
  build-push-service-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up environment variables
        run: |
          echo "AWS_IMG_REPO=257861300614.dkr.ecr.$AWS_REGION.amazonaws.com/$AWS_ECR_REPO" >> $GITHUB_ENV
          echo "IMAGE_TAG=`echo ${{ github.sha }} | cut -c1-7`" >> $GITHUB_ENV
          echo "AWS_PROFLE=$AWS_PROFILE_NAME" >> $GITHUB_ENV
          echo "DOCKERFILE_PATH=.docker/$PROJECT/$TYPE.dockerfile" >> $GITHUB_ENV

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Login to amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Pull cached docker image
        run: |
          docker pull $AWS_IMG_REPO:$IMAGE_TAG || echo "continuing..."
          docker pull $AWS_IMG_REPO:latest || echo "continuing..."

      - name: Build docker image
        run: |
          docker build -f $DOCKERFILE_PATH \
          --build-arg NPM_TOKEN=$NPM_TOKEN \
          --tag $AWS_IMG_REPO:$IMAGE_TAG \
          --tag $AWS_IMG_REPO:latest \
          --cache-from $AWS_IMG_REPO:$IMAGE_TAG \
          --cache-from $AWS_IMG_REPO:latest .

      - name: Push docker image
        run: |
          docker push $AWS_IMG_REPO:$IMAGE_TAG
          docker push $AWS_IMG_REPO:latest
