name: Lint and test services

on:
  pull_request:

jobs:
  check-pr-title:
    runs-on: ubuntu-latest

    steps:
      - uses: Slashgear/action-check-pr-title@v4.3.0
        with:
          regexp: '^(CORE-\d{1,5}|NO-JIRA): .{1,50}$'
          helpMessage: "Example: 'CORE-XXXX: <Message>' (The message can have max 50 characters)"

  lint-test-api:
    needs:
      - check-pr-title
    uses: ./.github/workflows/build-push-service-image.yml
    with:
      type: 'lint-test'
      project: 'api'
      aws_ecr_repo: 'ateams/core-platform-api-lint-test'
    secrets: inherit

  lint-test-web:
    needs:
      - check-pr-title
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'lint-test'
      project: 'web'
      env: 'dev'
      aws_ecr_repo: 'ateams/core-platform-web-lint-test'
    secrets: inherit

  lint-test-admin-web:
    needs:
      - check-pr-title
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'lint-test'
      project: 'admin-web'
      env: 'dev'
      aws_ecr_repo: 'ateams/core-platform-admin-web-lint-test'
    secrets: inherit
