name: Deploy frontend to cluster

on:
  workflow_call:
    inputs:
      cluster_name:
        required: true
        type: string
      role_arn:
        required: true
        type: string
      env:
        required: true
        type: string
      k8s_deployment_name:
        required: true
        type: string
      aws_ecr_repo:
        required: true
        type: string
      project:
        required: true
        type: string

env:
  ENV: ${{ inputs.env }}
  PROJECT: ${{ inputs.project }}
  ROLE_ARN: ${{ inputs.role_arn }}
  AWS_ECR_REPO: ${{ inputs.aws_ecr_repo }}
  K8S_CLUSTER_NAME: ${{ inputs.cluster_name }}
  K8S_DEPLOYMENT_NAME: ${{ inputs.k8s_deployment_name }}
  K8S_NAMESPACE: 'core'
  AWS_REGION: 'us-east-2'
  AWS_PROFILE_NAME: 'ateams'
  SOPS_RELEASE_URL: 'https://github.com/mozilla/sops/releases/download/v3.7.3/sops-v3.7.3.linux'

jobs:
  deploy-frontend-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up environment variables
        run: |
          TAG=$(echo ${{ github.sha }} | cut -c1-7)
          echo "IMAGE_TAG=$(if [ $ENV == 'dev' ]; then echo $TAG-dev; else echo $TAG; fi)" >> $GITHUB_ENV
          echo "AWS_IMG_REPO=257861300614.dkr.ecr.$AWS_REGION.amazonaws.com/$AWS_ECR_REPO" >> $GITHUB_ENV
          echo "AWS_PROFLE=$AWS_PROFILE_NAME" >> $GITHUB_ENV
          echo "DOCKERFILE_PATH=.docker/$PROJECT/build.dockerfile" >> $GITHUB_ENV

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Install sops
        run: |
          curl -Lo sops $SOPS_RELEASE_URL && chmod +x sops
          sudo cp sops /usr/local/bin && rm sops

      - name: Install kubectl
        uses: azure/setup-kubectl@v4
        with:
          version: 'v1.23.5'

      - name: Update kubeconfig with authenticator
        run: |
          aws eks update-kubeconfig --name $K8S_CLUSTER_NAME --role-arn $ROLE_ARN --region $AWS_REGION

      - name: Set default Kubernetes namespace
        run: |
          kubectl config set-context --current --namespace=$K8S_NAMESPACE

      - name: Decrypt and apply config secrets
        run: |
          sops --decrypt .kube/$PROJECT/overlays/$ENV/server-config.yaml | kubectl apply -f -

      - name: Update container image
        run: |
          kubectl set image deployment/$K8S_DEPLOYMENT_NAME $K8S_DEPLOYMENT_NAME=$AWS_IMG_REPO:$IMAGE_TAG

      - name: Check rollout status
        run: |
          kubectl rollout status deployment/$K8S_DEPLOYMENT_NAME
