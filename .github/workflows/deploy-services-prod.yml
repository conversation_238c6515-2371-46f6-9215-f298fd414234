name: Deploy all services to prod

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

env:
  CORE_PLATFORM_SLACK_CHANNEL_ID: 'C08BK4P9FRS'
  TAG: ${{ github.ref_name }}

jobs:
  deploy-api-prod:
    concurrency:
      group: ca-deploy-api-prod
    uses: ./.github/workflows/deploy-service-to-cluster.yml
    with:
      env: 'prod'
      project: 'api'
      cluster_name: 'eks-production'
      aws_ecr_repo: 'ateams/core-platform-api'
      k8s_deployment_name: 'core-api'
      role_arn: 'arn:aws:iam::257861300614:role/production-eks-admin'
    secrets: inherit

  build-web-image:
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'build'
      project: 'web'
      env: 'prod'
      aws_ecr_repo: 'ateams/core-platform-web'
    secrets: inherit

  deploy-web-prod:
    needs:
      - build-web-image
    concurrency:
      group: ca-deploy-web-prod
    uses: ./.github/workflows/deploy-frontend-to-cluster.yml
    with:
      env: 'prod'
      project: 'web'
      cluster_name: 'eks-production'
      aws_ecr_repo: 'ateams/core-platform-web'
      k8s_deployment_name: 'core-web'
      role_arn: 'arn:aws:iam::257861300614:role/production-eks-admin'
    secrets: inherit

  build-admin-web-image:
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'build'
      project: 'admin-web'
      env: 'prod'
      aws_ecr_repo: 'ateams/core-platform-admin-web'
    secrets: inherit

  deploy-admin-web-prod:
    needs:
      - build-admin-web-image
    concurrency:
      group: ca-deploy-admin-web-prod
    uses: ./.github/workflows/deploy-frontend-to-cluster.yml
    with:
      env: 'prod'
      project: 'admin-web'
      cluster_name: 'eks-production'
      aws_ecr_repo: 'ateams/core-platform-admin-web'
      k8s_deployment_name: 'core-admin-web'
      role_arn: 'arn:aws:iam::257861300614:role/production-eks-admin'
    secrets: inherit

  generate-release-slack-message:
    needs:
      - deploy-api-prod
      - deploy-web-prod
      - deploy-admin-web-prod
    runs-on: ubuntu-latest
    steps:
      - name: Release
        id: release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.TAG }}
          generate_release_notes: true

      - uses: octokit/request-action@dad4362715b7fb2ddedf9772c8670824af564f0d # Temporary fix for https://github.com/octokit/request-action/issues/315
        id: get_latest_release
        with:
          route: GET /repos/${{ github.repository }}/releases/tags/${{ env.TAG }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: devops-actions/json-to-file@v1.0.0
        with:
          json: '${{ fromJson(steps.get_latest_release.outputs.data).body }}'
          filename: RELEASE_NOTES

      - name: Release notes formatting - Strip html comment from the beginning of release notes
        run: sed --in-place -e 's/<\!--[^>]*-->//g' RELEASE_NOTES && cat RELEASE_NOTES

      - name: Release notes formatting - Strip "full changelog" link
        run: sed --in-place -e 's/\*\*Full Changelog.*//g' RELEASE_NOTES && cat RELEASE_NOTES

      - name: Release notes formatting - Strip new contributor section
        run: |
          sed --in-place -E -e 's/## New Contributors.*//' RELEASE_NOTES
          sed --in-place -E -e 's/\* .+? made their first contribution .+?//' RELEASE_NOTES
          cat RELEASE_NOTES

      - name: Release notes formatting - Replace markdown bullets with a more slack-compatible format
        run: sed --in-place -E -e 's/\* (.+?) (by .+?) in (http.+?)/- <\3|\1> \2/' RELEASE_NOTES && cat RELEASE_NOTES

      - name: Release notes formatting - Replace headers with bold text
        run: sed --in-place -E -e 's/## (.*)/*\1*/' RELEASE_NOTES && cat RELEASE_NOTES

      - name: Ensure release body is JSON-friendly
        run: |
          jq --null-input '{"body": $releaseNotes}' --rawfile releaseNotes RELEASE_NOTES > RELEASE_NOTES_JSON && cat RELEASE_NOTES_JSON

      - name: Assign release notes to step variable
        id: release-notes-formatting
        run: |
          echo "RELEASE_NOTES=$(cat RELEASE_NOTES_JSON | jq '.body')" >> $GITHUB_OUTPUT

      - name: Send release to core-platform
        uses: slackapi/slack-github-action@v1.26.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_TOKEN }}
        with:
          channel-id: ${{ env.CORE_PLATFORM_SLACK_CHANNEL_ID }}
          payload: |
            {
              "blocks": [
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "Core platform release: <${{ steps.release.outputs.url }}|${{ env.TAG }}>"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": ${{ steps.release-notes-formatting.outputs.RELEASE_NOTES }}
                    }
                  }
                ]
            }
