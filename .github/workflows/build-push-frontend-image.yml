name: Build & push frontend image

on:
  workflow_call:
    inputs:
      env:
        required: true
        type: string
      aws_ecr_repo:
        required: true
        type: string
      project:
        required: true
        type: string
      type:
        required: true
        type: string

env:
  ENV: ${{ inputs.env }}
  TYPE: ${{ inputs.type }}
  PROJECT: ${{ inputs.project }}
  AWS_ECR_REPO: ${{ inputs.aws_ecr_repo }}
  AWS_REGION: 'us-east-2'
  AWS_PROFILE_NAME: 'ateams'
  SOPS_RELEASE_URL: 'https://github.com/mozilla/sops/releases/download/v3.7.3/sops-v3.7.3.linux'

jobs:
  build-push-frontend-image:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up environment variables
        run: |
          TAG=$(echo ${{ github.sha }} | cut -c1-7)
          echo "IMAGE_TAG=$(if [ $ENV == 'dev' ] && [ $TYPE == 'build' ]; then echo $TAG-dev; else echo $TAG; fi)" >> $GITHUB_ENV
          echo "AWS_IMG_REPO=257861300614.dkr.ecr.$AWS_REGION.amazonaws.com/$AWS_ECR_REPO" >> $GITHUB_ENV
          echo "AWS_PROFLE=$AWS_PROFILE_NAME" >> $GITHUB_ENV
          echo "DOCKERFILE_PATH=.docker/$PROJECT/$TYPE.dockerfile" >> $GITHUB_ENV

      - name: Configure aws credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Install sops
        run: |
          curl -Lo sops $SOPS_RELEASE_URL && chmod +x sops
          sudo cp sops /usr/local/bin && rm sops

      - name: Decrypt client secrets
        run: |
          sops --decrypt .kube/$PROJECT/overlays/$ENV/client-config.env > .env

          while IFS='=' read -r key value; do
            if [[ -n "$key" && "$key" != "#"* ]]; then
              echo "$key=$value" >> $GITHUB_ENV
            fi
          done < .env

          rm .env

      - name: Login to amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Pull cached docker image
        run: |
          docker pull $AWS_IMG_REPO:$IMAGE_TAG || echo "continuing..."
          docker pull $AWS_IMG_REPO:latest || echo "continuing..."

      - name: Build docker image
        run: |
          BUILD_ARGS=""

          for arg in $(env | grep '^NEXT_' | awk -F= '{print $1}'); do
            if [[ -n "${!arg}" ]]; then
                BUILD_ARGS+="--build-arg $arg=${!arg} "
            fi
          done

          docker build -f $DOCKERFILE_PATH \
          $BUILD_ARGS \
          --tag $AWS_IMG_REPO:$IMAGE_TAG \
          --tag $AWS_IMG_REPO:latest \
          --cache-from $AWS_IMG_REPO:$IMAGE_TAG \
          --cache-from $AWS_IMG_REPO:latest .

      - name: Push docker image
        run: |
          docker push $AWS_IMG_REPO:$IMAGE_TAG
          docker push $AWS_IMG_REPO:latest
