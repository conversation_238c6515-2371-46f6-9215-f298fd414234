name: Deploy all services to dev

on:
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+-dev-[0-9]+'

jobs:
  deploy-api-dev:
    concurrency:
      group: ca-deploy-api-dev
    uses: ./.github/workflows/deploy-service-to-cluster.yml
    with:
      env: 'dev'
      project: 'api'
      cluster_name: 'eks-sandbox'
      aws_ecr_repo: 'ateams/core-platform-api'
      k8s_deployment_name: 'core-api'
      role_arn: 'arn:aws:iam::257861300614:role/sandbox-eks-admin'
    secrets: inherit

  build-web-image:
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'build'
      project: 'web'
      env: 'dev'
      aws_ecr_repo: 'ateams/core-platform-web'
    secrets: inherit

  deploy-web-dev:
    needs:
      - build-web-image
    concurrency:
      group: ca-deploy-web-dev
    uses: ./.github/workflows/deploy-frontend-to-cluster.yml
    with:
      env: 'dev'
      project: 'web'
      cluster_name: 'eks-sandbox'
      aws_ecr_repo: 'ateams/core-platform-web'
      k8s_deployment_name: 'core-web'
      role_arn: 'arn:aws:iam::257861300614:role/sandbox-eks-admin'
    secrets: inherit

  build-admin-web-image:
    uses: ./.github/workflows/build-push-frontend-image.yml
    with:
      type: 'build'
      project: 'admin-web'
      env: 'dev'
      aws_ecr_repo: 'ateams/core-platform-admin-web'
    secrets: inherit

  deploy-admin-web-dev:
    needs:
      - build-admin-web-image
    concurrency:
      group: ca-deploy-admin-web-dev
    uses: ./.github/workflows/deploy-frontend-to-cluster.yml
    with:
      env: 'dev'
      project: 'admin-web'
      cluster_name: 'eks-sandbox'
      aws_ecr_repo: 'ateams/core-platform-admin-web'
      k8s_deployment_name: 'core-admin-web'
      role_arn: 'arn:aws:iam::257861300614:role/sandbox-eks-admin'
    secrets: inherit
