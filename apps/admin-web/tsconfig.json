{"compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "allowImportingTsExtensions": true, "noEmit": true, "inlineSources": false, "isolatedModules": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "noUnusedLocals": true, "noUnusedParameters": true, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "noImplicitAny": true, "allowJs": true, "incremental": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "resolveJsonModule": true, "target": "es5", "baseUrl": ".", "paths": {"@config/*": ["src/config/*"], "@lib/*": ["src/lib/*"], "@modules/*": ["src/modules/*"], "@shared-components": ["src/components/index"]}, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "src", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}