'use client';

import Document from '@tiptap/extension-document';
import { Heading } from '@tiptap/extension-heading';
import { Link } from '@tiptap/extension-link';
import { Placeholder } from '@tiptap/extension-placeholder';
import { Underline } from '@tiptap/extension-underline';
import { useEditor, EditorContent, BubbleMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Bold, Italic, Underline as UnderlineIcon, Link as LinkIcon, List, ListOrdered, HeadingIcon } from 'lucide-react';
import { useEffect, useMemo } from 'react';

import { cn } from '@lib/utils';

export type RichTextEditorProps = {
  value: string;
  onChange: (value: string, text?: string) => void;
  placeholder?: string;
  className?: string;
  contentClassName?: string;
  includeHeadingControl?: boolean;
  includeLinkControl?: boolean;
  includeHeading?: boolean;
  customContentSchema?: ('heading' | 'block')[];
};

type ToolbarButtonProps = {
  title: string;
  onClick: () => void;
  isActive?: boolean;
  children: React.ReactNode;
};

const ToolbarButton = ({ title, onClick, isActive, children }: ToolbarButtonProps) => {
  return (
    <button
      type="button"
      title={title}
      className={cn('flex h-8 w-8 items-center justify-center rounded', isActive ? 'bg-gray-200 text-gray-900' : 'text-gray-700 hover:bg-gray-100')}
      onClick={onClick}
    >
      {children}
    </button>
  );
};

const DocumentWithHeading = Document.extend({
  content: 'heading block+',
});

export const RichTextEditor = ({
  value,
  onChange,
  placeholder,
  className,
  contentClassName,
  includeHeadingControl = false,
  includeLinkControl = true,
  includeHeading = false,
  customContentSchema,
}: RichTextEditorProps) => {
  const documentSchema = useMemo(() => {
    if (customContentSchema) {
      return Document.extend({
        content: customContentSchema.join(' '),
      });
    }

    if (includeHeading) {
      return DocumentWithHeading;
    }

    return Document;
  }, [includeHeading, customContentSchema]);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({ document: false, heading: false }),
      documentSchema,
      Underline,
      Heading.configure({
        levels: [3],
      }),
      Link.configure({
        openOnClick: false,
      }),
      Placeholder.configure({
        showOnlyCurrent: false,
        placeholder: ({ node }) => {
          if (node.type.name === 'heading') {
            return 'Add a headline';
          }

          if (placeholder) {
            return placeholder;
          }

          return '';
        },
      }),
    ],
    content: value,
    immediatelyRender: true,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML(), editor.getText());
    },
  });

  useEffect(() => {
    if (editor && value !== editor.getHTML()) {
      editor.commands.setContent(value);
    }
  }, [value, editor]);

  const handleInsertLink = () => {
    if (!editor) {
      return;
    }

    const previousUrl = editor.getAttributes('link').href as string | undefined;
    const url = window.prompt('URL', previousUrl ?? '');

    if (url === null) {
      return;
    }

    if (url === '') {
      editor.chain().focus().extendMarkRange('link').unsetLink().run();
      return;
    }

    editor.chain().focus().extendMarkRange('link').setLink({ href: url }).run();
  };

  return (
    <div className={cn('relative overflow-hidden rounded-md border border-gray-400', className)}>
      {editor && (
        <div className="flex items-center gap-2 border-b border-gray-200 bg-gray-50 p-2">
          {includeHeadingControl && (
            <>
              <ToolbarButton
                title="Heading"
                isActive={editor.isActive('heading', { level: 3 })}
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              >
                <HeadingIcon size={16} />
              </ToolbarButton>
              <div className="mx-1 h-4 w-px bg-gray-300" />
            </>
          )}
          <ToolbarButton title="Bold" isActive={editor.isActive('bold')} onClick={() => editor.chain().focus().toggleBold().run()}>
            <Bold size={16} />
          </ToolbarButton>
          <ToolbarButton title="Italic" isActive={editor.isActive('italic')} onClick={() => editor.chain().focus().toggleItalic().run()}>
            <Italic size={16} />
          </ToolbarButton>
          <ToolbarButton title="Underline" isActive={editor.isActive('underline')} onClick={() => editor.chain().focus().toggleUnderline().run()}>
            <UnderlineIcon size={16} />
          </ToolbarButton>
          <div className="mx-1 h-4 w-px bg-gray-300" />
          <ToolbarButton title="Bullet list" isActive={editor.isActive('bulletList')} onClick={() => editor.chain().focus().toggleBulletList().run()}>
            <List size={16} />
          </ToolbarButton>
          <ToolbarButton title="Numbered list" isActive={editor.isActive('orderedList')} onClick={() => editor.chain().focus().toggleOrderedList().run()}>
            <ListOrdered size={16} />
          </ToolbarButton>
          {includeLinkControl && (
            <>
              <div className="mx-1 h-4 w-px bg-gray-300" />
              <ToolbarButton title="Insert link" isActive={editor.isActive('link')} onClick={handleInsertLink}>
                <LinkIcon size={16} />
              </ToolbarButton>
            </>
          )}
        </div>
      )}
      <EditorContent
        editor={editor}
        className={cn(
          'min-h-[150px] w-full resize-y p-4',
          'prose prose-neutral prose-sm prose-p:my-0 prose-ol:my-0 prose-ul:my-0 prose-li:my-0 max-w-none',
          '[&_.is-editor-empty]:before:absolute [&_.is-editor-empty]:before:text-gray-500 [&_.is-editor-empty]:before:content-[attr(data-placeholder)] [&_h3]:mt-0',
          '[&_.ProseMirror]:border-none [&_.ProseMirror]:shadow-none [&_.ProseMirror]:outline-none',
          '[&_.ProseMirror:focus]:border-none [&_.ProseMirror:focus]:shadow-none [&_.ProseMirror:focus]:outline-none',
          contentClassName
        )}
      />

      {editor && (
        <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }}>
          <div className="flex rounded-md border border-gray-200 bg-white p-1 shadow-sm">
            <ToolbarButton title="Bold" isActive={editor.isActive('bold')} onClick={() => editor.chain().focus().toggleBold().run()}>
              <Bold size={16} />
            </ToolbarButton>
            <ToolbarButton title="Italic" isActive={editor.isActive('italic')} onClick={() => editor.chain().focus().toggleItalic().run()}>
              <Italic size={16} />
            </ToolbarButton>
            <ToolbarButton title="Underline" isActive={editor.isActive('underline')} onClick={() => editor.chain().focus().toggleUnderline().run()}>
              <UnderlineIcon size={16} />
            </ToolbarButton>
            <ToolbarButton title="Insert link" isActive={editor.isActive('link')} onClick={handleInsertLink}>
              <LinkIcon size={16} />
            </ToolbarButton>
          </div>
        </BubbleMenu>
      )}
    </div>
  );
};
