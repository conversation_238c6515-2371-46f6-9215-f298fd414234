import { UserBadge as UserBadgeType } from '@packages/contracts';

import { TooltipContent, TooltipProvider, TooltipTrigger } from '@shared-components';

import {
  ATeamerIcon,
  ATeamerResidenceIcon,
  BeenOnMissionIcon,
  ExceptionalATeamerIcon,
  HighPotentialIcon,
  LimitedAccessIcon,
  NotScrubbedIcon,
  SelectionTeamIcon,
  UnqualifiedIcon,
  VettingInterviewDateIcon,
  VettingScheduledIcon,
} from './icons';

import { Tooltip } from '../tooltip';

export const BADGES_CONFIG = {
  ATeamer: {
    icon: ATeamerIcon,
    tooltip: `Vetted A.Teamer`,
  },
  ATeamerResidence: {
    icon: ATeamerResidenceIcon,
    tooltip: `A.Teamer in Residence`,
  },
  BeenOnMission: {
    icon: BeenOnMissionIcon,
    tooltip: `Been on Mission`,
  },
  ExceptionalATeamer: {
    icon: ExceptionalATeamerIcon,
    tooltip: `Exceptional A.Teamer`,
  },
  HighPotential: {
    icon: HighPotentialIcon,
    tooltip: `High Potential User`,
  },
  NotScrubbed: {
    icon: NotScrubbedIcon,
    tooltip: `Not Scrubbed`,
  },
  SelectionTeam: {
    icon: SelectionTeamIcon,
    tooltip: `SelectionTeam`,
  },
  UnqualifiedUser: {
    icon: UnqualifiedIcon,
    tooltip: `Unqualified`,
  },
  VettingScheduled: {
    icon: VettingScheduledIcon,
    tooltip: `Evaluation Call Pending`,
  },
  VettingInterviewDate: {
    icon: VettingInterviewDateIcon,
    tooltip: `Evaluation Call Booked`,
  },
  Unvetted: {
    icon: NotScrubbedIcon,
    tooltip: `Not fully vetted`,
  },
  LimitedAccess: {
    icon: LimitedAccessIcon,
    tooltip: `Limited Access`,
  },
};

export const UserBadge = ({ badge, className }: { badge: UserBadgeType; className?: string }) => {
  const badgeConfig = BADGES_CONFIG[badge];
  const Icon = badgeConfig.icon;
  const tooltip = badgeConfig.tooltip;

  return (
    <TooltipProvider delayDuration={200}>
      <Tooltip>
        <TooltipTrigger>
          <Icon className={className} />
        </TooltipTrigger>
        <TooltipContent>{tooltip}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
