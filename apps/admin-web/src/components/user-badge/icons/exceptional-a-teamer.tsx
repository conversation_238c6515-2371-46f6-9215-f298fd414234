import React from 'react';

import { BadgeIconProps } from './types';

export const ExceptionalATeamerIcon: React.FC<BadgeIconProps> = ({ className }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path
      d="M9.37239 1.30518C10.6919 -0.435059 13.3081 -0.435059 14.6276 1.30518C15.3476 2.25471 16.5239 2.74193 17.7044 2.57962C19.868 2.28214 21.7179 4.13203 21.4204 6.29562C21.2581 7.47614 21.7453 8.65241 22.6948 9.37239C24.4351 10.6919 24.4351 13.3081 22.6948 14.6276C21.7453 15.3476 21.2581 16.5239 21.4204 17.7044C21.7179 19.868 19.868 21.7179 17.7044 21.4204C16.5239 21.2581 15.3476 21.7453 14.6276 22.6948C13.3081 24.4351 10.6919 24.4351 9.37239 22.6948C8.65241 21.7453 7.47614 21.2581 6.29562 21.4204C4.13203 21.7179 2.28214 19.868 2.57962 17.7044C2.74193 16.5239 2.25471 15.3476 1.30518 14.6276C-0.435059 13.3081 -0.435059 10.6919 1.30518 9.37239C2.25471 8.65241 2.74193 7.47614 2.57962 6.29562C2.28214 4.13203 4.13203 2.28214 6.29562 2.57962C7.47614 2.74193 8.65241 2.25471 9.37239 1.30518Z"
      fill="url(#paint0_linear_2306_94)"
    />
    <path d="M8 11.5L11 15L16 9" stroke="white" strokeWidth="1.6" strokeLinecap="round" strokeLinejoin="round" />
    <defs>
      <linearGradient id="paint0_linear_2306_94" x1="12" y1="0" x2="12" y2="24" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FEAC0C" />
        <stop offset="1" stopColor="#FE630C" />
      </linearGradient>
    </defs>
  </svg>
);
