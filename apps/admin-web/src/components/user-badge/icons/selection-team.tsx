import React from 'react';

import { BadgeIconProps } from './types';

export const SelectionTeamIcon: React.FC<BadgeIconProps> = ({ className }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <circle cx="12" cy="12" r="12" fill="url(#paint0_linear_3698_508)" />
    <circle cx="12" cy="12" r="9" fill="url(#paint1_linear_3698_508)" />
    <path
      d="M11.4204 7.35934C11.6706 6.88022 12.3294 6.88022 12.5796 7.35934L13.7008 9.50655C13.7958 9.68838 13.964 9.81599 14.1589 9.85411L16.4608 10.3043C16.9744 10.4047 17.1779 11.059 16.8189 11.4556L15.2101 13.2328C15.0738 13.3833 15.0096 13.5898 15.0351 13.7952L15.3365 16.2206C15.4037 16.7618 14.8708 17.1662 14.3987 16.9322L12.2831 15.8834C12.1039 15.7945 11.8961 15.7945 11.7169 15.8834L9.60129 16.9322C9.12922 17.1662 8.5963 16.7618 8.66354 16.2206L8.96489 13.7952C8.99041 13.5898 8.92617 13.3833 8.78993 13.2328L7.18106 11.4556C6.82206 11.059 7.02562 10.4047 7.53924 10.3043L9.84111 9.85411C10.036 9.81599 10.2042 9.68838 10.2992 9.50655L11.4204 7.35934Z"
      fill="#FFF3B5"
    />
    <defs>
      <linearGradient id="paint0_linear_3698_508" x1="12" y1="0" x2="12" y2="24" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFBF1C" />
        <stop offset="1" stopColor="#FE800C" />
      </linearGradient>
      <linearGradient id="paint1_linear_3698_508" x1="12" y1="21" x2="12" y2="3" gradientUnits="userSpaceOnUse">
        <stop stopColor="#FFC120" />
        <stop offset="1" stopColor="#FE800C" />
      </linearGradient>
    </defs>
  </svg>
);
