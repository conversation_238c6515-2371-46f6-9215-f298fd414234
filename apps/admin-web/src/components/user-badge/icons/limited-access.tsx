import React from 'react';

import { BadgeIconProps } from './types';

export const LimitedAccessIcon: React.FC<BadgeIconProps> = ({ className }) => (
  <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path
      d="M14.0586 1.95776C16.0379 -0.652588 19.9621 -0.652588 21.9414 1.95776C23.0214 3.38206 24.7858 4.1129 26.5566 3.86943C29.802 3.42321 32.5768 6.19805 32.1306 9.44342C31.8871 11.2142 32.6179 12.9786 34.0422 14.0586C36.6526 16.0379 36.6526 19.9621 34.0422 21.9414C32.6179 23.0214 31.8871 24.7858 32.1306 26.5566C32.5768 29.802 29.802 32.5768 26.5566 32.1306C24.7858 31.8871 23.0214 32.6179 21.9414 34.0422C19.9621 36.6526 16.0379 36.6526 14.0586 34.0422C12.9786 32.6179 11.2142 31.8871 9.44342 32.1306C6.19805 32.5768 3.42321 29.802 3.86943 26.5566C4.1129 24.7858 3.38206 23.0214 1.95776 21.9414C-0.652588 19.9621 -0.652588 16.0379 1.95776 14.0586C3.38206 12.9786 4.1129 11.2142 3.86943 9.44342C3.42321 6.19805 6.19805 3.42321 9.44342 3.86943C11.2142 4.1129 12.9786 3.38206 14.0586 1.95776Z"
      fill="url(#paint0_linear_4383_487)"
    />
    <path d="M21 18H15" stroke="white" strokeWidth="2.4" strokeLinecap="round" strokeLinejoin="round" />
    <circle cx="18" cy="18" r="8.8" stroke="white" strokeWidth="2.4" strokeLinecap="round" />
    <defs>
      <linearGradient id="paint0_linear_4383_487" x1="18" y1="0" x2="18" y2="36" gradientUnits="userSpaceOnUse">
        <stop stopColor="#9C9C9C" />
        <stop offset="1" stopColor="#5C5C5C" />
      </linearGradient>
    </defs>
  </svg>
);
