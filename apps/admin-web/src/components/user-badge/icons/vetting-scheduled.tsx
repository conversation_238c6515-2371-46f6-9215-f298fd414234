import React from 'react';

import { BadgeIconProps } from './types';

export const VettingScheduledIcon: React.FC<BadgeIconProps> = ({ className }) => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
    <path
      d="M9.37239 1.30518C10.6919 -0.435059 13.3081 -0.435059 14.6276 1.30518C15.3476 2.25471 16.5239 2.74193 17.7044 2.57962C19.868 2.28214 21.7179 4.13203 21.4204 6.29562C21.2581 7.47614 21.7453 8.65241 22.6948 9.37239C24.4351 10.6919 24.4351 13.3081 22.6948 14.6276C21.7453 15.3476 21.2581 16.5239 21.4204 17.7044C21.7179 19.868 19.868 21.7179 17.7044 21.4204C16.5239 21.2581 15.3476 21.7453 14.6276 22.6948C13.3081 24.4351 10.6919 24.4351 9.37239 22.6948C8.65241 21.7453 7.47614 21.2581 6.29562 21.4204C4.13203 21.7179 2.28214 19.868 2.57962 17.7044C2.74193 16.5239 2.25471 15.3476 1.30518 14.6276C-0.435059 13.3081 -0.435059 10.6919 1.30518 9.37239C2.25471 8.65241 2.74193 7.47614 2.57962 6.29562C2.28214 4.13203 4.13203 2.28214 6.29562 2.57962C7.47614 2.74193 8.65241 2.25471 9.37239 1.30518Z"
      fill="url(#paint0_linear_2306_123)"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12 17C14.7614 17 17 14.7614 17 12C17 9.23858 14.7614 7 12 7C9.23858 7 7 9.23858 7 12C7 12.747 7.16383 13.4558 7.45753 14.0924C7.56453 14.3243 7.60387 14.5845 7.54192 14.8323L7.19163 16.2335C7.1026 16.5896 7.41483 16.917 7.77476 16.845L9.38552 16.5229C9.6066 16.4787 9.83474 16.5138 10.0422 16.6021C10.6435 16.8583 11.3052 17 12 17Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15 11C15 10.7239 14.7761 10.5 14.5 10.5H9.5C9.22386 10.5 9 10.7239 9 11C9 11.2761 9.22386 11.5 9.5 11.5H14.5C14.7761 11.5 15 11.2761 15 11ZM12 13C12 12.7239 11.7761 12.5 11.5 12.5H9.5C9.22386 12.5 9 12.7239 9 13C9 13.2761 9.22386 13.5 9.5 13.5H11.5C11.7761 13.5 12 13.2761 12 13Z"
      fill="#5BD2BD"
    />
    <defs>
      <linearGradient id="paint0_linear_2306_123" x1="12" y1="0" x2="12" y2="24" gradientUnits="userSpaceOnUse">
        <stop stopColor="#35E2C4" />
        <stop offset="1" stopColor="#498BB0" />
      </linearGradient>
    </defs>
  </svg>
);
