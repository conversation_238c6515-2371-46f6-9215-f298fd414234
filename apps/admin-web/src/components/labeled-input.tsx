import * as React from 'react';

import { cn } from '@lib/utils';

import { Input } from './input';

export type LabeledInputProps = {
  label: string;
  type: string;
  containerClassName?: string;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size' | 'type'>;

export const LabeledInput = React.forwardRef<HTMLInputElement, LabeledInputProps>(({ className, type, label, containerClassName, ...props }, ref) => {
  return (
    <div className="space-y-2">
      <div className={cn('border-input bg-background flex overflow-hidden rounded-md border', containerClassName)}>
        <Input
          className={cn('rounded-none border-0 p-4 text-sm focus-visible:ring-0 focus-visible:ring-offset-0', className)}
          type={type}
          ref={ref}
          {...props}
        />
        <div className="flex items-center px-4 text-gray-500">{label}</div>
      </div>
    </div>
  );
});

LabeledInput.displayName = 'LabeledInput';
