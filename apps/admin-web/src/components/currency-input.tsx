import React from 'react';

import { cn } from '@lib/utils';

import { Input, InputProps } from './input';

type CurrencyInputProps = InputProps & {
  unit?: 'hour' | 'month';
};

export const CurrencyInput = React.forwardRef<HTMLInputElement, CurrencyInputProps>(({ className, unit = 'hour', ...props }, ref) => {
  const unitToShow = unit === 'hour' ? '/hr' : '/mon';

  return (
    <div className="relative flex items-center">
      <span className="absolute left-3 text-sm font-medium text-gray-700">$</span>
      <Input type="number" ref={ref} className={cn('px-7', className)} {...props} />
      <span className="absolute right-3 text-sm font-medium text-gray-700">{unitToShow}</span>
    </div>
  );
});
CurrencyInput.displayName = 'CurrencyInput';
