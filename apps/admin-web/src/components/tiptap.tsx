'use client';

import Link from '@tiptap/extension-link';
import Placeholder from '@tiptap/extension-placeholder';
import Underline from '@tiptap/extension-underline';
import { Content, EditorContent, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';

import { cn } from '@lib/utils';

import { TiptapMenu } from '@shared-components';

type TiptapProps = {
  className?: string;
  content?: Content;
  placeholder?: string;
  readOnly?: boolean;
  onChange: (content: Content) => void;
};

const Tiptap = ({ className, content, placeholder, readOnly = false, onChange }: TiptapProps) => {
  const editor = useEditor({
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    extensions: [
      StarterKit,
      Underline,
      Link,
      Placeholder.configure({
        placeholder,
      }),
    ],
    editable: !readOnly,
    immediatelyRender: false,
    editorProps: {
      attributes: {
        class: 'focus:outline-none',
      },
    },
  });

  return (
    <>
      <EditorContent
        editor={editor}
        className={cn(
          'prose prose-neutral prose-sm prose-p:my-0 prose-ol:my-0 prose-ul:my-0 prose-li:my-0 max-w-none',
          '[&_.is-editor-empty]:before:absolute [&_.is-editor-empty]:before:text-gray-500 [&_.is-editor-empty]:before:content-[attr(data-placeholder)]',
          className
        )}
      />
      {editor && <TiptapMenu editor={editor} />}
    </>
  );
};

export { Tiptap };
