'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useState } from 'react';

import api from '@config/api';

const ReactQueryProvider = ({ children }: { children: React.ReactNode }) => {
  const [queryClient] = useState(() => new QueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      <api.ReactQueryProvider>{children}</api.ReactQueryProvider>
    </QueryClientProvider>
  );
};

export default ReactQueryProvider;
