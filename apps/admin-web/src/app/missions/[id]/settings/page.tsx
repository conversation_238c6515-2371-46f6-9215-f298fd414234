import type { Metadata } from 'next';

import MissionSettings from '@modules/missions/components/settings';

export const metadata: Metadata = {
  title: 'Mission Management | Admin Dashboard',
  description: 'Admin panel for editing mission details, managing roles, and configuring mission settings',
};

const MissionPage = ({ params: { id } }: { params: { id: string } }) => {
  return (
    <div className="min-h-dvh bg-gray-50">
      <MissionSettings missionId={id} />
    </div>
  );
};

export default MissionPage;
