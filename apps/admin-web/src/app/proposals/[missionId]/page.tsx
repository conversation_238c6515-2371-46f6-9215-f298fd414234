import type { Metadata } from 'next';

import ProposalCreation from '@modules/proposals/components/proposal-creation';

export const metadata: Metadata = {
  title: 'Proposals Management | Admin Dashboard',
  description: 'Admin panel for viewing and creating proposals',
};

const ProposalsPage = ({ params: { missionId } }: { params: { missionId: string } }) => {
  return (
    <div className="min-h-screen bg-gray-50 px-14 py-10">
      <ProposalCreation missionId={missionId} />
    </div>
  );
};

export default ProposalsPage;
