import './globals.css';

import { inter, plusJakartaSans } from '@lib/fonts';

import { Toaster } from '@shared-components';

import ReactQueryProvider from './react-query';

type RootLayoutProps = {
  children: React.ReactNode;
};

const RootLayout: React.FC<RootLayoutProps> = ({ children }): JSX.Element => {
  return (
    <html lang="en" className={`${inter.variable} ${plusJakartaSans.variable}`}>
      <body className={inter.className}>
        <ReactQueryProvider>{children}</ReactQueryProvider>
        <Toaster richColors />
      </body>
    </html>
  );
};

export default RootLayout;
