import { X } from 'lucide-react';
import React from 'react';

import { cn } from '@lib/utils';

type TagProps = {
  name: string;
  className?: string;
  onTagRemoveClick?: () => void;
};

export const Tag: React.FC<TagProps> = ({ name, className, onTagRemoveClick }) => {
  return (
    <div className={cn('flex items-center gap-1 rounded-full bg-gray-400 px-3 py-1 text-xs hover:bg-gray-300', className)}>
      {name}
      {onTagRemoveClick && (
        <button type="button" className="ml-1" onClick={() => onTagRemoveClick()}>
          <X className="size-3" />
        </button>
      )}
    </div>
  );
};
