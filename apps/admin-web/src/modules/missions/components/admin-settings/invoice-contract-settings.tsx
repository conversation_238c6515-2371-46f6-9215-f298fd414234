import { MISSION_BILLING_PAYMENT_DUE_VALUES } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import {
  Checkbox,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@shared-components';

import { AdminSettingsFormValues } from './admin-settings-form';
import { BillingAddress } from './billing-address';

export const InvoiceAndContractSettings = () => {
  const { control } = useFormContext<AdminSettingsFormValues>();

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name="invoicePO"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Invoice P.O.</FormLabel>
            <FormDescription>If defined, the purchase order number will be referenced throughout the mission invoices with the client.</FormDescription>
            <FormControl>
              <Input placeholder="P.O. number..." className="lg:w-1/2" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <BillingAddress />
      <FormField
        control={control}
        name="paymentTerms"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Payment terms</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className="lg:w-1/2">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {MISSION_BILLING_PAYMENT_DUE_VALUES.map((option) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="generateContracts"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <FormLabel className="!my-auto">Generate contracts for any member and client changes for this mission.</FormLabel>
          </FormItem>
        )}
      />
    </div>
  );
};
