import { zodResolver } from '@hookform/resolvers/zod';
import {
  GetAdminSettingsDto,
  MISSION_APPLY_STATUSES,
  MISSION_BILLING_PAYMENT_DUE_VALUES,
  MISSION_BILLING_PERIOD_VALUES,
  MISSION_STATUSES,
} from '@packages/contracts';
import { notFound } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Divider } from '@modules/missions/components/divider';
import { useUpdateAdminSettingsMutation } from '@modules/missions/rq/mutations';
import { useGetAdminSettings } from '@modules/missions/rq/queries';

import { Button, Form, Skeleton } from '@shared-components';

import { AdminInfoAndStatuses } from './admin-info-statuses';
import { ApplySettings } from './apply-settings';
import { InvoiceAndContractSettings } from './invoice-contract-settings';
import { MissionClients } from './mission-clients';
import { TfsOwner } from './tfs-owner';

export type AdminSettingsFormValues = z.infer<typeof adminSettingsFormSchema>;

type AdminSettingsFormProps = {
  missionId: string;
};

const adminSettingsFormSchema = z.object({
  missionStatus: z.enum(MISSION_STATUSES),
  applyStatus: z.enum(MISSION_APPLY_STATUSES).optional(),
  timesheetPeriods: z.enum(MISSION_BILLING_PERIOD_VALUES).optional(),
  automaticInvoicing: z.string().optional(),
  tfsOwner: z.string().optional(),
  invoicePO: z.string().optional(),
  billingAddress: z
    .object({
      line1: z.string().optional(),
      line2: z.string().optional(),
      city: z.string().optional(),
      state: z.string().optional(),
      postalCode: z.string().optional(),
      country: z.string().optional(),
    })
    .optional(),
  paymentTerms: z.enum(MISSION_BILLING_PAYMENT_DUE_VALUES).optional(),
  generateContracts: z.boolean().default(false),
});

const getFormValues = (adminSettings: GetAdminSettingsDto): AdminSettingsFormValues => {
  return {
    missionStatus: adminSettings.missionStatus,
    applyStatus: adminSettings.applyStatus,
    timesheetPeriods: adminSettings.timesheetPeriods,
    automaticInvoicing: adminSettings.automaticInvoicing,
    tfsOwner: adminSettings.tfsOwner,
    invoicePO: adminSettings.invoicePO,
    billingAddress: adminSettings.billingAddress,
    paymentTerms: adminSettings.paymentTerms,
    generateContracts: adminSettings.generateContracts,
  };
};

export const AdminSettingsForm: React.FC<AdminSettingsFormProps> = ({ missionId }) => {
  const [formKey, setFormKey] = useState(0);

  const { data: adminSettings, isLoading } = useGetAdminSettings(missionId);
  const updateAdminSettingsMutation = useUpdateAdminSettingsMutation();

  const form = useForm<AdminSettingsFormValues>({
    resolver: zodResolver(adminSettingsFormSchema),
  });

  useEffect(() => {
    if (adminSettings) {
      form.reset(getFormValues(adminSettings));
      setFormKey((prev) => prev + 1);
    }
  }, [adminSettings, form]);

  const onSubmit = (values: AdminSettingsFormValues) => {
    updateAdminSettingsMutation.mutate(
      { missionId, settings: values },
      {
        onSuccess: () => {
          toast.success('Admin settings updated successfully');
        },
        onError: () => {
          toast.error('Failed to update admin settings');
        },
      }
    );
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    void form.handleSubmit(onSubmit)(e);
  };

  if (isLoading) {
    return (
      <div className="mx-auto my-4 max-w-5xl space-y-8">
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/5" />
          <Skeleton className="h-10" />
        </div>
        <Skeleton className="h-20" />
        <hr className="border-gray-400" />
        <Skeleton className="h-20" />
        <Skeleton className="h-32" />
        <Skeleton className="h-24" />
      </div>
    );
  }

  if (!adminSettings) {
    return notFound();
  }

  const managers = adminSettings.managers;

  return (
    <Form {...form} key={formKey}>
      <form onSubmit={handleSubmit} className="mx-auto max-w-5xl space-y-8 pb-4">
        <AdminInfoAndStatuses />
        <Divider />
        <ApplySettings />
        <TfsOwner />
        <Divider />
        <MissionClients managers={managers} missionId={missionId} />
        <InvoiceAndContractSettings />
        <Button type="submit" variant="purple" className="mx-auto flex px-8">
          Save changes
        </Button>
      </form>
    </Form>
  );
};
