import { MissionStatus, MISSION_STATUSES } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared-components';

import { AdminSettingsFormValues } from './admin-settings-form';

const getAvailableMissionStatusOptions = (currentStatus: MissionStatus) => {
  if (currentStatus === 'Running') {
    return ['Running', 'ScheduledToEnd'];
  }

  if (currentStatus === 'ScheduledToEnd') {
    return MISSION_STATUSES.filter((status) => status !== 'Ended');
  }

  if (currentStatus === 'Ended') {
    return MISSION_STATUSES.filter((status) => status !== 'ScheduledToEnd');
  }

  return MISSION_STATUSES;
};

export const AdminInfoAndStatuses = () => {
  const { control } = useFormContext<AdminSettingsFormValues>();

  const renderStatuses = (status: MissionStatus) => {
    const allStatusOptions = getAvailableMissionStatusOptions(status);

    return (
      <>
        {allStatusOptions.map((status) => (
          <SelectItem key={status} value={status}>
            {status}
          </SelectItem>
        ))}
      </>
    );
  };

  return (
    <>
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">Admin settings</h1>
        <p className="text-xs text-gray-500">
          Change the status and make adjustments to this mission as needed. The formation team will automatically be notified when the mission is edited, roles
          are added/removed, or if a role has changed.
        </p>
      </div>
      <FormField
        control={control}
        name="missionStatus"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Mission status</FormLabel>
            <Select value={field.value} onValueChange={field.onChange}>
              <FormControl>
                <SelectTrigger className="lg:w-1/2">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>{renderStatuses(field.value)}</SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};
