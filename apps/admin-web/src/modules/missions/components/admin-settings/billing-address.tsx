import { useFormContext } from 'react-hook-form';

import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@shared-components';

import { AdminSettingsFormValues } from './admin-settings-form';

export const BillingAddress = () => {
  const { control } = useFormContext<AdminSettingsFormValues>();

  return (
    <div className="space-y-4">
      <FormDescription>
        This address will be used for all invoices sent to the billing customer. It's stored both on Stripe customers and the client customer entity and can be
        updated by the client.
      </FormDescription>
      <div className="grid grid-cols-1 gap-4">
        <FormField
          control={control}
          name="billingAddress.line1"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Billing address</FormLabel>
              <FormControl>
                <Input placeholder="Line 1..." className="lg:w-1/2" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name="billingAddress.line2"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input placeholder="Line 2..." className="lg:w-1/2" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={control}
            name="billingAddress.city"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder="City..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="billingAddress.state"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder="State..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={control}
            name="billingAddress.postalCode"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input placeholder="Postal code..." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={control}
            name="billingAddress.country"
            render={({ field }) => (
              <FormItem>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="us">United States</SelectItem>
                    <SelectItem value="ca">Canada</SelectItem>
                    <SelectItem value="uk">United Kingdom</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};
