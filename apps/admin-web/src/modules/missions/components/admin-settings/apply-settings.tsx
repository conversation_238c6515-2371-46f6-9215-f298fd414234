import { MISSION_APPLY_STATUSES, MISSION_BILLING_PERIOD_VALUES } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared-components';

import { AdminSettingsFormValues } from './admin-settings-form';

export const ApplySettings = () => {
  const { control } = useFormContext<AdminSettingsFormValues>();

  return (
    <div className="space-y-4">
      <FormLabel className="text-base">Apply settings</FormLabel>
      <FormField
        control={control}
        name="applyStatus"
        render={({ field }) => (
          <FormItem>
            <p className="text-xs text-gray-500">
              Change the status and make adjustments to this mission as needed. The formation team will automatically be notified when the mission is edited,
              roles are added/removed, or if a role has changed.
            </p>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger className="lg:w-1/2">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {MISSION_APPLY_STATUSES.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="timesheetPeriods"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Timesheet periods</FormLabel>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger className="lg:w-1/2">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {MISSION_BILLING_PERIOD_VALUES.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
            <ul className="mt-2 list-disc pl-5 text-xs text-gray-500">
              <li>Weekly - Monday to Sunday</li>
              <li>Bi-Weekly - On the date of 15 and the end of month</li>
              <li>Monthly - On the end of month</li>
            </ul>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="automaticInvoicing"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Automatic invoicing</FormLabel>
            {/* disabled until we are ready for automated invoicing. */}
            <Select disabled onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger className="lg:w-1/2">
                  <SelectValue placeholder="Disabled" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="biweekly">Bi-Weekly - On the dates 4th and 20th</SelectItem>
                <SelectItem value="monthly">Monthly - On the 4th of month</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
            <ul className="mt-2 list-disc pl-5 text-xs text-gray-500">
              <li>Bi-Weekly - On the dates 4th and 20th</li>
              <li>Monthly - On the 4th of month</li>
            </ul>
          </FormItem>
        )}
      />
    </div>
  );
};
