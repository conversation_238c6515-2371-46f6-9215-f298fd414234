import { GetAdminSettingsDto } from '@packages/contracts';
import { ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { memo, useState } from 'react';

import { config } from '@config/index';

import { FormLabel } from '@shared-components';

type MissionManager = GetAdminSettingsDto['managers'][number];

type MissionClientProps = {
  missionId: string;
  managers: MissionManager[];
};

const getClientAppMissionUrl = (missionId: string) => {
  return config.IS_PROD ? `https://client.a.team/mission/${missionId}` : `https://client-sandbox.a.team/mission/${missionId}`;
};

export const MissionClients: React.FC<MissionClientProps> = memo(({ missionId, managers }) => {
  const [showMoreThanFiveManagers, setShowMoreThanFiveManagers] = useState(false);

  const renderManager = (manager: MissionManager) => {
    const userInfo = manager.user;
    const fullName = `${userInfo.firstName} ${userInfo.lastName}`;

    if (!userInfo.profilePictureURL) {
      return (
        <>
          <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-full bg-purple-500 text-xs text-white">
            {manager.user.firstName[0]}
            {manager.user.lastName[0]}
          </div>
          <span className="font-medium text-gray-900">{fullName}</span>
        </>
      );
    }

    return (
      <>
        <Image src={userInfo.profilePictureURL} alt={fullName} className="mr-3 h-8 w-8 rounded-full object-cover" width={32} height={32} />
        <span className="font-medium text-gray-900">{fullName}</span>
      </>
    );
  };

  const renderManagers = () => {
    if (managers.length === 0) {
      return (
        <tr>
          <td className="flex items-center p-4 text-sm text-gray-500">No mission clients</td>
          <td className="px-4 py-4"></td>
        </tr>
      );
    }

    const managersToRender = !showMoreThanFiveManagers ? managers.slice(0, 5) : managers;

    return (
      <>
        {managersToRender.map((manager) => (
          <tr key={manager.username}>
            <td className="flex items-center p-4 text-sm">{renderManager(manager)}</td>
            <td className="p-4 text-sm text-gray-500">{manager.accessMode ?? 'N/A'}</td>
          </tr>
        ))}
      </>
    );
  };

  const onSeeAllManagersClick = () => {
    setShowMoreThanFiveManagers(true);
  };

  const clientAppUrl = getClientAppMissionUrl(missionId);

  const amountOfManagers = managers.length;
  const doMoreThanFiveManagersExist = amountOfManagers > 5;

  return (
    <div className="space-y-4 last:mt-4">
      <div className="flex items-center justify-between">
        <FormLabel className="text-base">Mission clients</FormLabel>
        <a href={clientAppUrl} target="_blank" className="text-xs text-indigo-600 hover:text-indigo-500">
          Manage users on Client app
        </a>
      </div>
      <div className="overflow-hidden rounded-lg border border-gray-400 bg-white shadow-sm">
        <table className="w-full">
          <thead>
            <tr className="bg-gray-50">
              <th className="w-2/3 p-4 text-left text-sm font-normal text-gray-600">Full name</th>
              <th className="w-1/3 p-4 text-left text-sm font-normal text-gray-600">Role</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-400">{renderManagers()}</tbody>
        </table>
      </div>
      {doMoreThanFiveManagersExist && !showMoreThanFiveManagers && (
        <div className="flex items-center hover:cursor-pointer" onClick={onSeeAllManagersClick}>
          <div className="pr-1 text-xs text-indigo-600 hover:text-indigo-500">See all {amountOfManagers} clients</div>
          <ChevronDown size={12} />
        </div>
      )}
    </div>
  );
});
