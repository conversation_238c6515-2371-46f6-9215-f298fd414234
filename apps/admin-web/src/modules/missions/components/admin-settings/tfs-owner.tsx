import Image from 'next/image';
import { useFormContext } from 'react-hook-form';

import { useGetTfsOwners } from '@modules/missions/rq/queries';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Skeleton,
} from '@shared-components';

import { AdminSettingsFormValues } from './admin-settings-form';

export const TfsOwner = () => {
  const { control, setValue } = useFormContext<AdminSettingsFormValues>();

  const { data: tfsOwners, isLoading } = useGetTfsOwners();

  if (isLoading || !tfsOwners) {
    return <Skeleton className="h-14 lg:w-1/2" />;
  }

  const onTfsOwnerClearClick = () => {
    setValue('tfsOwner', '');
  };

  return (
    <div className="flex w-full">
      <FormField
        control={control}
        name="tfsOwner"
        render={({ field }) => (
          <FormItem className="w-4/5 lg:w-1/2">
            <FormLabel>TFS owner</FormLabel>
            <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {tfsOwners.map((tfsOwner) => (
                  <SelectItem key={tfsOwner.id} value={tfsOwner.id}>
                    <div className="flex items-center">
                      {tfsOwner.profilePictureURL ? (
                        <Image
                          src={tfsOwner.profilePictureURL}
                          alt={tfsOwner.firstName}
                          className="mr-2 size-6 rounded-full object-cover"
                          width={24}
                          height={24}
                        />
                      ) : (
                        <div className="mr-2 flex size-6 items-center justify-center rounded-full bg-purple-500 text-xs text-white">
                          {tfsOwner.firstName[0]}
                        </div>
                      )}
                      <span>
                        {tfsOwner.firstName} {tfsOwner.lastName}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="mb-auto mt-10 w-1/5 pl-4 text-sm text-red-600 hover:cursor-pointer hover:text-red-500 lg:w-1/2" onClick={onTfsOwnerClearClick}>
        Clear
      </div>
    </div>
  );
};
