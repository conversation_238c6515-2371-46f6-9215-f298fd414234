'use client';

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from 'src/components/tabs';

import { AdminSettingsForm } from './admin-settings/admin-settings-form';
import { MissionSettingsForm } from './mission-settings/mission-settings-form';
import { RoleSettingContextProvider } from './role-settings/role-setting-context';
import RoleSettingsForm from './role-settings/role-settings-form';

type MissionSettingsProps = { missionId: string };

const MissionSettings: React.FC<MissionSettingsProps> = ({ missionId }) => {
  return (
    <div className="min-h-screen w-full">
      <Tabs defaultValue="roles" className="w-full">
        <TabsList className="mb-4 w-full justify-center sm:justify-start">
          <TabsTrigger value="roles" className="text-xs sm:text-base">
            Role Settings
          </TabsTrigger>
          <TabsTrigger value="mission" className="text-xs sm:text-base">
            Mission Settings
          </TabsTrigger>
          <TabsTrigger value="admin" className="text-xs sm:text-base">
            Admin Settings
          </TabsTrigger>
        </TabsList>
        <TabsContent value="roles" className="px-8">
          <RoleSettingContextProvider>
            <RoleSettingsForm missionId={missionId} />
          </RoleSettingContextProvider>
        </TabsContent>
        <TabsContent value="mission" className="px-8">
          <MissionSettingsForm missionId={missionId} />
        </TabsContent>
        <TabsContent value="admin" className="px-8">
          <AdminSettingsForm missionId={missionId} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default MissionSettings;
