import '@uploadcare/react-uploader/core.css';
import { FileUploaderRegular, UploadCtxProvider } from '@uploadcare/react-uploader';
import { useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { config } from '@config/index';

import { Button, FormDescription, FormField, FormItem, FormLabel, ImageIcon, Input, PencilIcon, TrashIcon } from '@shared-components';

import { CompanyVideoPlayer } from './company-video-player';
import { MissionSettingsFormValues } from './mission-settings-form';

const isValidUrl = (value: string) => {
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
};

export const CompanyVideo = () => {
  const [url, setUrl] = useState<string>('');

  const { control, setValue } = useFormContext<MissionSettingsFormValues>();

  const uploaderRef = useRef<InstanceType<UploadCtxProvider> | null>(null);

  const onUploadVideoClick = () => {
    if (uploaderRef.current) {
      const api = uploaderRef.current.getAPI();

      api.setCurrentActivity('start-from');
      api.setModalState(true);
    }
  };

  const onVideoUrlEnterClick = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();

      if (isValidUrl(url)) {
        setValue('videoUrl', url);
      }
    }
  };

  const onVideoRemoveClick = () => {
    setValue('videoUrl', null);
  };

  return (
    <FormField
      control={control}
      name="videoUrl"
      render={({ field }) => (
        <FormItem>
          <FormLabel>
            Video description <span className="text-gray-500">(Optional)</span>
          </FormLabel>
          <FormDescription>Got a video that can help us understand your mission better? Share the link here.</FormDescription>
          <div className="flex flex-col gap-2">
            {field.value ? (
              <div className="mt-2">
                <CompanyVideoPlayer url={field.value} />
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                <div className="cursor-pointer rounded-md border border-dashed p-8 text-center" onClick={onUploadVideoClick}>
                  <div className="flex flex-col items-center justify-center gap-2">
                    <div className="flex size-12 items-center justify-center rounded-full bg-purple-100">
                      <ImageIcon />
                    </div>
                    <div>
                      <p className="text-xs font-medium text-purple-500">Upload a video</p>
                      <p className="mt-1 text-xs text-gray-500">Max 150 MB.</p>
                    </div>
                  </div>
                </div>
                <div>
                  <FormLabel>
                    or add a video file url <span className="text-gray-500">(Optional)</span>
                  </FormLabel>
                  <Input
                    placeholder="Link to a video about your mission..."
                    className="mt-2"
                    onChange={(e) => setUrl(e.target.value)}
                    onKeyDown={onVideoUrlEnterClick}
                  />
                  <span className="text-muted-foreground text-xs">Supported: Youtube, Loom</span>
                </div>
              </div>
            )}
            {field.value && (
              <div className="flex w-full justify-center gap-2">
                <Button type="button" variant="outline" size="sm" className="size-7" onClick={onUploadVideoClick}>
                  <PencilIcon className="size-2 text-gray-600" />
                </Button>
                <Button type="button" variant="outline" size="sm" className="size-7" onClick={onVideoRemoveClick}>
                  <TrashIcon className="size-2 text-gray-600" />
                </Button>
              </div>
            )}
            <FileUploaderRegular
              headless
              accept="video/*"
              multiple={false}
              useCloudImageEditor={false}
              sourceList="local"
              pubkey={config.NEXT_PUBLIC_UPLOADCARE_API_KEY}
              apiRef={uploaderRef}
              onDoneClick={(event) => {
                if (event.successEntries.length > 0) {
                  field.onChange(event.successEntries[0].cdnUrl);
                }
              }}
            />
          </div>
        </FormItem>
      )}
    />
  );
};
