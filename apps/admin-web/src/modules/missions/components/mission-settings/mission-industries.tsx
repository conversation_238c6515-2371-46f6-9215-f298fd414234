import { Check, ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { useGetMissionIndustries } from '@modules/missions/rq/queries';

import { cn } from '@lib/utils';

import { Button, CommandItem, FormControl, FormItem, FormLabel, Skeleton } from '@shared-components';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandList } from '@shared-components';
import { Popover, PopoverContent, PopoverTrigger } from '@shared-components';

import { Tag } from '../tag';
import { MissionSettingsFormValues } from './mission-settings-form';

export const MissionIndustries = () => {
  const [open, setOpen] = useState(false);

  const { data: missionIndustries, isLoading } = useGetMissionIndustries();
  const { control } = useFormContext<MissionSettingsFormValues>();

  const {
    fields: selectedMissionIndustries,
    append,
    remove,
  } = useFieldArray({
    control,
    name: 'industries',
  });

  if (isLoading || !missionIndustries) {
    return <Skeleton className="h-14 lg:w-1/2" />;
  }

  const onRemoveIndustryClick = (index: number) => {
    remove(index);
  };

  const onIndustrySelect = (value: string) => {
    const selectedIndustry = missionIndustries.find((industry) => industry.name.toLowerCase() === value.toLowerCase());

    if (!selectedIndustry) {
      return;
    }

    const existingIndex = selectedMissionIndustries.findIndex((industry) => industry.name.toLowerCase() === selectedIndustry.name.toLowerCase());

    if (existingIndex !== -1) {
      remove(existingIndex);
    } else {
      append(selectedIndustry);
    }
  };

  const selectedIndustriesExist = selectedMissionIndustries.length > 0;

  return (
    <FormItem>
      <FormLabel>Mission industries</FormLabel>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button variant="outline" role="combobox" className="flex w-full justify-between sm:w-1/2" aria-expanded={open}>
              {selectedIndustriesExist ? `${selectedMissionIndustries.length} industries selected` : 'Select industries'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
          <Command>
            <CommandInput placeholder="Search industries..." />
            <CommandList>
              <CommandEmpty>No industries found.</CommandEmpty>
              <CommandGroup>
                {missionIndustries.map((industry) => (
                  <CommandItem className="justify-between" key={industry.id} value={industry.name} onSelect={onIndustrySelect}>
                    {industry.name}
                    <Check className={cn('mr-2 size-4', selectedMissionIndustries.some((v) => v.name === industry.name) ? 'opacity-100' : 'opacity-0')} />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {selectedIndustriesExist && (
        <div className="mt-2 flex w-full flex-wrap gap-2 sm:w-1/2">
          {selectedMissionIndustries.map((industry, index) => (
            <Tag key={industry.id} name={industry.name} onTagRemoveClick={() => onRemoveIndustryClick(index)} />
          ))}
        </div>
      )}
    </FormItem>
  );
};
