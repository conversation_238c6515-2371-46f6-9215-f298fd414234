import '@uploadcare/react-uploader/core.css';
import { UploadCtxProvider } from '@uploadcare/react-uploader';
import { FileUploaderRegular } from '@uploadcare/react-uploader/next';
import { useRef } from 'react';
import { useFormContext } from 'react-hook-form';

import { config } from '@config/index';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  FolderIcon,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  PencilIcon,
  TrashIcon,
} from '@shared-components';

import { MissionSettingsFormValues } from './mission-settings-form';

export const CompanyLogo = () => {
  const { control, setValue } = useFormContext<MissionSettingsFormValues>();

  const uploaderRef = useRef<InstanceType<UploadCtxProvider> | null>(null);

  const onChangeLogoClick = () => {
    if (uploaderRef.current) {
      const api = uploaderRef.current.getAPI();

      api.setCurrentActivity('start-from');
      api.setModalState(true);
    }
  };

  const onRemoveLogoClick = () => {
    setValue('companyLogo', null);
  };

  return (
    <FormField
      control={control}
      name="companyLogo"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-gray-1000">
            Company logo <span className="text-gray-500">(Optional)</span>
          </FormLabel>
          <FormDescription>Upload the logo of the company. The logo will be visible to everyone.</FormDescription>
          <FormControl>
            <div className="flex flex-col items-start gap-2">
              <div className="flex items-center gap-2">
                <Avatar className="h-16 w-16 rounded-xl">
                  <AvatarImage src={field.value ?? undefined} />
                  <AvatarFallback className="rounded-xl bg-gray-400">
                    <FolderIcon className="h-6 w-6 text-white" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex h-full flex-col space-y-1">
                  <Button type="button" variant="outline" size="sm" className="size-7" onClick={onChangeLogoClick}>
                    <PencilIcon className="size-1 text-gray-600" />
                  </Button>
                  {field.value && (
                    <Button type="button" variant="outline" size="sm" className="size-7" onClick={onRemoveLogoClick}>
                      <TrashIcon className="size-2 text-gray-600" />
                    </Button>
                  )}
                </div>
                <FileUploaderRegular
                  imgOnly
                  headless
                  useCloudImageEditor
                  sourceList="local"
                  multiple={false}
                  apiRef={uploaderRef}
                  pubkey={config.NEXT_PUBLIC_UPLOADCARE_API_KEY}
                  onDoneClick={(event) => {
                    if (event.successEntries.length > 0) {
                      field.onChange(event.successEntries[0].cdnUrl);
                    }
                  }}
                />
              </div>
            </div>
          </FormControl>
        </FormItem>
      )}
    />
  );
};
