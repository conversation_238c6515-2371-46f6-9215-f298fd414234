import { FieldError, useFieldArray, useFormContext } from 'react-hook-form';

import { cn } from '@lib/utils';

import { FormDescription, FormItem, FormLabel, FormMessage, Input } from '@shared-components';

import { MissionSettingsFormValues } from './mission-settings-form';

export const MissionResources = () => {
  const {
    control,
    register,
    formState: { errors },
  } = useFormContext<MissionSettingsFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'missionResources',
  });

  const onNewAttribution = () => {
    append({ title: '', url: '' });
  };

  const onRemoveAttribution = (index: number) => {
    remove(index);
  };

  const renderError = (error: FieldError | undefined) => {
    if (!error) {
      return null;
    }

    return <FormMessage className="mt-2">{error.message}</FormMessage>;
  };

  return (
    <FormItem>
      <FormLabel className="text-gray-1000">
        Mission resources <span className="text-gray-500">(Optional)</span>
      </FormLabel>
      <FormDescription>
        Can you think of anything that could help us understand the mission in more context? The company's website? A similar project?
      </FormDescription>
      <div className="mt-4">
        {fields.map((field, index) => {
          const fieldTitleError = errors.missionResources?.[index]?.title;
          const fieldUrlError = errors.missionResources?.[index]?.url;

          return (
            <div key={field.id} className="mb-3 space-y-1">
              <div className="flex gap-2">
                <div className="w-3/12">
                  <Input
                    placeholder="Title..."
                    className={cn(fieldTitleError && 'placeholder:text-red-600')}
                    {...register(`missionResources.${index}.title`)}
                  />
                  {renderError(fieldTitleError)}
                </div>
                <div className="w-8/12">
                  <Input placeholder="https://" className={cn(fieldUrlError && 'placeholder:text-red-600')} {...register(`missionResources.${index}.url`)} />
                  {renderError(fieldUrlError)}
                </div>
                <div className="mt-3 w-1/12 cursor-pointer text-xs text-red-600 hover:text-red-500" onClick={() => onRemoveAttribution(index)}>
                  Remove
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <div className="cursor-pointer pr-1 text-xs text-indigo-600 hover:text-indigo-500" onClick={onNewAttribution}>
        + Add New Attribution
      </div>
    </FormItem>
  );
};
