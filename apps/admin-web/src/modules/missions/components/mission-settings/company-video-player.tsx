import { useMemo } from 'react';

type CompanyVideoPlayerProps = {
  url: string;
};

export const CompanyVideoPlayer: React.FC<CompanyVideoPlayerProps> = ({ url }) => {
  const videoId = useMemo(() => {
    try {
      if (url.includes('youtube.com/watch')) {
        const urlParams = new URLSearchParams(new URL(url).search);
        return { platform: 'youtube', id: urlParams.get('v') };
      }

      if (url.includes('youtu.be/')) {
        const id = url.split('youtu.be/')[1].split('?')[0];
        return { platform: 'youtube', id };
      }

      if (url.includes('loom.com/share/')) {
        const id = url.split('loom.com/share/')[1].split('?')[0];
        return { platform: 'loom', id };
      }

      return { platform: 'unknown', id: url };
    } catch {
      return { platform: 'unknown', id: url };
    }
  }, [url]);

  if (!url) {
    return null;
  }

  switch (videoId.platform) {
    case 'youtube':
      return (
        <div className="aspect-video w-full overflow-hidden rounded-lg">
          <iframe
            src={`https://www.youtube.com/embed/${videoId.id}`}
            title="YouTube video player"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            className="h-full w-full"
          />
        </div>
      );
    case 'loom':
      return (
        <div className="aspect-video w-full overflow-hidden rounded-lg">
          <iframe src={`https://www.loom.com/embed/${videoId.id}`} title="Loom video player" allowFullScreen className="h-full w-full" />
        </div>
      );
    default:
      return (
        <div className="aspect-video w-full overflow-hidden rounded-lg">
          <video
            key={url}
            controls
            playsInline
            disableRemotePlayback
            disablePictureInPicture
            preload="metadata"
            crossOrigin="anonymous"
            className="size-full cursor-pointer rounded-md transition-opacity hover:opacity-80"
            controlsList="nodownload nofullscreen noremoteplayback"
          >
            <source src={url} type="video/mp4" />
            Your browser does not support mp4 video.
          </video>
        </div>
      );
  }
};
