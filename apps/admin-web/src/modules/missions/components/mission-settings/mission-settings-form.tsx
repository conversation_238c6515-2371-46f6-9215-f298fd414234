'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { GetMissionSettingsDto, UpdateMissionSettingsDto } from '@packages/contracts';
import { notFound } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { useUpdateMissionSettingsMutation } from '@modules/missions/rq/mutations';
import { useGetMissionSettings } from '@modules/missions/rq/queries';

import { Button, Checkbox, Form, FormControl, FormField, FormItem, FormLabel, Skeleton } from '@shared-components';

import { CompanyLogo } from './company-logo';
import { CompanyVideo } from './company-video';
import { MissionInformation } from './mission-information';
import { MissionResources } from './mission-resources';

type MissionSettingsFromProps = {
  missionId: string;
};

export type MissionSettingsFormValues = z.infer<typeof missionSettingsFormSchema>;

const missionSettingsFormSchema = z.object({
  companyLogo: z.string().nullable(),
  videoUrl: z.string().nullable(),
  missionName: z.string().min(1, { message: 'Mission name is required' }),
  companyDescription: z.string().default(''),
  missionDescription: z.string().min(1, { message: 'Mission description is required' }),
  isInternalMission: z.boolean().default(false),
  industries: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
      })
    )
    .default([]),
  duration: z.coerce
    .number()
    .min(1, { message: 'Duration must be between 1 and 24 months' })
    .max(24, { message: 'Duration must be between 1 and 24 months' })
    .default(1),
  isUnderWraps: z.boolean().default(false),
  missionResources: z.array(
    z.object({
      title: z.string().min(1, { message: 'Resource title is required' }),
      url: z.string().url({ message: 'Please enter a valid url' }),
    })
  ),
});

const getFormValues = (missionSettings: GetMissionSettingsDto): MissionSettingsFormValues => {
  return {
    companyLogo: missionSettings.companyLogo ?? null,
    videoUrl: missionSettings.videoUrl ?? null,
    missionName: missionSettings.missionName,
    companyDescription: missionSettings.companyDescription,
    missionDescription: missionSettings.missionDescription,
    isInternalMission: missionSettings.isInternalMission,
    industries: missionSettings.industries,
    duration: missionSettings.duration,
    isUnderWraps: missionSettings.isUnderWraps,
    missionResources: missionSettings.missionResources,
  };
};

export const MissionSettingsForm: React.FC<MissionSettingsFromProps> = ({ missionId }) => {
  const [formKey, setFormKey] = useState(0);

  const { data: missionSettings, isLoading } = useGetMissionSettings(missionId);
  const updateMissionSettingsMutation = useUpdateMissionSettingsMutation();

  const form = useForm<MissionSettingsFormValues>({
    resolver: zodResolver(missionSettingsFormSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (missionSettings) {
      form.reset(getFormValues(missionSettings));
      setFormKey((prev) => prev + 1);
    }
  }, [missionSettings, form]);

  const handleSubmit = (values: MissionSettingsFormValues) => {
    const settings: UpdateMissionSettingsDto = {
      companyLogo: values.companyLogo !== null ? values.companyLogo : undefined,
      videoUrl: values.videoUrl !== null ? values.videoUrl : undefined,
      missionName: values.missionName,
      companyDescription: values.companyDescription,
      missionDescription: values.missionDescription,
      isInternalMission: values.isInternalMission,
      industries: values.industries,
      duration: values.duration ?? 1,
      isUnderWraps: values.isUnderWraps,
      missionResources: values.missionResources,
    };

    updateMissionSettingsMutation.mutate(
      { missionId, settings },
      {
        onSuccess: () => {
          toast.success('Mission settings updated successfully');
        },
        onError: () => {
          toast.error('Failed to update mission settings');
        },
      }
    );
  };

  if (isLoading) {
    return (
      <div className="mx-auto my-4 max-w-5xl space-y-8">
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/5" />
          <Skeleton className="h-10 w-1/3" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/5" />
          <Skeleton className="h-10 w-1/3" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-1/3" />
        </div>
        <Skeleton className="h-72" />
      </div>
    );
  }

  if (!missionSettings) {
    return notFound();
  }

  return (
    <Form {...form} key={formKey}>
      <form
        className="mx-auto max-w-5xl space-y-8 pb-4"
        onSubmit={(e) => {
          e.preventDefault();
          void form.handleSubmit(handleSubmit)(e);
        }}
      >
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">Mission settings</h1>
          <p className="text-xs text-gray-500">This section provides information related to the mission details.</p>
        </div>
        <div className="space-y-6">
          <CompanyLogo />
          <CompanyVideo />
          <MissionInformation />
          <MissionResources />
          <FormField
            control={form.control}
            name="isUnderWraps"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <FormLabel className="!my-auto">This mission is under wraps! Only people with the direct link can see this mission.</FormLabel>
              </FormItem>
            )}
          />
        </div>
        <Button type="submit" variant="purple" className="mx-auto flex px-8">
          Save changes
        </Button>
      </form>
    </Form>
  );
};
