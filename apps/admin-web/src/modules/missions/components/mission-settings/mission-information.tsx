import { useFormContext } from 'react-hook-form';

import { Checkbox, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage, Input, RichTextEditor } from '@shared-components';

import { MissionIndustries } from './mission-industries';
import { MissionSettingsFormValues } from './mission-settings-form';

export const MissionInformation = () => {
  const { control } = useFormContext<MissionSettingsFormValues>();

  return (
    <>
      <FormField
        control={control}
        name="missionName"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Let's name this mission</FormLabel>
            <FormControl>
              <Input placeholder="Name your mission..." value={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="companyDescription"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Long company description</FormLabel>
            <FormDescription>Long-form description of the company, added to the text body on the mission detail page.</FormDescription>
            <FormControl>
              <RichTextEditor placeholder="Describe your company..." value={field.value ?? ''} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="missionDescription"
        render={({ field }) => (
          <FormItem>
            <FormLabel>What's the mission?</FormLabel>
            <FormDescription>
              Give us some details about this mission! What kind of work is needed? What type of team do you have in mind? Key objectives? The more specific,
              the better!
            </FormDescription>
            <FormControl>
              <RichTextEditor placeholder="Describe your mission..." value={field.value} onChange={field.onChange} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name="isInternalMission"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <FormLabel className="!my-auto">This is an A.Team internal mission.</FormLabel>
          </FormItem>
        )}
      />
      <MissionIndustries />
      <FormField
        control={control}
        name="duration"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Mission duration in months</FormLabel>
            <div className="flex items-center gap-2">
              <FormControl>
                <Input type="number" min={1} className="max-w-24" onChange={field.onChange} value={field.value ?? 1} />
              </FormControl>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};
