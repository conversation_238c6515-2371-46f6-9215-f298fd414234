import React, { createContext, ReactNode, useContext, useState } from 'react';

export type RoleFilterStatus = 'openRoles' | 'activeRoles' | 'endedRoles' | 'canceledRoles';

type RoleSettingContextType = {
  selectedRoleIndex: number;
  setSelectedRoleIndex: (index: number) => void;
  filterStatus: RoleFilterStatus;
  setFilterStatus: (filterStatus: RoleFilterStatus) => void;
};

const RoleSettingContext = createContext<RoleSettingContextType>({} as RoleSettingContextType);

export const RoleSettingContextProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [selectedRoleIndex, setSelectedRoleIndex] = useState<number>(0);
  const [filterStatusValue, setFilterStatusValue] = useState<RoleFilterStatus>('openRoles');

  const setFilterStatus = (filterStatus: RoleFilterStatus) => {
    setSelectedRoleIndex(0);
    setFilterStatusValue(filterStatus);
  };

  return (
    <RoleSettingContext.Provider value={{ selectedRoleIndex, setSelectedRoleIndex, filterStatus: filterStatusValue, setFilterStatus }}>
      {children}
    </RoleSettingContext.Provider>
  );
};

export const useRoleSettingContext = (): RoleSettingContextType => {
  const context = useContext(RoleSettingContext);

  if (!context) {
    throw new Error('useRoleSettingContext must be used within a RoleSettingContextProvider');
  }

  return context;
};
