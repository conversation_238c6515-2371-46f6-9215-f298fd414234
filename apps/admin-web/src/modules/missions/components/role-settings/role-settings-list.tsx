import { MISSION_ROLE_STATUSES, MissionRoleStatus, MissionStatus } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { getAvailableRoleStatusOptions } from '@modules/missions/helpers/role';

import { cn } from '@lib/utils';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@shared-components';

import { Divider } from '../divider';
import { AssignRole } from './assign-role/assign-role';
import { PublishRole } from './publish-role/publish-role';
import { useRoleSettingContext } from './role-setting-context';
import { RoleSettingsFormValues } from './role-settings-form';

type RoleSettingsListProps = {
  missionStatus: MissionStatus;
};

export const RoleSettingsList: React.FC<RoleSettingsListProps> = ({ missionStatus }) => {
  const { selectedRoleIndex, setSelectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { setValue, watch } = useFormContext<RoleSettingsFormValues>();

  const roleSettings = watch(filterStatus) ?? [];
  const selectedRole = typeof selectedRoleIndex === 'number' ? roleSettings[selectedRoleIndex] : undefined;

  const onRoleClick = (index: number) => {
    setSelectedRoleIndex(index);
  };

  const onRoleStatusChange = (status: string) => {
    const missionRoleStatus = status as MissionRoleStatus;

    // onValueChange from select tends to trigger with an empty string
    // once Select gets re-rendered (example below with selectedRole rendering)
    // hence we check if an actual expected status value is being passed in or not
    if (!MISSION_ROLE_STATUSES.includes(missionRoleStatus)) {
      return;
    }

    setValue(`${filterStatus}.${selectedRoleIndex!}.status`, missionRoleStatus);
  };

  if (roleSettings.length === 0) {
    return <div className="mt-10 text-center text-base font-bold text-gray-500">No roles found</div>;
  }

  return (
    <div className="flex">
      <div className="w-1/3 rounded-xl bg-gray-100 p-2">
        {roleSettings.map((role, index) => (
          <div
            key={role.rid}
            className={cn('mx-2 mb-2 cursor-pointer rounded-md p-4 text-base', selectedRoleIndex === index && 'bg-purple-300 text-purple-600')}
            onClick={() => onRoleClick(index)}
          >
            <h3 className="text-base font-medium">{role.title}</h3>
          </div>
        ))}
      </div>
      <div className="w-2/3 p-2">
        {selectedRole && (
          <>
            <div className="p-4">
              <h3 className="text-base font-medium">Role status</h3>
              <div className="mt-2">
                <Select value={selectedRole.status} onValueChange={(status) => onRoleStatusChange(status)}>
                  <SelectTrigger className="w-5/6">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    {getAvailableRoleStatusOptions(missionStatus, selectedRole.status).map((status) => (
                      <SelectItem key={status} value={status}>
                        {status}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Divider />
            <div className="rounded-md">
              <Accordion type="multiple" className="w-full">
                <AccordionItem value="publish-role" className="border-none">
                  <AccordionTrigger className="p-4">
                    <div className="w-full text-left">
                      <h3 className="text-base font-medium">Publish a role</h3>
                      <p className="text-left text-xs text-gray-600">This section is used to publish or edit a role.</p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="p-4">
                    <PublishRole />
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem value="assign-role" className="border-none">
                  <AccordionTrigger className="p-4">
                    <div className="w-full text-left">
                      <h3 className="text-base font-medium">Assign a role</h3>
                      <p className="text-left text-xs text-gray-600">This section is used to assign a builder.</p>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="p-4">
                    <AssignRole />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
