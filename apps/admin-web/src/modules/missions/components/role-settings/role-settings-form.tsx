import { zodResolver } from '@hookform/resolvers/zod';
import { GetRoleSettingsDto, roleSettingDtoSchema } from '@packages/contracts';
import { notFound } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from 'src/components/tabs';
import { z } from 'zod';

import { transformRoleSettingDtoToRoleSettingForm } from '@modules/missions/helpers/transformers';
import { useGetRoleSettings } from '@modules/missions/rq/queries';

import { Button, Form, Skeleton } from '@shared-components';

import { RoleFilterStatus, useRoleSettingContext } from './role-setting-context';
import { RoleSettingsList } from './role-settings-list';

type RoleSettingsFormProps = {
  missionId: string;
};

export type RoleSettingsFormValues = z.infer<typeof roleSettingsFormSchema>;
export type RoleSettingFormValues = z.infer<typeof roleSettingFormSchema>;

const roleSettingFormSchema = roleSettingDtoSchema
  .omit({
    customQuestion: true,
    workingHoursStartTime: true,
    workingHoursEndTime: true,
    hoursOverlapMinutes: true,
    allowedCountries: true,
  })
  .extend({
    customQuestionQid: z.string().optional(),
    customQuestionText: z.string().optional(),
    customQuestionIsVisible: z.boolean().default(false),
    customQuestionIsRequired: z.boolean().default(false),
    workingHoursStartTime: z.string().optional(),
    workingHoursEndTime: z.string().optional(),
    hoursOverlap: z.string().optional(),

    // react-hook-form doesn't work nice with string arrays
    // hence why there is a dedicated array with code/object in the form schema
    allowedCountries: z.array(z.object({ code: z.string() })),
  });

const roleSettingsFormSchema = z.object({
  openRoles: z.array(roleSettingFormSchema),
  activeRoles: z.array(roleSettingFormSchema),
  endedRoles: z.array(roleSettingFormSchema),
  canceledRoles: z.array(roleSettingFormSchema),
});

const getFormValues = (roleSettings: GetRoleSettingsDto): RoleSettingsFormValues => {
  return {
    openRoles: roleSettings.openRoles.map(transformRoleSettingDtoToRoleSettingForm),
    activeRoles: roleSettings.activeRoles.map(transformRoleSettingDtoToRoleSettingForm),
    endedRoles: roleSettings.endedRoles.map(transformRoleSettingDtoToRoleSettingForm),
    canceledRoles: roleSettings.canceledRoles.map(transformRoleSettingDtoToRoleSettingForm),
  };
};

const RoleSettingsForm: React.FC<RoleSettingsFormProps> = ({ missionId }) => {
  const [formKey, setFormKey] = useState(0);

  const { filterStatus, setFilterStatus } = useRoleSettingContext();

  const { data: roleSettings, isLoading } = useGetRoleSettings(missionId);

  const form = useForm<RoleSettingsFormValues>({
    resolver: zodResolver(roleSettingsFormSchema),
    defaultValues: {},
  });

  useEffect(() => {
    if (roleSettings) {
      form.reset(getFormValues(roleSettings));
      setFormKey((prev) => prev + 1);
    }
  }, [roleSettings, form]);

  const handleSubmit = (values: RoleSettingsFormValues) => {
    // eslint-disable-next-line no-console
    console.log(values);

    // TODOS:
    // - update client rate based from the builder rate on the backend, calculation
    // - convert working hours start time, end time & hours overlap into minutes
    // - convert/map the allowed countries to the required array/code format
  };

  if (isLoading) {
    return (
      <div className="mx-auto my-4 max-w-5xl space-y-8">
        <div className="space-y-4">
          <Skeleton className="h-10 w-1/3" />
          <Skeleton className="h-10 w-1/2" />
        </div>
        <div className="flex gap-4">
          <Skeleton className="h-10 w-1/4" />
          <Skeleton className="h-10 w-1/4" />
          <Skeleton className="h-10 w-1/4" />
          <Skeleton className="h-10 w-1/4" />
        </div>
        <div className="flex gap-4">
          <Skeleton className="h-64 w-1/3" />
          <Skeleton className="h-64 w-2/3" />
        </div>
      </div>
    );
  }

  if (!roleSettings) {
    return notFound();
  }

  const openRolesCount = roleSettings.openRoles.length;
  const activeRolesCount = roleSettings.activeRoles.length;
  const endedRolesCount = roleSettings.endedRoles.length;
  const canceledRolesCount = roleSettings.canceledRoles.length;

  const missionStatus = roleSettings.missionStatus;

  return (
    <Form {...form} key={formKey}>
      <form
        className="mx-auto max-w-5xl space-y-8 pb-4"
        onSubmit={(e) => {
          e.preventDefault();
          void form.handleSubmit(handleSubmit)(e);
        }}
      >
        <div className="space-y-4">
          <div>
            <h2 className="text-2xl font-bold">Role settings</h2>
            <p className="text-muted-foreground">This section provides information related to the role details.</p>
          </div>
          <Tabs defaultValue={filterStatus} className="w-full" onValueChange={(status) => setFilterStatus(status as RoleFilterStatus)}>
            <TabsList className="w-full">
              <TabsTrigger value="openRoles">Open roles ({openRolesCount})</TabsTrigger>
              <TabsTrigger value="activeRoles">Active roles ({activeRolesCount})</TabsTrigger>
              <TabsTrigger value="endedRoles">Ended roles ({endedRolesCount})</TabsTrigger>
              <TabsTrigger value="canceledRoles">Canceled roles ({canceledRolesCount})</TabsTrigger>
            </TabsList>
            <TabsContent value="openRoles">
              <RoleSettingsList missionStatus={missionStatus} />
            </TabsContent>
            <TabsContent value="activeRoles">
              <RoleSettingsList missionStatus={missionStatus} />
            </TabsContent>
            <TabsContent value="endedRoles">
              <RoleSettingsList missionStatus={missionStatus} />
            </TabsContent>
            <TabsContent value="canceledRoles">
              <RoleSettingsList missionStatus={missionStatus} />
            </TabsContent>
          </Tabs>
        </div>
        <Button type="submit" variant="purple" className="mx-auto flex px-8">
          Save changes
        </Button>
      </form>
    </Form>
  );
};

export default RoleSettingsForm;
