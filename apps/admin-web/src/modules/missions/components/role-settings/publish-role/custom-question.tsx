import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { Checkbox, FormField, FormItem, FormLabel, Switch, Textarea } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleCustomQuestion = () => {
  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <>
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.customQuestionText`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Add a question for builders</FormLabel>
            <Textarea className="mb-4 resize-none" value={field.value} onChange={field.onChange} />
          </FormItem>
        )}
      />
      <div className="flex justify-start gap-2">
        <FormField
          control={control}
          name={`${filterStatus}.${selectedRoleIndex}.customQuestionIsVisible`}
          render={({ field }) => (
            <FormItem className="flex gap-2 space-y-0 border-r pr-4">
              <Switch id="custom-question-is-visible" checked={field.value} onCheckedChange={field.onChange} />
              <label htmlFor="custom-question-is-visible" className="!my-auto text-xs">
                Include question
              </label>
            </FormItem>
          )}
        />
        <FormField
          control={control}
          name={`${filterStatus}.${selectedRoleIndex}.customQuestionIsRequired`}
          render={({ field }) => (
            <FormItem className="ml-2 flex gap-2 space-y-0">
              <Checkbox id="custom-question-is-required" className="!my-auto" checked={field.value} onCheckedChange={field.onChange} />
              <label htmlFor="custom-question-is-required" className="!my-auto text-xs">
                The answer is mandatory
              </label>
            </FormItem>
          )}
        />
      </div>
    </>
  );
};
