import { useFormContext } from 'react-hook-form';

import { FormField, FormItem, FormLabel, Switch } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingFormValues, RoleSettingsFormValues } from '../role-settings-form';

type PublishRoleRateRangeSettingsProps = {
  collectBuilderRateProperty: keyof Pick<RoleSettingFormValues, 'collectBuilderHourlyRate' | 'collectBuilderMonthlyRate'>;
  showClientBudgetProperty: keyof Pick<RoleSettingFormValues, 'showClientHourlyBudget' | 'showClientMonthlyBudget'>;
};

export const PublishRoleRateRangeSettings: React.FC<PublishRoleRateRangeSettingsProps> = ({ collectBuilderRateProperty, showClientBudgetProperty }) => {
  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control, getValues } = useFormContext<RoleSettingsFormValues>();

  const colectBuilderRateChecked = getValues(`${filterStatus}.${selectedRoleIndex}.${collectBuilderRateProperty}`);

  const rateKeyword = collectBuilderRateProperty === 'collectBuilderHourlyRate' ? 'hourly' : 'monthly';

  return (
    <div className="flex w-full flex-col justify-between space-y-2">
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.${collectBuilderRateProperty}`}
        render={({ field }) => (
          <FormItem className="flex gap-4">
            <Switch checked={field.value} onCheckedChange={field.onChange} />
            <FormLabel className="my-auto">Collect {rateKeyword} rate on application</FormLabel>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.${showClientBudgetProperty}`}
        render={({ field }) => (
          <FormItem className="flex gap-4 space-y-2">
            <Switch checked={field.value} onCheckedChange={field.onChange} disabled={!colectBuilderRateChecked} />
            <FormLabel>Show client's {rateKeyword} budget</FormLabel>
          </FormItem>
        )}
      />
    </div>
  );
};
