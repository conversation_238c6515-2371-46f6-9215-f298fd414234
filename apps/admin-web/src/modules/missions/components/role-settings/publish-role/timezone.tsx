import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import timezones from '@lib/constants/timezones';

import {
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleTimezone = () => {
  const [open, setOpen] = useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <FormField
      control={control}
      name={`${filterStatus}.${selectedRoleIndex}.timezone`}
      render={({ field }) => (
        <FormItem className="flex flex-1 flex-col sm:items-start">
          <FormLabel>Timezone</FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button variant="outline" role="combobox" className="flex w-5/6 justify-between" aria-expanded={open}>
                  {field.value ?? 'Select timezone'}
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
              <Command>
                <CommandInput placeholder="Search timezone..." />
                <CommandList>
                  <CommandEmpty>No timezone found.</CommandEmpty>
                  <CommandGroup className="px-2">
                    {timezones.map((timezone) => (
                      <CommandItem
                        className="justify-between"
                        key={timezone.tzCode}
                        value={timezone.label}
                        onSelect={() => {
                          field.onChange(timezone.tzCode);
                          setOpen(false);
                        }}
                      >
                        {timezone.label}
                        <Check className={field.value === timezone.tzCode ? 'opacity-100' : 'opacity-0'} />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
