import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { HOUR_OPTIONS_12_HOUR_FORMAT } from '@modules/missions/helpers/working-hours';

import {
  Button,
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleWorkingHours = () => {
  const [startTimeOpen, setStartTimeOpen] = useState(false);
  const [endTimeOpen, setEndTimeOpen] = useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <div className="flex w-5/6 justify-between">
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.workingHoursStartTime`}
        render={({ field }) => (
          <FormItem className="flex flex-1 flex-col sm:items-start">
            <FormLabel>Working hours start</FormLabel>
            <Popover open={startTimeOpen} onOpenChange={setStartTimeOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button variant="outline" role="combobox" className="flex w-full justify-between" aria-expanded={startTimeOpen}>
                    {field.value ?? <span className="text-gray-500">Start time</span>}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandList>
                    <CommandGroup className="px-2">
                      {HOUR_OPTIONS_12_HOUR_FORMAT.map((hour) => (
                        <CommandItem
                          className="justify-between"
                          key={hour}
                          value={hour}
                          onSelect={() => {
                            field.onChange(hour);
                            setStartTimeOpen(false);
                          }}
                        >
                          {hour}
                          <Check className={field.value === hour ? 'opacity-100' : 'opacity-0'} />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
      <span className="my-auto w-1/12 pt-5 text-center">-</span>
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.workingHoursEndTime`}
        render={({ field }) => (
          <FormItem className="flex flex-1 flex-col sm:items-start">
            <FormLabel>Working hours end</FormLabel>
            <Popover open={endTimeOpen} onOpenChange={setEndTimeOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button variant="outline" role="combobox" className="flex w-full justify-between" aria-expanded={endTimeOpen}>
                    {field.value ?? <span className="text-gray-500">End time</span>}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
                <Command>
                  <CommandList>
                    <CommandGroup className="px-2">
                      {HOUR_OPTIONS_12_HOUR_FORMAT.map((hour) => (
                        <CommandItem
                          className="justify-between"
                          key={hour}
                          value={hour}
                          onSelect={() => {
                            field.onChange(hour);
                            setEndTimeOpen(false);
                          }}
                        >
                          {hour}
                          <Check className={field.value === hour ? 'opacity-100' : 'opacity-0'} />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
