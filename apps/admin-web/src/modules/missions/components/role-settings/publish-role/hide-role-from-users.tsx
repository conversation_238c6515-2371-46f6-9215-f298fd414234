import { Check, X } from 'lucide-react';
import Image from 'next/image';
import { ChangeEventHandler, useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { useGetMissionUsersToExclude } from '@modules/missions/rq/queries';

import { useDebounce } from '@lib/hooks/use-debounce';
import { cn } from '@lib/utils';

import { CommandGroup, CommandItem, FormItem, FormLabel, Input, Skeleton } from '@shared-components';
import { Command, CommandEmpty, CommandList } from '@shared-components';
import { Popover, PopoverContent, PopoverTrigger } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleHideRoleFromUsers = () => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  const debouncedSearchTerm = useDebounce(searchTerm);

  const { data: searchedUsers, isLoading } = useGetMissionUsersToExclude(debouncedSearchTerm.trim());

  const {
    fields: selectedUsersToBeExcluded,
    append,
    remove,
  } = useFieldArray({
    control,
    name: `${filterStatus}.${selectedRoleIndex}.roleHiddenFromBuilders`,
    keyName: '_id',
  });

  const onRemoveUserClick = (index: number) => {
    remove(index);
  };

  const onBuilderSelect = (value: string) => {
    const selectedUser = searchedUsers!.find((user) => user.name.toLowerCase() === value.toLowerCase());

    if (!selectedUser) {
      return;
    }

    const existingIndex = selectedUsersToBeExcluded.findIndex((user) => user.id === selectedUser.id);

    if (existingIndex !== -1) {
      remove(existingIndex);
    } else {
      append(selectedUser);
    }
  };

  const handleInputChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    const value = e.target.value;

    setSearchTerm(value);
    setOpen(value.length > 0);
  };

  const selectedIndustriesExist = selectedUsersToBeExcluded.length > 0;
  const shouldShowDropdown = open && !isLoading && !!searchedUsers;

  return (
    <FormItem className="flex flex-col">
      <FormLabel>Hide role from users</FormLabel>
      <Popover open={shouldShowDropdown} onOpenChange={setOpen}>
        <PopoverTrigger className="w-5/6" onClick={(e) => e.preventDefault()}>
          <Input value={searchTerm} onChange={handleInputChange} placeholder="Search builders.." />
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" onOpenAutoFocus={(e) => e.preventDefault()}>
          <Command>
            <CommandList>
              <CommandEmpty>No users found.</CommandEmpty>
              <CommandGroup>
                {isLoading || !searchedUsers ? (
                  <CommandItem>
                    <Skeleton className="h-14 w-full lg:w-1/2" />
                  </CommandItem>
                ) : (
                  <>
                    {searchedUsers.map((user) => (
                      <CommandItem className="justify-between" key={user.id} value={user.name} onSelect={onBuilderSelect}>
                        <div className="flex items-center">
                          {user.pictureUrl ? (
                            <Image src={user.pictureUrl} alt={user.name} className="mr-2 size-6 rounded-full object-cover" width={24} height={24} />
                          ) : (
                            <div className="mr-2 flex size-6 items-center justify-center rounded-full bg-purple-500 text-xs text-white">{user.name[0]}</div>
                          )}
                          <span>{user.name}</span>
                        </div>
                        <Check className={cn('mr-2 size-4', selectedUsersToBeExcluded.some((u) => u.id === user.id) ? 'opacity-100' : 'opacity-0')} />
                      </CommandItem>
                    ))}
                  </>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {selectedIndustriesExist && (
        <div className="mt-4 flex w-5/6 flex-wrap gap-2">
          {selectedUsersToBeExcluded.map((user, index) => (
            <div key={user.id}>
              <div className="flex items-center rounded-xl bg-gray-400 px-3 py-1 text-xs hover:bg-gray-300">
                {user.pictureUrl ? (
                  <Image src={user.pictureUrl} alt={user.name} className="mr-2 size-6 rounded-full object-cover" width={24} height={24} />
                ) : (
                  <div className="mr-2 flex size-6 items-center justify-center rounded-full bg-purple-500 text-xs text-white">{user.name[0]}</div>
                )}
                <span>{user.name}</span>
                <button type="button" className="ml-1" onClick={() => onRemoveUserClick(index)}>
                  <X className="size-3" />
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </FormItem>
  );
};
