import { Check, ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { Tag } from '@modules/missions/components/tag';

import { COUNTRIES, COUNTRIES_CODE_NAME_MAP } from '@lib/constants/countries';
import { cn } from '@lib/utils';

import { Button, CommandItem, FormControl, FormItem, FormLabel } from '@shared-components';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandList } from '@shared-components';
import { Popover, PopoverContent, PopoverTrigger } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleAllowedCountries = () => {
  const [open, setOpen] = useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  const {
    fields: selectedCountries,
    append,
    remove,
  } = useFieldArray({
    control,
    name: `${filterStatus}.${selectedRoleIndex}.allowedCountries`,
  });

  const onRemoveCountryClick = (index: number) => {
    remove(index);
  };

  const onCountrySelect = (countryName: string) => {
    const selectedCountry = COUNTRIES.find((country) => country.name === countryName);

    if (!selectedCountry) {
      return;
    }

    const existingIndex = selectedCountries.findIndex((country) => country.code === selectedCountry.code);

    if (existingIndex !== -1) {
      remove(existingIndex);
    } else {
      append({ code: selectedCountry.code });
    }
  };

  const onClearCountriesClick = () => {
    remove();
  };

  const selectedCountriesExist = selectedCountries.length > 0;

  return (
    <FormItem>
      <FormLabel>Allowed countries</FormLabel>
      <div className="flex w-full">
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>
            <FormControl className="justify-start">
              <Button variant="outline" role="combobox" className="flex w-5/6 justify-between" aria-expanded={open}>
                <span>
                  Select countries
                  <span className="ml-2 rounded-xl bg-gray-400 px-2 py-1">
                    {selectedCountries.length} / {COUNTRIES.length}
                  </span>
                </span>
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </FormControl>
          </PopoverTrigger>
          <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
            <Command>
              <CommandInput placeholder="Search countries" />
              <CommandList>
                <CommandEmpty>No countries found.</CommandEmpty>
                <CommandGroup>
                  {COUNTRIES.map((country) => (
                    <CommandItem className="justify-between" key={country.code} value={country.name} onSelect={onCountrySelect}>
                      {country.name}
                      <Check className={cn('mr-2 size-4', selectedCountries.some((s) => s.code === country.code) ? 'opacity-100' : 'opacity-0')} />
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <div className="my-auto w-1/6 pl-4 text-sm text-red-600 hover:cursor-pointer hover:text-red-500" onClick={onClearCountriesClick}>
          Clear
        </div>
      </div>
      {selectedCountriesExist && (
        <div className="mt-2 flex w-5/6 flex-wrap gap-2">
          {selectedCountries.map((country, index) => (
            <Tag key={country.code} name={COUNTRIES_CODE_NAME_MAP[country.code]} onTagRemoveClick={() => onRemoveCountryClick(index)} />
          ))}
        </div>
      )}
    </FormItem>
  );
};
