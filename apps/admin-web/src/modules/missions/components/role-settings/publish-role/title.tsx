import { Check, ChevronDown } from 'lucide-react';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { useGetMissionRoleCategories } from '@modules/missions/rq/queries';

import { cn } from '@lib/utils';

import { Button, FormField, FormItem, FormLabel, Skeleton } from '@shared-components';
import { Popover, PopoverContent, PopoverTrigger } from '@shared-components';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleTitle = () => {
  const [open, setOpen] = React.useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control, setValue } = useFormContext<RoleSettingsFormValues>();

  const { data: roleCategories, isLoading, isError } = useGetMissionRoleCategories();

  if (isLoading || !roleCategories) {
    return <Skeleton className="h-14 lg:w-1/2" />;
  }

  const formPropertyKeyword = `${filterStatus}.${selectedRoleIndex}.title` as const;

  const onRoleTitleSelect = (title: string) => {
    setValue(formPropertyKeyword, title);
    setOpen(false);
  };

  return (
    <FormField
      control={control}
      name={formPropertyKeyword}
      render={({ field }) => (
        <FormItem className="flex w-full flex-col">
          <FormLabel>Role title</FormLabel>
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" role="combobox" className="w-5/6 justify-between" aria-expanded={open} disabled={isLoading || isError}>
                {field.value}
                <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" side="bottom" align="start" sideOffset={4}>
              <Command>
                <CommandInput placeholder="Search role category..." />
                <CommandList>
                  <CommandEmpty>No role category found.</CommandEmpty>
                  <CommandGroup>
                    {roleCategories.map((category) => (
                      <CommandItem className="justify-between" key={category.id} value={category.title} onSelect={onRoleTitleSelect}>
                        {category.title}
                        <Check className={cn('mr-2 size-4', category.title === field.value ? 'opacity-100' : 'opacity-0')} />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </FormItem>
      )}
    />
  );
};
