import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { OVERLAP_HOURS_OPTIONS } from '@modules/missions/helpers/working-hours';

import {
  Button,
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';

export const PublishRoleWorkingHoursOverlap = () => {
  const [overlapHoursOpen, setOverlapHoursOpen] = useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <FormField
      control={control}
      name={`${filterStatus}.${selectedRoleIndex}.hoursOverlap`}
      render={({ field }) => (
        <FormItem className="flex flex-1 flex-col sm:items-start">
          <FormLabel>Hours overlap</FormLabel>
          <Popover open={overlapHoursOpen} onOpenChange={setOverlapHoursOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button variant="outline" role="combobox" className="flex w-5/6 justify-between" aria-expanded={overlapHoursOpen}>
                  {field.value ?? 'Select hours overlap'}
                  <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
              <Command>
                <CommandList>
                  <CommandGroup className="px-2">
                    {OVERLAP_HOURS_OPTIONS.map((hour) => (
                      <CommandItem
                        className="justify-between"
                        key={`hours-overlap-${hour}`}
                        value={hour}
                        onSelect={() => {
                          field.onChange(hour);
                          setOverlapHoursOpen(false);
                        }}
                      >
                        {hour}
                        <Check className={field.value === hour ? 'opacity-100' : 'opacity-0'} />
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
