import { Check, ChevronsUpDown } from 'lucide-react';
import { useState } from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { Tag } from '@modules/missions/components/tag';
import { useGetMissionRoleSkills } from '@modules/missions/rq/queries';

import { cn } from '@lib/utils';

import { Button, CommandItem, FormControl, FormItem, FormLabel, Skeleton } from '@shared-components';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandList } from '@shared-components';
import { Popover, PopoverContent, PopoverTrigger } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingFormValues, RoleSettingsFormValues } from '../role-settings-form';

type PublishRoleSkillsProps = {
  skillsProperty: keyof Pick<RoleSettingFormValues, 'requiredSkills' | 'preferredSkills'>;
};

export const PublishRoleSkills: React.FC<PublishRoleSkillsProps> = ({ skillsProperty }) => {
  const [open, setOpen] = useState(false);

  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { data: missionRoleSkills, isLoading } = useGetMissionRoleSkills();
  const { control } = useFormContext<RoleSettingsFormValues>();

  const {
    fields: selectedSkills,
    append,
    remove,
  } = useFieldArray({
    control,
    name: `${filterStatus}.${selectedRoleIndex}.${skillsProperty}`,
  });

  if (isLoading || !missionRoleSkills) {
    return <Skeleton className="h-14 lg:w-1/2" />;
  }

  const onRemoveSkillClick = (index: number) => {
    remove(index);
  };

  const onSkillSelect = (value: string) => {
    const selectedSkill = missionRoleSkills.find((skill) => skill.name.toLowerCase() === value.toLowerCase());

    if (!selectedSkill) {
      return;
    }

    const existingIndex = selectedSkills.findIndex((skill) => skill.name.toLowerCase() === selectedSkill.name.toLowerCase());

    if (existingIndex !== -1) {
      remove(existingIndex);
    } else {
      append(selectedSkill);
    }
  };

  const selectedSkillsExist = selectedSkills.length > 0;

  const skillsType = skillsProperty === 'requiredSkills' ? 'Required' : 'Preferred';
  const skillsTypeLowered = skillsType.toLowerCase();

  return (
    <FormItem>
      <FormLabel>{skillsType} skills</FormLabel>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button variant="outline" role="combobox" className="flex w-5/6 justify-between" aria-expanded={open}>
              {selectedSkillsExist ? `${selectedSkills.length} skills selected` : 'Select industries'}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
          <Command>
            <CommandInput placeholder={`Search ${skillsTypeLowered} skills`} />
            <CommandList>
              <CommandEmpty>No skills found.</CommandEmpty>
              <CommandGroup>
                {missionRoleSkills.map((skill) => (
                  <CommandItem className="justify-between" key={skill.id} value={skill.name} onSelect={onSkillSelect}>
                    {skill.name}
                    <Check className={cn('mr-2 size-4', selectedSkills.some((s) => s.name === skill.name) ? 'opacity-100' : 'opacity-0')} />
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {selectedSkillsExist && (
        <div className="mt-2 flex w-5/6 flex-wrap gap-2">
          {selectedSkills.map((skill, index) => (
            <Tag key={skill.id} name={skill.name} onTagRemoveClick={() => onRemoveSkillClick(index)} />
          ))}
        </div>
      )}
    </FormItem>
  );
};
