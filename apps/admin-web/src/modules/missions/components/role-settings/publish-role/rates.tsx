import { FormLabel } from '@shared-components';

import { Divider } from '../../divider';
import { PublishRoleRateRanges } from './rate-range';
import { PublishRoleRateRangeSettings } from './rate-range-settings';

export const PublishRoleRates = () => {
  return (
    <div className="space-y-2">
      <FormLabel>Rates</FormLabel>
      <div className="border-1 space-y-4 rounded-md border p-4">
        <div className="space-y-2">
          <FormLabel>Builder hourly budget</FormLabel>
          <PublishRoleRateRanges builderMinNumberProperty="builderRateMin" builderMaxNumberProperty="builderRateMax" />
          <PublishRoleRateRangeSettings collectBuilderRateProperty="collectBuilderHourlyRate" showClientBudgetProperty="showClientHourlyBudget" />
          <Divider className="!my-6" />
          <FormLabel>Builder monthly budget</FormLabel>
          <PublishRoleRateRanges builderMinNumberProperty="builderMonthlyRateMin" builderMaxNumberProperty="builderMonthlyRateMax" />
          <PublishRoleRateRangeSettings collectBuilderRateProperty="collectBuilderMonthlyRate" showClientBudgetProperty="showClientMonthlyBudget" />
        </div>
      </div>
    </div>
  );
};
