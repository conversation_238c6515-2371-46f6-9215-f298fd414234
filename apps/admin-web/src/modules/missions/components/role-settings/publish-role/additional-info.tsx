import { useFormContext } from 'react-hook-form';

import { Checkbox, FormControl, FormField, FormItem, FormLabel, LabeledInput, Switch } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';
import { PublishRoleAllowedCountries } from './allowed-countries';
import { PublishRoleHideRoleFromUsers } from './hide-role-from-users';
import { PublishRoleSkills } from './skills';
import { PublishRoleTimezone } from './timezone';
import { PublishRoleWorkingHours } from './working-hours';
import { PublishRoleWorkingHoursOverlap } from './working-hours-overlap';

export const PublishRoleAdditionalInfo = () => {
  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.minimumHoursPerWeek`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Minimum hours per week</FormLabel>
            <LabeledInput label="hrs/week" type="number" containerClassName="w-1/3" value={field.value} onChange={field.onChange} />
          </FormItem>
        )}
      />
      <PublishRoleTimezone />
      <PublishRoleWorkingHours />
      <PublishRoleWorkingHoursOverlap />
      <PublishRoleSkills skillsProperty="requiredSkills" />
      <PublishRoleSkills skillsProperty="preferredSkills" />
      <PublishRoleAllowedCountries />
      <PublishRoleHideRoleFromUsers />
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.automatedStatusesAsignmentDisabled`}
        render={({ field }) => (
          <FormItem className="flex gap-4 rounded-md bg-gray-300 p-4">
            <Switch checked={field.value} onCheckedChange={field.onChange} />
            <FormLabel className="!my-auto">Disable automated statuses assignment</FormLabel>
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.readyForReview`}
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md bg-gray-300 p-4">
            <FormControl>
              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
            </FormControl>
            <FormLabel className="!my-auto">Optimization: Role is updated and ready to review</FormLabel>
          </FormItem>
        )}
      />
    </div>
  );
};
