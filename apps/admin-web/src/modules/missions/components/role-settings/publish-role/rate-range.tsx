import { useFormContext } from 'react-hook-form';

import { formatCurrency } from '@lib/helpers/currency';

import { FormField, FormItem, CurrencyInput } from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingFormValues, RoleSettingsFormValues } from '../role-settings-form';

type PublishRoleRateRangesProps = {
  builderMinNumberProperty: keyof Pick<RoleSettingFormValues, 'builderRateMin' | 'builderMonthlyRateMin'>;
  builderMaxNumberProperty: keyof Pick<RoleSettingFormValues, 'builderRateMax' | 'builderMonthlyRateMax'>;
};

export const PublishRoleRateRanges: React.FC<PublishRoleRateRangesProps> = ({ builderMinNumberProperty, builderMaxNumberProperty }) => {
  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control, getValues } = useFormContext<RoleSettingsFormValues>();

  const builderMinNumber = getValues(`${filterStatus}.${selectedRoleIndex}.${builderMinNumberProperty}`);
  const builderMaxNumber = getValues(`${filterStatus}.${selectedRoleIndex}.${builderMaxNumberProperty}`);
  const roleMarkup = getValues(`${filterStatus}.${selectedRoleIndex}.markup`);

  const companyBudgetMinRate = builderMinNumber ? builderMinNumber * (1 + roleMarkup) : 0;
  const companyBudgetMaxRate = builderMaxNumber ? builderMaxNumber * (1 + roleMarkup) : 0;

  const roleMarkupPercentage = roleMarkup * 100;

  const unit = builderMinNumberProperty === 'builderMonthlyRateMin' ? 'month' : 'hour';
  const companyBudgetUnit = builderMinNumberProperty === 'builderMonthlyRateMin' ? '/m' : '/hr';

  return (
    <>
      <div className="flex w-full justify-between">
        <FormField
          control={control}
          name={`${filterStatus}.${selectedRoleIndex}.${builderMinNumberProperty}`}
          render={({ field }) => (
            <FormItem className="w-full">
              <CurrencyInput unit={unit} value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
        <span className="my-auto w-1/5 text-center">-</span>
        <FormField
          control={control}
          name={`${filterStatus}.${selectedRoleIndex}.${builderMaxNumberProperty}`}
          render={({ field }) => (
            <FormItem className="w-full">
              <CurrencyInput unit={unit} value={field.value} onChange={field.onChange} />
            </FormItem>
          )}
        />
      </div>
      <div className="rounded-md bg-gray-300 px-4 py-2">
        Company's budget is {formatCurrency(companyBudgetMinRate)} to {formatCurrency(companyBudgetMaxRate)}
        {companyBudgetUnit} [{roleMarkupPercentage}%]
      </div>
    </>
  );
};
