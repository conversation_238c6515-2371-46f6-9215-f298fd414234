import { MISSION_ROLE_VISIBILITY_STATUSES } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  FormItem,
  FormLabel,
  FormField,
  RichTextEditor,
  RadioGroup,
  RadioGroupItem,
  Label,
} from '@shared-components';

import { useRoleSettingContext } from '../role-setting-context';
import { RoleSettingsFormValues } from '../role-settings-form';
import { PublishRoleCustomQuestion } from './custom-question';
import { PublishRoleTitle } from './title';

export const PublishRoleBaseInfo = () => {
  const { selectedRoleIndex, filterStatus } = useRoleSettingContext();

  const { control } = useFormContext<RoleSettingsFormValues>();

  return (
    <div className="space-y-4">
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.visibilityStatus`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role visibility</FormLabel>
            <Select value={field.value} onValueChange={field.onChange}>
              <SelectTrigger className="w-5/6 px-2">
                <SelectValue placeholder="Select visibility status" />
              </SelectTrigger>
              <SelectContent>
                {MISSION_ROLE_VISIBILITY_STATUSES.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FormItem>
        )}
      />
      <PublishRoleTitle />
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.description`}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role description</FormLabel>
            <RichTextEditor value={field.value} onChange={field.onChange} placeholder="Describe your role..." />
          </FormItem>
        )}
      />
      <PublishRoleCustomQuestion />
      <FormField
        control={control}
        name={`${filterStatus}.${selectedRoleIndex}.isNiche`}
        render={({ field }) => (
          <FormItem className="space-y-4 rounded-md bg-gray-300 p-4">
            <FormLabel>Is this a niche role?</FormLabel>
            <RadioGroup value={field.value ? 'niche' : 'general'} onValueChange={(value) => field.onChange(value === 'niche')} className="flex gap-2">
              <div className="flex items-center gap-2 space-y-0 border-r pr-4">
                <RadioGroupItem value="niche" id="niche" />
                <Label htmlFor="niche" className="cursor-pointer">
                  Yes, it's a niche role
                </Label>
              </div>
              <div className="ml-2 flex items-center gap-2 space-y-0">
                <RadioGroupItem value="general" id="general" />
                <Label htmlFor="general" className="cursor-pointer">
                  No, it's a general role
                </Label>
              </div>
            </RadioGroup>
          </FormItem>
        )}
      />
    </div>
  );
};
