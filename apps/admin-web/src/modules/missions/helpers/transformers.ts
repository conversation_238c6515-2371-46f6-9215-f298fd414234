import { RoleSettingDto } from '@packages/contracts';

import { RoleSettingFormValues } from '@modules/missions/components/role-settings/role-settings-form';

import { convertMinutesToTimeString } from './working-hours';

export const transformRoleSettingDtoToRoleSettingForm = (roleSetting: RoleSettingDto): RoleSettingFormValues => {
  return {
    rid: roleSetting.rid,
    status: roleSetting.status,
    title: roleSetting.title,
    visibilityStatus: roleSetting.visibilityStatus,
    description: roleSetting.description,
    isNiche: roleSetting.isNiche,
    markup: roleSetting.markup,
    // hourly rates
    builderRateMin: roleSetting.builderRateMin,
    builderRateMax: roleSetting.builderRateMax,
    clientRateMin: roleSetting.clientRateMin,
    clientRateMax: roleSetting.clientRateMax,
    collectBuilderHourlyRate: roleSetting.collectBuilderHourlyRate,
    showClientHourlyBudget: roleSetting.showClientHourlyBudget,
    // monthly rates
    builderMonthlyRateMin: roleSetting.builderMonthlyRateMin,
    builderMonthlyRateMax: roleSetting.builderMonthlyRateMax,
    clientMonthlyRateMin: roleSetting.clientMonthlyRateMin,
    clientMonthlyRateMax: roleSetting.clientMonthlyRateMax,
    collectBuilderMonthlyRate: roleSetting.collectBuilderMonthlyRate,
    showClientMonthlyBudget: roleSetting.showClientMonthlyBudget,
    minimumHoursPerWeek: roleSetting.minimumHoursPerWeek,
    timezone: roleSetting.timezone,
    workingHoursStartTime: roleSetting.workingHoursStartTime ? convertMinutesToTimeString(roleSetting.workingHoursStartTime) : undefined,
    workingHoursEndTime: roleSetting.workingHoursEndTime ? convertMinutesToTimeString(roleSetting.workingHoursEndTime) : undefined,
    hoursOverlap: roleSetting.hoursOverlapMinutes ? convertMinutesToTimeString(roleSetting.hoursOverlapMinutes) : undefined,
    requiredSkills: roleSetting.requiredSkills,
    preferredSkills: roleSetting.preferredSkills,
    allowedCountries: roleSetting.allowedCountries.map((countryCode) => ({ code: countryCode })),
    automatedStatusesAsignmentDisabled: roleSetting.automatedStatusesAsignmentDisabled,
    roleHiddenFromBuilders: roleSetting.roleHiddenFromBuilders,
    readyForReview: roleSetting.readyForReview,
    // Form adjustments
    customQuestionQid: roleSetting.customQuestion?.qid,
    customQuestionText: roleSetting.customQuestion?.text,
    customQuestionIsVisible: roleSetting.customQuestion?.isVisible ?? false,
    customQuestionIsRequired: roleSetting.customQuestion?.isRequired ?? false,
  };
};
