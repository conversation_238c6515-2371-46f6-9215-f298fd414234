import { MISSION_ROLE_STATUSES, MissionRoleStatus, MissionStatus } from '@packages/contracts';

const MISSION_ROLE_STATUS_OPTIONS = MISSION_ROLE_STATUSES.reduce((acc, status) => ({ ...acc, [status]: status }), {} as Record<MissionRoleStatus, string>);

export const getAvailableRoleStatusOptions = (missionStatus: MissionStatus, roleStatus: MissionRoleStatus): string[] => {
  const { Canceled, Open, Ended, ScheduledToEnd, Active } = MISSION_ROLE_STATUS_OPTIONS;

  if (roleStatus === Active) {
    return [Active, ScheduledToEnd, Ended];
  }

  const options: string[] = [];

  switch (missionStatus) {
    case 'Created':
    case 'Published':
    case 'Pending':
      options.push(Canceled, Open);
      break;
    case 'Running':
      options.push(Canceled, Open, Active, ScheduledToEnd, Ended);
      break;
    case 'Ended':
      options.push(Ended, Canceled);
      break;
    case 'Archived':
      options.push(Canceled);
      break;
    default:
      options.push(Canceled, Open, Active, ScheduledToEnd, Ended);
      break;
  }

  return Array.from(new Set(options));
};
