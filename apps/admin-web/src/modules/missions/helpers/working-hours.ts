export const HOUR_OPTIONS_12_HOUR_FORMAT: string[] = [
  '12:00 AM',
  '1:00 AM',
  '2:00 AM',
  '3:00 AM',
  '4:00 AM',
  '5:00 AM',
  '6:00 AM',
  '7:00 AM',
  '8:00 AM',
  '9:00 AM',
  '10:00 AM',
  '11:00 AM',
  '12:00 PM',
  '1:00 PM',
  '2:00 PM',
  '3:00 PM',
  '4:00 PM',
  '5:00 PM',
  '6:00 PM',
  '7:00 PM',
  '8:00 PM',
  '9:00 PM',
  '10:00 PM',
  '11:00 PM',
] as const;

export const OVERLAP_HOURS_OPTIONS: string[] = [
  '0:00',
  '1:00',
  '2:00',
  '3:00',
  '4:00',
  '5:00',
  '6:00',
  '7:00',
  '8:00',
  '9:00',
  '10:00',
  '11:00',
  '12:00',
] as const;

/**
 * Converts a time string in 'H:MM' format to minutes
 *
 * @param timeString - Time string in 'H:MM' format (e.g., '7:00', '13:30')
 * @returns Total minutes
 */
export const convertTimeToMinutes = (timeString: string): number => {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + (minutes || 0);
};

/**
 * Converts minutes to a time string in 'H:MM AM/PM' format.
 *
 * @param totalMinutes - Total minutes
 * @returns Time string in 'H:MM AM/PM' format (e.g., '9:00 AM', '6:00 PM')
 */
export const convertMinutesToTimeString = (totalMinutes: number): string => {
  let hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  const period = hours < 12 ? 'AM' : 'PM';

  hours = hours % 12;
  hours = hours === 0 ? 12 : hours; // 0 hours should be displayed as 12

  const minutesFormatted = minutes.toString().padStart(2, '0');

  return `${hours}:${minutesFormatted} ${period}`;
};
