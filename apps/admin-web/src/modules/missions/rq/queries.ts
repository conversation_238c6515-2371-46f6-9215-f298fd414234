import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useGetMissionIndustries = () => {
  return api.adminMissions.getMissionIndustries.useQuery({
    queryKey: queryKeys.missions.getMissionIndustries,
    select: (data) => data.body,
  });
};

export const useGetTfsOwners = () => {
  return api.adminMissions.getTfsOwners.useQuery({
    queryKey: queryKeys.missions.getTfsOwners,
    select: (data) => data.body,
  });
};

export const useGetMissionRoleCategories = () => {
  return api.adminMissions.getMissionRoleCategories.useQuery({
    queryKey: queryKeys.missions.getMissionRoleCategories,
    select: (data) => data.body,
  });
};

export const useGetMissionRoleSkills = () => {
  return api.adminMissions.getMissionRoleSkills.useQuery({
    queryKey: queryKeys.missions.getMissionRoleSkills,
    select: (data) => data.body,
  });
};

export const useGetMissionUsersToExclude = (queryName: string) => {
  return api.adminMissions.getUsersToExclude.useQuery({
    queryKey: queryKeys.missions.getMissionUsersToExclude(queryName),
    queryData: { query: { query: queryName } },
    select: (data) => data.body,
    enabled: queryName.length > 0,
  });
};

export const useGetAdminSettings = (id: string) => {
  return api.adminMissions.getAdminSettings.useQuery({
    queryKey: queryKeys.missions.getAdminSettings(id),
    queryData: { params: { id } },
    select: (data) => data.body,
  });
};

export const useGetMissionSettings = (id: string) => {
  return api.adminMissions.getMissionSettings.useQuery({
    queryKey: queryKeys.missions.getMissionSettings(id),
    queryData: { params: { id } },
    select: (data) => data.body,
  });
};

export const useGetRoleSettings = (id: string) => {
  return api.adminMissions.getRoleSettings.useQuery({
    queryKey: queryKeys.missions.getRoleSettings(id),
    queryData: { params: { id } },
    select: (data) => data.body,
  });
};
