import { InfoIcon } from 'lucide-react';

import { formatCurrency } from '@lib/helpers/currency';
import { cn } from '@lib/utils';

import { Separator, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@shared-components';

type MonthlyRateBreakdownProps = {
  monthlyRate: number;
  currency: 'USD' | 'EUR';
};

const HOURS_PER_WEEK = 40;
const AVERAGE_WEEKS_IN_A_MONTH = 4.33;

const LabelValue = ({ label, value, className }: { label: string; value: string; className?: string }) => {
  return (
    <div className={cn('gap-y-0.75 flex justify-between gap-x-10 text-xs font-normal', className)}>
      <span>{label}</span>
      <span>{value}</span>
    </div>
  );
};

export const MonthlyRateBreakdown = ({ monthlyRate, currency }: MonthlyRateBreakdownProps) => {
  const hourlyRateEquivalent = monthlyRate / HOURS_PER_WEEK / AVERAGE_WEEKS_IN_A_MONTH;

  return (
    <div className="flex items-center gap-1 text-xs text-gray-600">
      <span>Equivalent to {formatCurrency(hourlyRateEquivalent, currency)}/hour</span>
      <TooltipProvider delayDuration={100}>
        <Tooltip>
          <TooltipTrigger>
            <InfoIcon size={14} className="text-gray-600" />
          </TooltipTrigger>
          <TooltipContent className="bg-white p-6 text-black sm:max-w-72 sm:bg-gray-700 sm:px-3 sm:py-2 sm:text-white">
            <div className="mb-2 text-sm font-medium">Hourly rate calculation</div>
            <LabelValue label="Monthly rate" value={`${formatCurrency(monthlyRate, currency)} per month`} className="font-medium" />
            <LabelValue label="Hours per week" value={`/ ${HOURS_PER_WEEK}`} />
            <LabelValue label="Average weeks in a month" value={`/ ${AVERAGE_WEEKS_IN_A_MONTH}`} />
            <Separator className="my-2 bg-gray-300 sm:bg-gray-600" />
            <LabelValue label="Hourly rate" value={`${formatCurrency(hourlyRateEquivalent, currency)} per hour`} />
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};
