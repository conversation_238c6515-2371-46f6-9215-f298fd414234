import { ProposalRoleApplication } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { MAX_LLM_SECTION_LENGTH, MIN_LLM_SECTION_LENGTH } from '@modules/proposals/helpers/constants';

import { cn } from '@lib/utils';

import { FormControl, FormItem, FormField, Switch, FormLabel, FormMessage } from '@shared-components';

import { convertTitleAndTextToMarkdown } from '../helpers/rich-text';
import { useFetchGeneratedSectionForCandidate } from '../rq/mutations';
import { LLMInput } from './llm-input';

type TeamMemberSectionsProps = {
  teamMemberIndex: number;
  application: ProposalRoleApplication;
  missionId: string;
  roleId: string;
};

export const TeamMemberSections = ({ teamMemberIndex, application, missionId, roleId }: TeamMemberSectionsProps) => {
  const { control } = useFormContext();

  const { mutateAsync: fetchGeneratedSectionForCandidate } = useFetchGeneratedSectionForCandidate();

  const generateSection = async (section: 'requirements-section' | 'experience-section') => {
    return await fetchGeneratedSectionForCandidate({
      missionId,
      roleId,
      userId: application.user.id,
      section,
    });
  };

  const generateSectionText = async (sectionType: 'experience-section' | 'requirements-section') => {
    const section = await generateSection(sectionType);
    return convertTitleAndTextToMarkdown(section.title ?? '', [section.section]);
  };

  return (
    <>
      <FormField
        control={control}
        name={`teamMembers.${teamMemberIndex}.requirementsSection`}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="mt-4 text-sm font-medium">How this builder fits the client's requirements?</FormLabel>
            <FormControl>
              <LLMInput
                onChange={field.onChange}
                content={field.value as string}
                onGenerateSection={() => generateSectionText('requirements-section')}
                startWritingLabel="Generate an answer based on the available information about the builder."
                minContentLength={MIN_LLM_SECTION_LENGTH}
                maxContentLength={MAX_LLM_SECTION_LENGTH}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={control}
        name={`teamMembers.${teamMemberIndex}.experienceSection`}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="mt-4 text-sm font-medium">Builder experience</FormLabel>
            <FormControl>
              <LLMInput
                includeHeading
                onChange={field.onChange}
                content={field.value as string}
                onGenerateSection={() => generateSectionText('experience-section')}
                startWritingLabel="Generate a text based on the available information about the builder."
                minContentLength={MIN_LLM_SECTION_LENGTH}
                maxContentLength={MAX_LLM_SECTION_LENGTH}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      {application.customQuestion && (
        <div>
          <FormField
            control={control}
            name={`teamMembers.${teamMemberIndex}.includeCustomQuestionReply`}
            render={({ field }) => (
              <FormItem className="space-y-0">
                <div className="mb-2.5 flex flex-row items-center gap-2">
                  <FormControl>
                    <Switch checked={field.value as boolean} onCheckedChange={field.onChange} />
                  </FormControl>
                  <FormLabel className="text-sm font-medium">Show a response to the custom question in the team proposal</FormLabel>
                </div>
                {application.customQuestion && (
                  <div className={cn('rounded-md border border-gray-400 p-6', !field.value && 'opacity-40')}>
                    <h6 className="mb-2 text-sm font-semibold">{application.customQuestion.questionText}</h6>
                    <p className="text-sm">{application.customQuestion.replyText}</p>
                  </div>
                )}
              </FormItem>
            )}
          />
        </div>
      )}
    </>
  );
};
