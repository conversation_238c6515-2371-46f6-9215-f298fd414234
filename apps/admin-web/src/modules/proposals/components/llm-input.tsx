'use client';

import { SparklesIcon, WandSparklesIcon } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { cn } from '@lib/utils';

import { Button, RichTextEditor } from '@shared-components';

import { extractTitleAndTextForSection } from '../helpers/rich-text';

type LLMInputProps = {
  content: string;
  onChange: (content: string) => void;
  onGenerateSection: () => Promise<string>;
  startWritingLabel: string;
  className?: string;
  placeholder?: string;
  minContentLength?: number;
  maxContentLength?: number;
  includeHeading?: boolean;
  generateOnMount?: boolean;
};

export const LLMInput = ({
  className,
  placeholder = 'Start typing...',
  content,
  onChange,
  onGenerateSection,
  startWritingLabel,
  minContentLength,
  maxContentLength,
  includeHeading = false,
  generateOnMount = true,
}: LLMInputProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedSection, setGeneratedSection] = useState('');
  const [editorText, setEditorText] = useState('');

  const alreadyRunOnMount = useRef(false);

  const handleGenerateSection = async () => {
    setIsGenerating(true);
    const section = await onGenerateSection();
    setGeneratedSection(section);
    setIsGenerating(false);
  };

  const handleEditorChange = (value: string, text?: string) => {
    if (maxContentLength && text && text.length <= maxContentLength) {
      onChange(value);
      setEditorText(text ?? '');
    }
  };

  const { title: generatedSectionTitle, paragraphs: generatedSectionParagraphs } = extractTitleAndTextForSection(generatedSection);

  const useSuggestion = () => {
    handleEditorChange(generatedSection, `${generatedSectionTitle}\n${generatedSectionParagraphs.join('\n')}`);
    setGeneratedSection('');
  };

  const initializeOnMount = async () => {
    alreadyRunOnMount.current = true;
    setIsGenerating(true);
    const section = await onGenerateSection();
    const { title, paragraphs } = extractTitleAndTextForSection(section);
    handleEditorChange(section, `${title}\n${paragraphs.join('\n')}`);
    setIsGenerating(false);
  };

  useEffect(() => {
    if (generateOnMount && !alreadyRunOnMount.current && !content) {
      initializeOnMount();
    }
  }, [generateOnMount]);

  return (
    <div className={className}>
      <div className="relative rounded-md border border-gray-400 bg-white px-4 py-4">
        <RichTextEditor
          value={content}
          onChange={handleEditorChange}
          placeholder={placeholder}
          className="border-none"
          contentClassName="min-h-[80px] px-0"
          includeHeadingControl
          includeLinkControl={false}
          includeHeading={includeHeading}
        />
        <div className={cn('flex', (!!minContentLength || !!maxContentLength) && 'pt-2')}>
          {minContentLength && <p className="text-xs text-gray-900">{minContentLength} characters minimum</p>}
          {maxContentLength && (
            <p className="ml-auto text-xs text-gray-900">
              {editorText.length} / {maxContentLength} characters
            </p>
          )}
        </div>
      </div>
      <div className="-mt-4 rounded-md border border-gray-400 bg-purple-100 px-4 pb-4 pt-8">
        <div className="flex items-center justify-between">
          <p className="flex items-center gap-1.5 text-xs font-medium text-purple-600">
            <SparklesIcon className="size-3" /> {generatedSection ? 'Use suggestion' : startWritingLabel}
          </p>
          {!generatedSection && (
            <Button type="button" size="sm" className="h-8" onClick={() => void handleGenerateSection()} loading={isGenerating}>
              <WandSparklesIcon className="size-4" /> Start writing
            </Button>
          )}
        </div>
        {generatedSection && (
          <>
            <div className="my-4 text-xs text-purple-600">
              {generatedSectionTitle && <h3 className="mb-1 text-sm font-medium">{generatedSectionTitle}</h3>}
              <p>{generatedSectionParagraphs.length ? generatedSectionParagraphs.map((paragraph) => <div>{paragraph}</div>) : generatedSection}</p>
            </div>
            <div className="flex items-center gap-2">
              <Button type="button" size="sm" className="h-8" onClick={useSuggestion}>
                Use suggestion
              </Button>
              <Button type="button" size="sm" className="h-8" variant="secondary" onClick={() => void handleGenerateSection()} loading={isGenerating}>
                Rewrite
              </Button>
              <Button type="button" size="sm" className="ml-auto h-8" onClick={() => setGeneratedSection('')}>
                Discard
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};
