import { ProposalRoleApplication } from '@packages/contracts';
import { XIcon } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { formatCurrency } from '@lib/helpers/currency';

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
  FormControl,
  FormItem,
  FormField,
  Separator,
  Switch,
  Input,
  FormDescription,
  FormLabel,
  UserBadge,
  FormMessage,
} from '@shared-components';

import { MonthlyRateBreakdown } from './monthly-rate-breakdown';
import { TeamMemberSections } from './team-member-sections';

type TeamMemberProps = {
  missionId: string;
  roleId: string;
  teamMemberIndex: number;
  application: ProposalRoleApplication;
  onRemoveMember: () => void;
};

const TeamMember = ({ missionId, roleId, application, teamMemberIndex, onRemoveMember }: TeamMemberProps) => {
  const { control, watch } = useFormContext();

  const currency = watch('currency');
  const currencySymbol = currency === 'USD' ? '$' : '€';

  return (
    <div className="relative flex flex-col gap-6 rounded-xl bg-gray-50 px-8 py-6">
      <Button className="absolute right-2 top-2" variant="ghost" size="sm" name="Admin Proposal Remove Member" onClick={onRemoveMember}>
        <XIcon className="size-4" />
      </Button>
      <div className="flex flex-row gap-2">
        <Avatar className="size-10 rounded-full">
          <AvatarImage src={application.user.pictureURL ?? undefined} />
          <AvatarFallback>{application.user.name.charAt(0)}</AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-0.5">
          <p className="flex items-center gap-1 text-sm font-medium">
            {application.user.name}
            <div className="flex gap-2 px-1">
              {application.user.badges.map((badge) => (
                <UserBadge key={badge} badge={badge} className="size-4" />
              ))}
            </div>
          </p>
          {application.user.title && <p className="text-sm font-normal text-gray-700">{application.user.title}</p>}
        </div>
      </div>
      <div className="flex flex-col gap-8">
        <div className="rounded-md border border-gray-400">
          <div className="grid grid-cols-[1fr_1fr_90px] gap-6 p-6">
            <div>
              <FormField
                control={control}
                name={`teamMembers.${teamMemberIndex}.showHourlyRate`}
                render={({ field }) => (
                  <FormItem className="mb-2 flex flex-row items-center gap-2 space-y-0">
                    <FormControl>
                      <Switch checked={field.value as boolean} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Builder hourly rate</FormLabel>
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`teamMembers.${teamMemberIndex}.hourlyRate`}
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center gap-2">
                      <span className="text-sm">{currencySymbol}</span>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <span className="text-sm">/hour</span>
                    </div>
                    {application.hourlyRateRange.min && application.hourlyRateRange.max && (
                      <FormDescription>
                        Applied range: {formatCurrency(application.hourlyRateRange.min, currency)} - {formatCurrency(application.hourlyRateRange.max, currency)}
                        /hour
                      </FormDescription>
                    )}
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={control}
                name={`teamMembers.${teamMemberIndex}.showMonthlyRate`}
                render={({ field }) => (
                  <FormItem className="mb-2 flex flex-row items-center gap-2 space-y-0">
                    <FormControl>
                      <Switch checked={field.value as boolean} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Builder monthly rate</FormLabel>
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`teamMembers.${teamMemberIndex}.monthlyRate`}
                render={({ field }) => (
                  <FormItem>
                    <div className="flex flex-row items-center gap-2">
                      <span className="text-sm">{currencySymbol}</span>
                      <FormControl>
                        <Input type="number" {...field} />
                      </FormControl>
                      <span className="text-sm">/month</span>
                    </div>
                    {application.monthlyRateRange.min && application.monthlyRateRange.max && (
                      <FormDescription>
                        Applied range: {formatCurrency(application.monthlyRateRange.min, currency)} -{' '}
                        {formatCurrency(application.monthlyRateRange.max, currency)}/month
                      </FormDescription>
                    )}
                    {field.value && <MonthlyRateBreakdown monthlyRate={Number(field.value)} currency={currency} />}
                  </FormItem>
                )}
              />
            </div>
            <div>
              <FormField
                control={control}
                name={`teamMembers.${teamMemberIndex}.markup`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Role markup</FormLabel>
                    <div className="flex flex-row items-center gap-2">
                      <FormControl>
                        <Input placeholder="30" type="number" {...field} />
                      </FormControl>
                      <span className="text-sm">%</span>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>
          <Separator className="w-full bg-gray-400" />
          <div className="grid grid-cols-2 gap-x-4 gap-y-6 p-6">
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.website`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Website <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="https://website.com..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.githubUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    GitHub <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="github.com/username..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.portfolioUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Portfolio <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="https://website.com/portfolio..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.portfolioPassword`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    Portfolio password <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Password-123..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.linkedInUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    LinkedIn <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="linkedin.com/in/username..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.cvUrl`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">
                    CV URL <span className="text-gray-700">(Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="https://website.com/cv..." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Separator className="w-full bg-gray-400" />
          <div className="p-6">
            <FormField
              control={control}
              name={`teamMembers.${teamMemberIndex}.totalHoursBilled`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm font-medium">Hours worked at A.Team</FormLabel>
                  <div className="flex flex-row items-center gap-2">
                    <FormControl>
                      <Input type="number" placeholder="0" className="max-w-40" {...field} />
                    </FormControl>
                    <div className="rounded-md bg-gray-200 p-2.5 text-sm">If 0, hours billed will be hidden for this builder.</div>
                  </div>
                </FormItem>
              )}
            />
          </div>
        </div>
        <TeamMemberSections teamMemberIndex={teamMemberIndex} application={application} missionId={missionId} roleId={roleId} />
      </div>
    </div>
  );
};

export default TeamMember;
