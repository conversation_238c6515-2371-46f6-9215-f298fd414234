'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ZapIcon } from 'lucide-react';
import { useEffect } from 'react';
import { FieldErrors, FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { useCreateProposal } from '@modules/proposals/rq/mutations';
import { useGetMissionDataForProposal, useGetTeamAdvisors } from '@modules/proposals/rq/queries';

import { formatUrl } from '@lib/helpers/urls';

import {
  RadioGroupItem,
  RadioGroup,
  Select,
  Separator,
  SelectContent,
  SelectValue,
  SelectTrigger,
  SelectItem,
  Skeleton,
  FormField,
  FormControl,
  FormItem,
  Button,
  Avatar,
  AvatarImage,
  AvatarFallback,
} from '@shared-components';

import { sendProposalCreatedIframeMessage } from '../helpers/iframe-actions';
import { extractTitleAndTextForSection } from '../helpers/rich-text';
import Role from './role';

const teamMemberSchema = z.object({
  roleId: z.string(),
  applicationId: z.string(),
  builderId: z.string(),
  showHourlyRate: z.boolean(),
  showMonthlyRate: z.boolean(),
  hourlyRate: z.coerce.number().min(0, 'Hourly rate must be a positive number').optional(),
  monthlyRate: z.coerce.number().min(0, 'Monthly rate must be a positive number').optional(),
  markup: z.coerce.number().min(0, 'Markup must be a positive number').max(100, 'Markup must be less than 100').optional(),
  website: z.string().url('Website must be a valid URL').or(z.literal('')).optional(),
  githubUrl: z.string().url('GitHub URL must be a valid URL').or(z.literal('')).optional(),
  portfolioUrl: z.string().url('Portfolio URL must be a valid URL').or(z.literal('')).optional(),
  portfolioPassword: z.string().optional(),
  linkedInUrl: z.string().url('LinkedIn URL must be a valid URL').or(z.literal('')).optional(),
  cvUrl: z.string().url('CV URL must be a valid URL').or(z.literal('')).optional(),
  totalHoursBilled: z.coerce.number().min(0, 'Total hours billed must be a positive number').optional(),
  requirementsSection: z.string(),
  experienceSection: z.string(),
  includeCustomQuestionReply: z.boolean(),
});

const createProposalFormSchema = z.object({
  teamAdvisorId: z.string(),
  currency: z.enum(['USD', 'EUR']),
  makeTeamNarrativeProposal: z.boolean(),
  showRatesToBuilders: z.boolean(),
  teamMembers: z.array(teamMemberSchema),
  roles: z.array(
    z.object({
      roleId: z.string(),
      aboutBuildersSection: z.string(),
    })
  ),
});

export type CreateProposalFormValues = z.infer<typeof createProposalFormSchema>;
export type TeamMemberData = z.infer<typeof teamMemberSchema>;

const createProposalFormInitialValues: CreateProposalFormValues = {
  teamAdvisorId: '',
  currency: 'USD',
  makeTeamNarrativeProposal: false,
  showRatesToBuilders: true,
  teamMembers: [],
  roles: [],
};

const ProposalCreationSkeleton = () => {
  return (
    <div className="flex flex-col gap-6">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  );
};

const ProposalCreation = ({ missionId }: { missionId: string }) => {
  const { data: missionData, isPending } = useGetMissionDataForProposal(missionId);
  const { mutate: createProposal, isPending: isCreatingProposal } = useCreateProposal();
  const { data: teamAdvisors = [] } = useGetTeamAdvisors();

  const form = useForm<CreateProposalFormValues>({
    resolver: zodResolver(createProposalFormSchema),
    defaultValues: createProposalFormInitialValues,
    shouldFocusError: true,
  });

  useEffect(() => {
    if (missionData) {
      form.setValue('teamAdvisorId', missionData.teamAdvisor.id);
    }
  }, [missionData]);

  if (!missionData || isPending) {
    return <ProposalCreationSkeleton />;
  }

  const handleSubmit = (values: CreateProposalFormValues) => {
    createProposal(
      {
        body: {
          missionId,
          currency: values.currency,
          teamAdvisorId: values.teamAdvisorId,
          roles: values.roles.map((role) => {
            const {
              paragraphs: [aboutBuildersSection],
            } = extractTitleAndTextForSection(role.aboutBuildersSection);

            return {
              roleId: role.roleId,
              aboutBuildersSection,
            };
          }),
          teamMembers: values.teamMembers.map((member) => {
            const {
              title: requirementsSectionTitle,
              paragraphs: [requirementsSectionText],
            } = extractTitleAndTextForSection(member.requirementsSection);
            const {
              title: experienceSectionTitle,
              paragraphs: [experienceSectionText],
            } = extractTitleAndTextForSection(member.experienceSection);

            return {
              roleId: member.roleId,
              applicationId: member.applicationId,
              builderId: member.builderId,
              showHourlyRate: member.showHourlyRate,
              showMonthlyRate: member.showMonthlyRate,
              hourlyRate: member.hourlyRate,
              monthlyRate: member.monthlyRate,
              markup: member.markup,
              website: formatUrl(member.website),
              githubUrl: formatUrl(member.githubUrl),
              portfolioUrl: formatUrl(member.portfolioUrl),
              portfolioPassword: member.portfolioPassword,
              linkedInUrl: formatUrl(member.linkedInUrl),
              cvUrl: formatUrl(member.cvUrl),
              totalHoursBilled: member.totalHoursBilled,
              includeCustomQuestionReply: member.includeCustomQuestionReply,
              requirementsSectionTitle,
              requirementsSectionText,
              experienceSectionTitle,
              experienceSectionText,
            };
          }),
        },
        headers: {},
      },
      {
        onSuccess: (data) => {
          toast.success('Proposal created successfully');
          window.scrollTo({ top: 0, behavior: 'smooth' });
          sendProposalCreatedIframeMessage(data.body.id);
        },
        onError: () => {
          toast.error('Failed to create proposal');
        },
      }
    );
  };

  const handleSubmitError = (errors: FieldErrors<CreateProposalFormValues>) => {
    sendProposalCreatedIframeMessage('');

    const firstErrorField = (Object.keys(errors) as (keyof typeof errors)[]).reduce<keyof typeof errors | null>((field, a) => {
      const fieldKey = field as keyof typeof errors;
      return !!errors[fieldKey] ? fieldKey : a;
    }, null);

    if (firstErrorField) {
      const firstError = errors[firstErrorField];
      if (Array.isArray(firstError)) {
        const [error] = Object.values(firstError[0]) as { ref: { name: string }; message: string }[];
        toast.error(`Error on field ${error.ref.name}: '${error.message}'`);
      } else {
        toast.error(`Error on field ${firstErrorField}: '${firstError}'`);
      }
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <FormProvider {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            void form.handleSubmit(handleSubmit, handleSubmitError)(e);
          }}
          className="flex flex-col gap-6"
        >
          <div>
            <h3 className="text-lg font-semibold">Team proposal details</h3>
            <p className="text-base font-normal">Add important information to this team proposal</p>
          </div>
          <div className="flex items-center gap-4">
            <FormField
              control={form.control}
              name="teamAdvisorId"
              render={({ field }) => (
                <FormItem key={field.value} className="min-w-52">
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="max-w-52 border border-gray-400">
                        <SelectValue placeholder="Team advisor" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent className="max-w-52">
                      {teamAdvisors.map((teamAdvisor) => (
                        <SelectItem key={teamAdvisor.id} value={teamAdvisor.id}>
                          <div className="flex items-center gap-2">
                            <Avatar className="size-4">
                              <AvatarImage src={teamAdvisor.pictureURL ?? undefined} />
                              <AvatarFallback>
                                <ZapIcon className="size-4 text-gray-600" />
                              </AvatarFallback>
                            </Avatar>
                            {teamAdvisor.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="currency"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex items-center rounded-sm border border-gray-400">
                      <div className="flex items-center gap-2 border-r border-r-gray-400 px-3 py-2">
                        <RadioGroupItem value="USD" />
                        <label className="whitespace-nowrap text-sm font-normal">Dollar ($)</label>
                      </div>
                      <div className="flex items-center gap-2 px-3 py-2">
                        <RadioGroupItem value="EUR" />
                        <label className="whitespace-nowrap text-sm font-normal">Euro (€)</label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          <Separator className="my-4" />
          <div>
            <h3 className="text-lg font-semibold">Team members</h3>
            <p className="text-sm font-normal">Select the proposed members for each open role</p>
          </div>
          {missionData.roles.map((role, index) => (
            <Role key={role.id} missionId={missionId} role={role} roleIndex={index} />
          ))}
          <Button type="submit" className="m-auto" loading={isCreatingProposal}>
            Generate proposal
          </Button>
        </form>
      </FormProvider>
    </div>
  );
};

export default ProposalCreation;
