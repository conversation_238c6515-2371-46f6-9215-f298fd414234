import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useGetMissionDataForProposal = (missionId: string) => {
  return api.adminProposals.getMissionDataForProposal.useQuery({
    queryKey: queryKeys.proposals.getMissionDataForProposal(missionId),
    queryData: { params: { missionId } },
    select: (data) => data.body,
  });
};

export const useGetRoleApplicationsForProposal = (missionId: string, roleId: string) => {
  return api.adminProposals.getRoleApplicationsForProposal.useQuery({
    queryKey: queryKeys.proposals.getRoleApplicationsForProposal(missionId, roleId),
    queryData: { params: { missionId, roleId } },
    select: (data) => data.body,
  });
};

export const useGetTeamAdvisors = () => {
  return api.adminProposals.getTeamAdvisors.useQuery({
    queryKey: queryKeys.proposals.getTeamAdvisors,
    select: (data) => data.body,
  });
};
