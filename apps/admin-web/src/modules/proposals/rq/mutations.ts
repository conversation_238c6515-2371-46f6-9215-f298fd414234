import { useMutation } from '@tanstack/react-query';

import api from '@config/api';

export const useFetchGeneratedSectionForCandidate = () => {
  return useMutation({
    mutationFn: async (data: { missionId: string; roleId: string; userId: string; section: 'experience-section' | 'requirements-section' }) => {
      const result = await api.adminProposals.getGeneratedSectionForCandidate.query({
        query: {
          missionId: data.missionId,
          roleId: data.roleId,
          userId: data.userId,
          section: data.section,
        },
      });

      if (result.status !== 200) {
        throw new Error(result.body.message);
      }

      return result.body;
    },
  });
};

export const useFetchGeneratedSectionForRole = () => {
  return useMutation({
    mutationFn: async (data: { missionId: string; roleId: string }) => {
      const result = await api.adminProposals.getGeneratedSectionForRole.query({
        query: {
          missionId: data.missionId,
          roleId: data.roleId,
        },
      });

      if (result.status !== 200) {
        throw new Error(result.body.message);
      }

      return result.body;
    },
  });
};

export const useCreateProposal = () => {
  return api.adminProposals.createProposal.useMutation();
};
