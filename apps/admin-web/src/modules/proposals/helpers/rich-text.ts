/**
 * Helper function to extract the title and text for a section from a rich text editor value.
 * Text is in the form of <h3>Title</h3><p>Text</p>.
 *
 * @param value - The rich text editor value.
 *
 * @returns The title and paragraphs for the section.
 */
export const extractTitleAndTextForSection = (value: string) => {
  const title = /<h3>((.|\s)*?)<\/h3>/.exec(value)?.[1] ?? '';
  const paragraphs = Array.from(value.matchAll(/<p>((.|\s)*?)<\/p>/g)).map((match) => match[1] ?? '');

  return {
    title,
    paragraphs,
  };
};

export const convertTitleAndTextToMarkdown = (title: string, paragraphs: string[]) => {
  return `<h3>${title}</h3>${paragraphs.map((paragraph) => `<p>${paragraph}</p>`).join('')}`;
};

export const convertTextToMarkdown = (text: string) => {
  return `<p>${text}</p>`;
};
