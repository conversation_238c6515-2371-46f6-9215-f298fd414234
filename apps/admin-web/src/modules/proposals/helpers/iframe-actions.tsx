'use client';

export const sendMissionApplicationClickedIframeMessage = (applicationId: string): void => {
  if (!window.top) {
    return;
  }

  window.top.postMessage({ name: 'openMissionApplicationModal', payload: { aid: applicationId } }, '*');
};

export const sendProposalCreatedIframeMessage = (proposalId: string): void => {
  if (!window.top) {
    return;
  }

  window.top.postMessage({ name: 'proposalCreated', payload: { pid: proposalId } }, '*');
};
