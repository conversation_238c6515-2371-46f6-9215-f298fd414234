import { z } from 'zod';

export const configSchema = z
  .object({
    NEXT_PUBLIC_API_URL: z.string().url(),
    NEXT_PUBLIC_ENV: z.enum(['local', 'test', 'development', 'production']),
    NEXT_PUBLIC_UPLOADCARE_API_KEY: z.string(),
  })
  .transform((data) => {
    return {
      ...data,
      IS_PROD: data.NEXT_PUBLIC_ENV === 'production',
      IS_LOCAL: data.NEXT_PUBLIC_ENV === 'local',
    };
  });

export type Config = Required<z.infer<typeof configSchema>>;
