import { contract } from '@packages/contracts';
import { tsRestFetchApi } from '@ts-rest/core';
import { initTsrReactQuery } from '@ts-rest/react-query/v5';

import { config } from '@config/index';

import { getAccountToken, getLocalAuthTokens } from '@lib/auth';

const api = initTsrReactQuery(contract, {
  baseUrl: config.NEXT_PUBLIC_API_URL,
  api: async (args) => {
    if (!args.headers) {
      args.headers = {};
    }

    const { accessToken } = getLocalAuthTokens();

    if (accessToken) {
      args.headers.authorization = `Bearer ${accessToken}`;
    }

    const accountToken = getAccountToken();

    if (accountToken) {
      args.headers['x-account-token'] = accountToken;
    }

    return tsRestFetchApi(args);
  },
});

export default api;
