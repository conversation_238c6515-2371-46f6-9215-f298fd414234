import { Config, configSchema } from './schema';

/**
 * Can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
 * middlewares) or client-side so we need to destruct manually.
 */
const parsedConfig = configSchema.safeParse({
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV,
  NEXT_PUBLIC_UPLOADCARE_API_KEY: process.env.NEXT_PUBLIC_UPLOADCARE_API_KEY,
});

if (!parsedConfig.success) {
  throw new Error('Invalid environment variables');
}

export const config: Config = parsedConfig.data;
