type TsRestError = {
  body: {
    message?: string;
  };
};

const UNKNOWN_ERROR_MESSAGE = 'An unknown error occurred, please contact support.';

export const getToastErrorMsg = (error: unknown): string => {
  if (isTsRestError(error)) {
    return error.body?.message ?? UNKNOWN_ERROR_MESSAGE;
  }

  if (error instanceof Error) {
    return error.message || UNKNOWN_ERROR_MESSAGE;
  }

  return UNKNOWN_ERROR_MESSAGE;
};

const isTsRestError = (error: unknown): error is TsRestError => {
  return typeof error === 'object' && !!error && 'body' in error;
};
