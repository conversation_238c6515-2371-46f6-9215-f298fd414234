const queryKeys = {
  auth: {
    getMe: ['auth', 'me'],
  },
  missions: {
    getAdminSettings: (id: string) => ['missions', 'adminSettings', id],
    getMissionSettings: (id: string) => ['missions', 'missionSettings', id],
    getRoleSettings: (id: string) => ['missions', 'roleSettings', id],
    getMissionIndustries: ['missions', 'missionIndustries'],
    getMissionRoleCategories: ['missions', 'roleCategories'],
    getMissionRoleSkills: ['missions', 'roleSkills'],
    getMissionUsersToExclude: (queryName: string) => ['missions', 'usersToExclude', queryName],
    getTfsOwners: ['missions', 'tfsOwners'],
  },
  proposals: {
    getRoleApplicationsForProposal: (missionId: string, roleId: string) => ['proposal', 'roleApplications', missionId, roleId],
    getMissionDataForProposal: (missionId: string) => ['proposal', 'missionData', missionId],
    getTeamAdvisors: ['proposal', 'teamAdvisors'],
  },
};

export default queryKeys;
