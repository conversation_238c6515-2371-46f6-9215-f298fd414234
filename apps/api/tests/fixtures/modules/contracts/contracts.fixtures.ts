import { PandadocDocumentWebhookPayload } from '@packages/contracts';

export const masterServicesPandadocWebhookDataFixture: PandadocDocumentWebhookPayload = {
  event: 'recipient_completed',
  data: {
    id: 'Ubx4x8VNHw5Xcqx8ke7Kka',
    name: 'MSA Cover Terms for OLIPOP',
    date_created: new Date('2024-12-13T15:05:54.852636Z'),
    date_modified: new Date('2024-12-16T13:06:15.933913Z'),
    expiration_date: new Date('2025-02-11T15:06:37.034181Z'),
    autonumbering_sequence_name: null,
    created_by: {
      id: '4ucsYUMk9k2RyfBQo85NiZ',
      email: '<EMAIL>',
      first_name: 'Maily',
      last_name: '<PERSON><PERSON>',
      membership_id: '6SH4Yshcdv79WgDNokoFZD',
    },
    fields: [
      {
        field_id: 'Signature2',
        uuid: '6669c277-ca1f-4fbd-aade-6d446388c729',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'MKnM9BADJ3MgmcYP2dPHVR',
          first_name: 'Admin',
          last_name: 'Ateam',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'Signature1',
        uuid: '984e5675-3992-484c-b65a-3d0108ba8a60',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'zyG6ggYzGnuBgkFNANExQg',
          first_name: 'Maily',
          last_name: 'Testson',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'Date2',
        uuid: 'd5c28e23-5008-46a6-b6a7-891e9def2df3',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: new Date('2024-12-16T14:06:15'),
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'zyG6ggYzGnuBgkFNANExQg',
          first_name: 'Maily',
          last_name: 'Testson',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
      {
        field_id: 'Date1',
        uuid: 'eabc4cdf-d752-4972-805f-2674c2c1d866',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: null,
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'MKnM9BADJ3MgmcYP2dPHVR',
          first_name: 'Admin',
          last_name: 'Ateam',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
    ],
    status: 'document.sent',
    recipients: [
      {
        type: 'recipient',
        id: 'zyG6ggYzGnuBgkFNANExQg',
        first_name: 'Maily',
        last_name: 'Testson',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: true,
        role: '',
        roles: ['Client'],
        signing_order: null,
        contact_id: 'LKthdowGfZHXTPb2JHbJZU',
        shared_link: '',
      },
      {
        type: 'recipient',
        id: 'MKnM9BADJ3MgmcYP2dPHVR',
        first_name: 'Admin',
        last_name: 'Ateam',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: false,
        role: '',
        roles: ['ATeam'],
        signing_order: null,
        contact_id: 'mcru8Q4wMAvrbJuH2rMuoG',
        shared_link: '',
      },
    ],
    sent_by: {
      id: '4ucsYUMk9k2RyfBQo85NiZ',
      email: '<EMAIL>',
      first_name: 'Maily',
      last_name: 'Testson',
      membership_id: '6SH4Yshcdv79WgDNokoFZD',
    },
    template: {
      id: 'uzQV5wCVBHSFuQH3ra5aDc',
      name: 'MSA Cover Terms for [Company.Name]',
    },
    version: '2',
    linked_objects: [
      {
        id: '38121418-6a6d-444e-8fb2-3a93784cda6d',
        provider: 'hubspot',
        entity_type: 'company',
        entity_id: '26510089064',
      },
    ],
  },
};

export const serviceOrderPandadocWebhookDataFixture: PandadocDocumentWebhookPayload = {
  event: 'document_state_changed',
  data: {
    id: 'i8JdkHEJ2akErJzwBJjyUP',
    name: 'ServiceOrder for [Company.Name]',
    date_created: new Date('2024-12-20T09:40:36.056917Z'),
    date_modified: new Date('2024-12-20T09:40:36.710975Z'),
    expiration_date: null,
    autonumbering_sequence_name: null,
    created_by: {
      id: '4ucsYUMk9k2RyfBQo85NiZ',
      email: '<EMAIL>',
      first_name: 'Maily',
      last_name: 'Testson',
      membership_id: '6SH4Yshcdv79WgDNokoFZD',
    },
    fields: [
      {
        field_id: 'Signature1',
        uuid: '3e82d513-602a-4eb5-8235-a59f2f7578a5',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: '246TH9NiKMcvmq6qzyAorM',
          first_name: 'Maily',
          last_name: 'Testson',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'Date1',
        uuid: '45da2140-66b6-48b4-b886-8085351bab89',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: null,
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'EYwZh5YBtFAFKeQJNZsezH',
          first_name: 'Admin',
          last_name: 'Ateam',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
      {
        field_id: 'Signature2',
        uuid: 'd9c6b613-abf4-46f2-a435-9e440836151b',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'EYwZh5YBtFAFKeQJNZsezH',
          first_name: 'Admin',
          last_name: 'Ateam',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'Date2',
        uuid: 'e416c5df-d1eb-4f43-9cc3-f94875c341a7',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: null,
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: '246TH9NiKMcvmq6qzyAorM',
          first_name: 'Maily',
          last_name: 'Testson',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
    ],
    status: 'document.draft',
    recipients: [
      {
        type: 'recipient',
        id: '246TH9NiKMcvmq6qzyAorM',
        first_name: 'Maily',
        last_name: 'Testson',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: true,
        role: '',
        roles: ['Client'],
        signing_order: null,
        contact_id: 'LKthdowGfZHXTPb2JHbJZU',
        shared_link: '',
      },
      {
        type: 'recipient',
        id: 'EYwZh5YBtFAFKeQJNZsezH',
        first_name: 'Admin',
        last_name: 'Ateam',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: true,
        role: '',
        roles: ['ATeam'],
        signing_order: null,
        contact_id: 'mcru8Q4wMAvrbJuH2rMuoG',
        shared_link: '',
      },
    ],
    sent_by: null,
    grand_total: { amount: '0.00', currency: 'USD' },
    template: { id: 'TrSHZ9oTxtEpxLZUFUQQBf', name: 'ServiceOrder for [Company.Name]' },
    version: '2',
    linked_objects: [{ id: '56cabf92-36ab-4d60-b1a7-771532539244', provider: 'hubspot', entity_type: 'deal', entity_id: '29347911515' }],
  },
};

export const serviceOrderPandadocWebhookDataFixtureWithBillingForm: PandadocDocumentWebhookPayload = {
  event: 'document_state_changed',
  data: {
    id: 'fk95MXY4xLc2pHuHvi8xRS',
    name: 'SMB Service Order for OLIPOP: Website | Front-End',
    date_created: new Date('2025-02-13T15:58:58.650966Z'),
    date_modified: new Date('2025-02-13T15:58:59.862495Z'),
    expiration_date: null,
    autonumbering_sequence_name: null,
    created_by: {
      id: '4ucsYUMk9k2RyfBQo85NiZ',
      email: '<EMAIL>',
      first_name: 'Haris',
      last_name: 'Soljic',
      membership_id: '6SH4Yshcdv79WgDNokoFZD',
    },
    fields: [
      {
        field_id: 'Signature1',
        uuid: '2418f42a-9e03-476b-b93a-690d437dc9ab',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'billingUserName',
        uuid: '307eb37f-1b7c-4a64-b17e-cbdd762577f4',
        name: 'Deal.BillingUserName',
        title: '',
        placeholder: 'Enter name',
        value: 'Oliver Test',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
      {
        field_id: 'Date1',
        uuid: '690409e7-2818-4c7a-9c57-0f482313e60b',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: null,
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'gHyReyBxsvW6qLrMpPg82m',
          first_name: 'Admintest',
          last_name: 'Ateamtest',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
      {
        field_id: 'Signature2',
        uuid: '6de69a95-ba6c-4a28-85fa-af4308323d1f',
        name: 'Signature',
        title: '',
        placeholder: 'Signature',
        value: {},
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'gHyReyBxsvW6qLrMpPg82m',
          first_name: 'Admintest',
          last_name: 'Ateamtest',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'signature',
      },
      {
        field_id: 'Date2',
        uuid: '79c95e5e-0159-4a22-8e70-6aca5eae6845',
        name: 'Date',
        title: '',
        placeholder: 'Select date',
        value: null,
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'date',
      },
      {
        field_id: 'purchaseOrderNumber',
        uuid: '82739fab-c557-4474-9108-10f52fcdfdf7f',
        name: 'Deal.PurchaseOrderNumber',
        title: '',
        placeholder: 'Enter value',
        value: 'Test',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
      {
        field_id: 'billingEntityName',
        uuid: '82739fab-c557-4474-9108-10f5fcdfdf7f',
        name: 'Deal.BillingEntityName',
        title: '',
        placeholder: 'Enter value',
        value: 'Test',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
      {
        field_id: 'billingFederalID',
        uuid: 'd2c15ac6-ed19-4f3e-8546-9ca37631daa5',
        name: 'Deal.BillingFederalID',
        title: '',
        placeholder: 'Enter federal tax id',
        value: '1234567890',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
      {
        field_id: 'billingPaymentTerms',
        uuid: 'd643ee41-eccb-4c5e-bc62-9424f88865dc',
        name: 'Deal.BillingPaymentTerms',
        title: '',
        placeholder: 'Enter terms',
        value: 'Net15',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
      {
        field_id: 'billingEmail',
        uuid: 'f2f7676d-a6bb-48b4-8b52-a5c1acfec04b',
        name: 'Deal.BillingEmail',
        title: '',
        placeholder: '<EMAIL>',
        value: '<EMAIL>',
        assignee: '<EMAIL>',
        assignee_details: {
          type: 'recipient',
          id: 'qrWMmoorLwFQdsieBmqDmK',
          first_name: 'Haris',
          last_name: 'Soljic',
          email: '<EMAIL>',
          phone: '',
        },
        type: 'text',
      },
    ],
    status: 'document.draft',
    recipients: [
      {
        type: 'recipient',
        id: 'gHyReyBxsvW6qLrMpPg82m',
        first_name: 'Admintest',
        last_name: 'Ateamtest',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: false,
        role: '',
        roles: ['ATeam'],
        signing_order: null,
        contact_id: 'mcru8Q4wMAvrbJuH2rMuoG',
        shared_link: '',
      },
      {
        type: 'recipient',
        id: 'qrWMmoorLwFQdsieBmqDmK',
        first_name: 'Haris',
        last_name: 'Soljic',
        email: '<EMAIL>',
        phone: '',
        recipient_type: 'signer',
        has_completed: false,
        role: '',
        roles: ['Client'],
        signing_order: null,
        contact_id: 'LKthdowGfZHXTPb2JHbJZU',
        shared_link: '',
      },
    ],
    sent_by: null,
    grand_total: {
      amount: '0.00',
      currency: 'USD',
    },
    template: {
      id: 'jhNUs4Mqb3g8Dkgz9XpvRF',
      name: 'SMB Service Order for [Deal.DealName]',
    },
    version: '2',
    linked_objects: [
      {
        id: 'cb214b4a-88ba-40b4-9efb-3a44eb5a31f0',
        provider: 'hubspot',
        entity_type: 'deal',
        entity_id: '29347911515',
      },
    ],
  },
};
