import { RoleCategory, TalentCategory } from '@a_team/prisma';
import { GetAccountTeamAdvisor, ConfirmMissionDto, ConfirmRoleDto } from '@packages/contracts';

import { MissionWithPopulatedRoles } from '@modules/missions/helpers/types';

export const accountIdFixture = '6741bb926441991d7c625132';
export const roleIdFixture = '6741bb926441991d7c625133';
export const categoryIdFixture = '6741bb926441991d7c625134';

const now = new Date();

export const missionFixture: MissionWithPopulatedRoles = {
  accountId: accountIdFixture,
  applyStatus: null,
  attachedLinks: [],
  automatedStatusesDisabled: null,
  automaticInvoicingPeriod: null,
  bdOwners: [],
  billingPeriod: null,
  clientMargin: null,
  companyRequest: null,
  companyStory: 'Company background',
  createdAt: now,
  creatorUser: null,
  description: 'Test mission description',
  expectedDurationMonths: null,
  hidden: false,
  hubspotDealId: '***********',
  internalDescription: null,
  internalMission: null,
  invoiceEmailGreeting: null,
  invoicing: null,
  lastDeployedAt: null,
  logoURL: 'http://example.com/logo.png',
  mainManagerUserId: null,
  managers: [],
  mid: 'mission-1',
  migrations: [],
  missionSpecId: null,
  overlapMinutes: 30,
  owner: null,
  plannedStart: 'Next month',
  promotedTags: [],
  publishedAt: now,
  publisherUser: null,
  roles: [
    {
      availability: null,
      builderMonthlyRateMax: null,
      builderMonthlyRateMin: null,
      builderRateMax: null,
      builderRateMin: null,
      categoryId: 'category-1',
      collectMonthlyRate: null,
      createdAt: null,
      customQuestions: [],
      description: 'Role description',
      fullName: 'Role 1',
      hasPerformanceIssue: false,
      headline: 'Role headline',
      hourlyRate: null,
      id: 'role-id',
      isBadPerformance: null,
      isFullTimeRetainer: false,
      isLead: false,
      locations: [],
      lookingForApplications: false,
      margin: null,
      markup: null,
      marginVAT: null,
      monthlyRate: null,
      preferredSkills: [],
      removalRequest: null,
      requiredSkills: [],
      setAsScheduledToEndAt: null,
      showRateRangeToBuilders: null,
      status: 'Open',
      updatedAt: null,
      user: 'user-id',
      userAssignedAt: null,
      utcOffsetRange: null,
      visibility: null,
      workingHours: null,
      headlineHtml: null,
      productOfferings: [],
      roleDealType: null,
      isExpansion: false,
      hiddenFromUserIds: [],
      assignedUser: {
        id: 'user-id',
        firstName: 'John',
        lastName: 'Doe',
        pictureURL: 'http://example.com/picture.png',
      },
    },
  ],
  rolesMargin: null,
  setAsScheduledToEndAt: null,
  shortCompanyDescription: null,
  skipContracts: false,
  status: 'Spec',
  talentIndustries: [],
  testing: null,
  timezone: 'UTC',
  title: 'Test Mission',
  updatedAt: now,
  videoURL: 'http://example.com/video.mp4',
  website: null,
};

export const createMissionFixture: ConfirmMissionDto = {
  title: 'Lifecycle Mission',
  description: 'Test Description',
  companyStory: 'Test Company Story',
  plannedStart: 'Next month',
  timezone: 'UTC',
  timeOverlap: '2h',
  logoURL: 'https://example.com/logo.jpg',
  videoURL: 'https://example.com/video.mp4',
  roles: [
    {
      id: roleIdFixture,
      status: 'Open',
      categoryId: categoryIdFixture,
      headline: 'Test Role',
      locations: [],
      preferredSkills: [],
      requiredSkills: [],
      timeCommitment: '40h',
      budgetType: 'hourly',
      budget: 100,
      markupPercentage: 20,
    },
  ],
};

export const roleCategoriesFixture: RoleCategory[] = [
  { id: '6741bb926441991d7c625101', title: 'Developer', anchors: [], deletedAt: null, group: null, talentCategoryIds: [] },
  { id: '6741bb926441991d7c625102', title: 'Designer', anchors: [], deletedAt: null, group: null, talentCategoryIds: [] },
];

export const talentSkillsFixture: TalentCategory[] = [
  {
    id: '6741bb926441991d7c626101',
    textId: 'frontend',
    name: 'JavaScript',
    nodeType: 'skill',
    parentTalentCategoryIds: ['parent-id'],
    deletedAt: null,
    aliases: [],
    isProgrammingLanguage: true,
    isVettingEligible: false,
  },
  {
    id: '6741bb926441991d7c626102',
    textId: 'backend',
    name: 'Python',
    nodeType: 'skill',
    parentTalentCategoryIds: ['parent-id'],
    deletedAt: null,
    aliases: [],
    isProgrammingLanguage: true,
    isVettingEligible: false,
  },
];

export const requestRoleFixture: ConfirmRoleDto = {
  id: '6741bb926441991d7c626103',
  status: 'Open',
  categoryId: categoryIdFixture,
  headline: 'New Role',
  locations: [],
  preferredSkills: [],
  requiredSkills: [],
  timeCommitment: '40h',
  budgetType: 'hourly',
  budget: 100,
  markupPercentage: 20,
};

export const teamAdvisorFixture: GetAccountTeamAdvisor = {
  id: '6741bb926441991d7c626103',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  accessLevel: 'bdAdmin',
  pictureURL: null,
  username: '',
};
