import { Company, MissionPrefill, MissionPrefillRole, Solution, User, Prisma } from '@a_team/prisma';

import { MissionPrefillRoleSkills } from '@modules/missions/missions-prefill.repository';
import { PrefillLLMResponse, PrefillRoleLLMResponse } from '@modules/missions/missions-prefill.service';
import { RoleCategoryInfo } from '@modules/missions/missions.repository';

export const categoryIdFixture = '5ea6a4e3baf6a185bbdf6158';

export const roleHeadlineFixture = 'Role headline';

export const roleScreeningQuestionFixture = 'Role question';

const skillId1 = '60c21b9b4d57fbdb0341bf37';

const skillId2 = '60c21b9b4d57fbdb0341bf3c';

const skillId3 = '60c21b9b4d57fbdb0341bf40';

const skillId4 = '60c21b9b4d57fbdb0341bf46';

export const prefillRoleSkillsFixture: MissionPrefillRoleSkills = {
  requiredSkills: [skillId1, skillId2, skillId4],
  preferredSkills: [skillId3],
};

export const llmRolesResponseFixture: { object: PrefillRoleLLMResponse[] } = {
  object: [
    {
      categoryId: categoryIdFixture,
      headline: roleHeadlineFixture,
      screeningQuestion: roleScreeningQuestionFixture,
    },
  ],
};

export const missionPrefillRoleFixture: MissionPrefillRole = {
  categoryId: categoryIdFixture,
  headline: roleHeadlineFixture,
  timeCommitment: null,
  screeningQuestion: roleScreeningQuestionFixture,
  ...prefillRoleSkillsFixture,
};

export const missionPrefillFixture: MissionPrefill = {
  id: '22',
  userId: '1',
  timezone: 'UTC',
  plannedStart: 'Next month',
  companyDescription: 'Company background',
  missionDescription: 'Test mission description',
  openRoles: [missionPrefillRoleFixture],
  missionName: 'Test Mission',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const clientRegistrationFixture: User['clientRegistration'] = {
  emailVerified: true,
  signupCompany: '123',
  companyQualified: true,
  requestType: 'BUILD_PRODUCT',
  requestSolution: '6474fc27ae4d2b5f186e923c',
  requestRoles: [categoryIdFixture],
  requestHelp: 'I need help with this',
  howDidYouHear: 'Google',
  hiringTimeline: 'immediate',
};

export const companyEnrichmentFixture: Company['enrichment'] = {
  structured: {
    name: 'Company name',
    timezone: 'UTC',
    industries: [],
    countryCode: null,
    city: null,
    companyStage: null,
    logoUrl: null,
    employeeRange: null,
    fundingRange: null,
    revenueRange: null,
  },
  clearbit: {
    data: {
      description: 'Company description',
    },
  },
  apollo: null,
};

export const solutionFixture: Solution = {
  id: '6474fc26ae4d2b5f186e922e',
  preset: 'web_platform',
  title: 'Web Platform',
  roles: [
    {
      category: categoryIdFixture,
    },
  ],
};

export const roleCategoryFixture: RoleCategoryInfo = {
  id: categoryIdFixture,
  title: 'Software Development',
};

export const llmResponseFixture: { object: PrefillLLMResponse } = {
  object: {
    project: {
      title: 'Test Mission',
      description: 'Test mission description',
      plannedStart: 'Next month',
    },
    company: {
      name: 'Company name',
      description: 'Company description',
      timezone: 'UTC',
    },
  },
};

export const userCreationDataFixture: Prisma.UserCreateInput = {
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  type: 'companyUser',
  status: 'Active',
  tokenVersion: 2,
  pictureURL: 'https://example.com/john-doe.jpg',
};

export const usersCreationDataFixture: Prisma.UserCreateInput[] = [
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId1 }, { talentSkillId: skillId2 }],
      },
    },
  },
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId4 }],
      },
    },
  },
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId2 }, { talentSkillId: skillId1 }],
      },
    },
  },
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId1 }],
      },
    },
  },
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId3 }, { talentSkillId: skillId4 }],
      },
    },
  },
  {
    ...userCreationDataFixture,
    type: 'user',
    talentProfile: {
      mainTalentSpecializationId: categoryIdFixture,
      talentSkills: {
        mainTalentSkills: [{ talentSkillId: skillId4 }, { talentSkillId: skillId2 }],
      },
    },
  },
];
