import { MissionPrefill } from '@a_team/prisma';

export const userIdFixture = '6741bb926441991d7c625133';

export const categoryIdFixture = '5ea6a4e3baf6a185bbdf6158';

export const skillId1Fixture = '123';
export const skillId2Fixture = '456';
export const skillId3Fixture = '789';
export const skillId4Fixture = '111';
export const skillId5Fixture = '222';

export const skillList1Fixture = [
  { talentSkillId: skillId1Fixture, rating: 5 },
  { talentSkillId: skillId1Fixture, rating: 5 },
  { talentSkillId: skillId1Fixture, rating: 5 },
  { talentSkillId: skillId1Fixture, rating: 5 },
  { talentSkillId: skillId1Fixture, rating: 5 },
];

export const skillList2Fixture = [
  { talentSkillId: skillId2Fixture, rating: 5 },
  { talentSkillId: skillId2Fixture, rating: 5 },
  { talentSkillId: skillId2Fixture, rating: 5 },
  { talentSkillId: skillId2Fixture, rating: 5 },
];

export const skillList3Fixture = [
  { talentSkillId: skillId3Fixture, rating: 5 },
  { talentSkillId: skillId3Fixture, rating: 5 },
  { talentSkillId: skillId3Fixture, rating: 5 },
];

export const skillList4Fixture = [
  { talentSkillId: skillId4Fixture, rating: 5 },
  { talentSkillId: skillId4Fixture, rating: 5 },
];

export const skillList5Fixture = [{ talentSkillId: skillId5Fixture, rating: 5 }];

export const prefillUpdateDataFixture: Partial<MissionPrefill> = {
  missionName: 'test',
  missionDescription: 'description',
  timezone: 'test/test',
  companyDescription: 'company description',
  plannedStart: 'next_month',
};
