import { HubspotDeal } from '@common/hubspot/schemas';
import { MissionWithPopulatedRoles } from '@modules/missions/helpers/types';

import { missionFixture } from './common.fixtures';

export const updatedMissionFixture: MissionWithPopulatedRoles = {
  ...missionFixture,
  title: 'Title Updated',
};

export const confirmedMissionFixture: MissionWithPopulatedRoles = {
  ...missionFixture,
  status: 'Formation',
};

export const publishMissionFixture: MissionWithPopulatedRoles = {
  ...missionFixture,
  status: 'Created',
};

export const hubspotDealFixture: HubspotDeal = {
  id: 'deal-1',
  name: 'Test Hubspot Deal',
  createdAt: new Date(),
  missionId: null,
  roleId: null,
  associations: null,
  class: null,
  contactSource: null,
  industry: null,
  industryCompany: null,
  leadOwner: null,
  leadType: null,
  medium: null,
  productOfferings: null,
  referralDetails: null,
  referralSource: null,
  typeOfTeam: null,
};
