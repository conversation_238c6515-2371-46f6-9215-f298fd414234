import { TalentIndustry, User } from '@a_team/prisma';

export const talentIndustriesFixture: TalentIndustry[] = [
  {
    id: '6741bb926441991d7c625101',
    name: 'Engineering',
    description: 'Engineering description',
    textId: 'eng',
  },
  {
    id: '6741bb926441991d7c625102',
    name: 'Design',
    description: 'Design description',
    textId: 'design',
  },
  {
    id: '6741bb926441991d7c625103',
    name: 'Marketing',
    description: 'Marketing description',
    textId: 'marketing',
  },
];

export const tfsOwnersFixture: User[] = [
  {
    id: 'user-id-1',
    firstName: 'John',
    lastName: 'Frist',
    pictureURL: 'https://mock-url-1.com',
    username: null,
    email: '',
    isAdmin: false,
    type: '',
    status: 'Registered',
    createdAt: new Date(),
    titles: [],
    scrubbed: null,
    tokenVersion: 0,
    aboutMe: null,
    cvURL: null,
    onboardingStage: null,
    yearsExperience: null,
    websites: [],
    clientRegistration: null,
    profileCompleteness: null,
    linkedin: null,
    rateRange: null,
    talentProfile: null,
    missionPreferences: null,
    availability: null,
    github: null,
    dribbble: null,
    location: null,
    exclusiveApplication: null,
    platformExperience: null,
  },
  {
    id: 'user-id-2',
    firstName: 'John',
    lastName: 'Second',
    pictureURL: 'https://mock-url-2.com',
    username: null,
    email: '',
    isAdmin: false,
    type: '',
    status: 'Registered',
    createdAt: new Date(),
    titles: [],
    scrubbed: null,
    tokenVersion: 0,
    aboutMe: null,
    cvURL: null,
    onboardingStage: null,
    yearsExperience: null,
    websites: [],
    clientRegistration: null,
    profileCompleteness: null,
    linkedin: null,
    rateRange: null,
    talentProfile: null,
    missionPreferences: null,
    availability: null,
    github: null,
    dribbble: null,
    location: null,
    exclusiveApplication: null,
    platformExperience: null,
  },
];
