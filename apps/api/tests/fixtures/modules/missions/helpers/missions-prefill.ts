import { RoleCategoryInfo } from '@modules/missions/missions.repository';

export const clientRequestTypePrompt = (type: string) => `The client is looking to ${type}`;

export const clientTimelinePrompt = (timeline?: string | null) => `The client's timeline to hire is ${timeline}`;

export const clientRequestHelpPrompt = (requestHelp?: string | null) =>
  `The client provided this description when asked what they are trying to build: ${requestHelp}`;

export const clientSolutionPrompt = (solution: string) => `The client indicated they are looking to build a ${solution}`;

export const companyNamePrompt = (name?: string | null) => `The company name is ${name}`;

export const companyIndustriesPrompt = (industries?: string) => `The company industries are ${industries}`;

export const companyDescriptionPrompt = (description: string) => `The company description is: ${description}`;

export const rolesPrompt = (roles: RoleCategoryInfo[]) =>
  `${roles.map((role, i) => `* Role ${i + 1} is ${role.title} with category id ${role.id}`).join('\n')}`;
