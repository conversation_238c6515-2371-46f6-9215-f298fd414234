import { Prisma } from '@a_team/prisma';

import { calendarFixture } from '.';

// DB errors
export const calendarDbError = new Error('DB Error');
export const createCalendarError = new Error('Creation failed');
export const updateCalendarError = new Error('Update failed');

// Prisma record not found error
export const recordNotFoundError = new Prisma.PrismaClientKnownRequestError('Record not found', {
  code: 'P2025',
  clientVersion: '1.0',
});

// Create data
export const createCalendarData: Prisma.CalendarUncheckedCreateInput = {
  calendarId: calendarFixture.id,
  userId: calendarFixture.userId,
  provider: calendarFixture.provider,
  credentials: calendarFixture.credentials,
};

// Update data
export const updateCalendarData: Prisma.CalendarUpdateInput = {
  credentials: {
    access_token: 'new-access-token',
  },
};
