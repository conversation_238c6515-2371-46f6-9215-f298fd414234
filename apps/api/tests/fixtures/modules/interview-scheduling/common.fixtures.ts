import { Calendar<PERSON>rovider } from '@a_team/prisma';

const now = 1745587219298;

// Calendar fixture
export const calendarFixture = {
  id: '6741bb926441991d7c626103',
  userId: '62b31586f2704800118b36c6',
  calendarId: 'primary',
  provider: CalendarProvider.google,
  credentials: {
    access_token: 'test-access-token',
    refresh_token: 'test-refresh-token',
    id_token: 'test-id-token',
    expiry_date: now + 3600000, // 1 hour from now
  },
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Calendar with null credentials
export const nullCredentialsCalendarFixture = {
  ...calendarFixture,
  credentials: {
    access_token: null,
    refresh_token: null,
    id_token: null,
    expiry_date: null,
  },
};

// Calendar with only id_token
export const idTokenOnlyCalendarFixture = {
  ...calendarFixture,
  credentials: {
    access_token: null,
    refresh_token: null,
    id_token: 'valid-id-token',
    expiry_date: null,
  },
};

// Calendar with only access_token
export const accessTokenOnlyCalendarFixture = {
  ...calendarFixture,
  credentials: {
    access_token: 'valid-access-token',
    refresh_token: null,
    id_token: null,
    expiry_date: null,
  },
};

// Updated calendar with new credentials
export const updatedCalendarFixture = {
  ...calendarFixture,
  credentials: {
    access_token: 'new-access-token',
    refresh_token: 'new-refresh-token',
    id_token: 'new-id-token',
    expiry_date: now + 3600000,
  },
};

export const calendarAvailabilityFixture = {
  id: 'cal-avail-id-1',
  userId: 'user-123',
  eventTypeId: 'event-type-456',
  startTime: '09:00',
  endTime: '17:00',
  day: 1, // Monday
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
};

export const updatedCalendarAvailabilityFixture = {
  ...calendarAvailabilityFixture,
  startTime: '10:00',
  endTime: '18:00',
  updatedAt: new Date('2023-01-02T00:00:00Z'),
};

export const createCalendarAvailabilityData = {
  userId: 'user-123',
  eventTypeId: 'event-type-456',
  startTime: '09:00',
  endTime: '17:00',
  day: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
};

export const updateCalendarAvailabilityData = {
  startTime: '10:00',
  endTime: '18:00',
};

export const calendarAvailabilityDbError = new Error('Database error occurred');
export const createCalendarAvailabilityError = new Error('Error creating calendar availability');
export const updateCalendarAvailabilityError = new Error('Error updating calendar availability');
export const deleteCalendarAvailabilityError = new Error('Error deleting calendar availability');
export const recordNotFoundError = new Error('Record not found');
