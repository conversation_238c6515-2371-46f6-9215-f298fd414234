import { CalendarProvider } from '@a_team/prisma';

import { Errors } from '@lib/errors';
import { AuthenticatedRequest } from '@lib/guards/jwt.guard';

// Mock authenticated request
export const mockRequest = {
  user: { id: 'test-user-id' },
} as AuthenticatedRequest;

// Mock query parameters for Google auth callback
export const mockGoogleAuthCallbackQuery = {
  code: 'test-code',
  state: 'test-state',
};

// Mock successful responses
export const authUrlResponseFixture = { authUrl: 'https://google-auth-url.com' };
export const connectedStatusFixture = {
  isConnected: true,
  provider: CalendarProvider.google,
};

export const disconnectedCalendartatusFixture = {
  isConnected: false,
  provider: null,
};

// Mock error responses

export const unexpectedGoogleCallbackErrorFixture = Errors.createError('CREATE_CALENDAR_DB_ERROR' as const, {
  isUnexpectedError: true,
  originalError: new Error('Callback error'),
});

export const expectedGoogleCallbackErrorFixture = Errors.createError('HANDLE_GOOGLE_CALLBACK_INVALID_STATE' as const);

export const unexpectedCalendarStatusErrorFixture = Errors.createError('FIND_CALENDAR_BY_USER_ID_DB_ERROR' as const, {
  isUnexpectedError: true,
  originalError: new Error('Status error'),
});

export const expectedCalendarStatusErrorFixture = Errors.createError('FIND_CALENDAR_BY_USER_ID_NOT_FOUND' as const);

export const unexpectedCalendarDisconnectErrorFixture = Errors.createError('UPDATE_CALENDAR_BY_ID_DB_ERROR' as const, {
  isUnexpectedError: true,
  originalError: new Error('Internal server error'),
});

export const expectedCalendarDisconnectErrorFixture = Errors.createError('UPDATE_CALENDAR_BY_ID_NOT_FOUND' as const);
