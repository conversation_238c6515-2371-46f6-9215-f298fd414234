import { Proposal } from '@a_team/prisma';

export const proposalFixture: Proposal = {
  id: '67783fec57e6850013e888d2',
  adminReview: {
    status: 'approved',
    reviewedBy: '61f9c68f3c61f4001633714c',
    reviewedAt: new Date('2025-01-03T20:10:37.464Z'),
    rejectionNote: null,
    rejectionReason: null,
  },
  applications: ['67783fd19cbb4c0014d62fcc'],
  candidates: [
    {
      hourlyRate: 125,
      aid: '67783fd19cbb4c0014d62fcc',
      userId: '61f9c68f3c61f4001633714c',
      roleCategoryId: '5ebaad1dcc2ec395b9511948',
      monthlyRate: 31250,
      showHourlyRate: true,
      rid: 'role-id',
      fullName: 'Test',
      clientReview: null,
      includeCustomQuestionReply: false,
      projects: [],
      recommendation: null,
      reviews: [],
      roleTitle: 'Role',
      showMonthlyRate: false,
      cardSections: {
        experience: {
          text: '',
          title: '',
        },
        extraSkill: {
          text: '',
          title: '',
        },
        requirements: {
          text: '',
          title: '',
        },
      },
      isTeamLead: false,
      proposedAt: new Date('2025-01-03T19:52:12.178Z'),
      githubUrl: 'https://github.com/github',
      cvUrl: 'https://example.com/cv.pdf',
      tfsPitch: {
        blurb: 'blurb',
        website: 'https://example.com',
      },
      portfolioUrl: 'https://example.com/portfolio',
      portfolioPassword: 'password',
      slug: null,
      linkedInUrl: 'linkedin.com/in/test1',
    },
    {
      hourlyRate: 125,
      aid: '67783fd19cbb4c0014d62fcc',
      userId: '61f9c68f3c61f4001633714c',
      roleCategoryId: '5ebaad1dcc2ec395b9511948',
      monthlyRate: 31250,
      showHourlyRate: true,
      rid: '67730b19f69c4e36b893bc01',
      fullName: 'Test',
      clientReview: null,
      includeCustomQuestionReply: false,
      projects: [],
      recommendation: null,
      reviews: [],
      roleTitle: 'Role',
      showMonthlyRate: false,
      cardSections: {
        experience: {
          text: '',
          title: '',
        },
        extraSkill: {
          text: '',
          title: '',
        },
        requirements: {
          text: '',
          title: '',
        },
      },
      isTeamLead: false,
      proposedAt: new Date('2025-01-03T19:52:12.178Z'),
      githubUrl: 'https://github.com/github',
      cvUrl: 'https://example.com/cv.pdf',
      tfsPitch: {
        blurb: 'blurb',
        website: 'https://example.com',
      },
      portfolioUrl: null,
      portfolioPassword: null,
      slug: null,
      linkedInUrl: 'linkedin.com/in/test2',
    },
    {
      hourlyRate: 125,
      aid: '67783fd19cbb4c0014d62fcc',
      userId: '61f9c68f3c61f4001633714c',
      roleCategoryId: '5ebaad1dcc2ec395b9511948',
      monthlyRate: 31250,
      showHourlyRate: true,
      rid: 'role-id',
      fullName: 'Test',
      clientReview: null,
      includeCustomQuestionReply: false,
      projects: [],
      recommendation: null,
      reviews: [],
      roleTitle: 'Role',
      showMonthlyRate: false,
      cardSections: {
        experience: {
          text: '',
          title: '',
        },
        extraSkill: {
          text: '',
          title: '',
        },
        requirements: {
          text: '',
          title: '',
        },
      },
      isTeamLead: false,
      proposedAt: new Date('2025-01-03T19:52:12.178Z'),
      githubUrl: 'https://github.com/github',
      cvUrl: 'https://example.com/cv.pdf',
      tfsPitch: {
        blurb: 'blurb',
        website: 'https://example.com',
      },
      portfolioUrl: null,
      portfolioPassword: null,
      slug: null,
      linkedInUrl: 'linkedin.com/in/test3',
    },
  ],
  clientName: 'TestClient',
  createdAt: new Date('2025-01-03T19:52:12.178Z'),
  createdBy: '61f9c68f3c61f4001633714c',
  isShared: true,
  missionId: 'mission-1',
  projectName: 'Test: Hello',
  publicURL: 'https://client-sandbox.a.team/mission/67730aa911599960740e9d9d/proposals/67783fec57e6850013e888d2',
  sharedAt: new Date('2025-01-03T20:10:37.682Z'),
  sharedBy: '61f9c68f3c61f4001633714c',
  version: 2,
  webflowId: null,
  archivedURL: null,
  expiresAt: null,
  missionSpecId: null,
  publicUntil: null,
  schemaVersion: 'v2',
  template: 'sampleV2',
  templateMap: null,
  updatedAt: null,
  currency: null,
  teamProposal: null,
  rolesData: [],
  name: null,
  slug: null,
  teamBlurb: null,
  teamAdvisorId: '61f9c68f3c61f4001633714c',
};
