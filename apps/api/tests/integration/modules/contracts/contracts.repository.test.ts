import { Contract, ContractPartyType, ContractStatus, ContractType } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { DbService } from '@lib/global/db.service';
import { ContractsRepository } from '@modules/contracts/contracts.repository';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

const contractDataFixture: Contract = {
  type: ContractType.ServiceOrder,
  status: ContractStatus.Created,
  downloadURL: 'https://cdn.a.team/contracts/standard/v2/ATeams-Builder-Agreement.pdf',
  parties: [
    {
      type: ContractPartyType.BillingCustomer,
      user: '606334c3e1840a0012601d62',
    },
  ],
} as Contract;

describe('ContractsRepository', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;

  let repository: ContractsRepository;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContractsRepository,
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
      ],
    }).compile();

    repository = module.get<ContractsRepository>(ContractsRepository);
    dbService = module.get<DbService>(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.contract.deleteMany({});
  });

  describe('findMany', () => {
    it('Should find contracts for the specific account', async () => {
      const TEST_ACCOUNT_ID = '606334c3e1840a0012601d62';

      await dbService.contract.create({ data: contractDataFixture });
      await dbService.contract.create({ data: { ...contractDataFixture, accountId: TEST_ACCOUNT_ID } });
      await dbService.contract.create({ data: { ...contractDataFixture, accountId: '606334c3e1840a0012601d63' } });

      const result = await repository.findMany(TEST_ACCOUNT_ID, 0, 10);

      expect(result.contracts).toHaveLength(1);
    });
  });

  describe('updateContractParty', () => {
    it('Should update contract party signedAt', async () => {
      const contract = await dbService.contract.create({ data: contractDataFixture });
      const userId = contractDataFixture.parties[0].user;
      const partyData = { signedAt: new Date() };

      const result = await repository.updateContractParty(contract.sid, userId!, partyData);

      expect(result.parties[0].signedAt).toBeDefined();
    });
  });

  describe('updateContractTitleAndSignedAt', () => {
    it('Should update contract party signed at and title', async () => {
      const contract = await dbService.contract.create({ data: contractDataFixture });
      const newSignedAt = new Date();
      newSignedAt.setDate(newSignedAt.getDate() + 5);

      const result = await repository.updateContractProperties(contract.sid, {
        documentTitle: 'New title',
        type: 'MasterServicesAgreement',
        signedAt: newSignedAt,
      });

      expect(result.documentTitle).toEqual('New title');
      expect(result.parties[0].signedAt).toEqual(newSignedAt);
      expect(result.type).toEqual('MasterServicesAgreement');
    });
  });
});
