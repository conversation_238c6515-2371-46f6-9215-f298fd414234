import { Test, TestingModule } from '@nestjs/testing';

import { CommonUsersRepository } from '@common/users/users.repository';
import { DbService } from '@lib/global/db.service';
import { MissionsRepository } from '@modules/missions/missions.repository';
import { ProposalsRepository } from '@modules/proposals/proposals.repository';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

describe('ProposalsRepository', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;

  let repository: ProposalsRepository;

  beforeAll(async () => {
    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProposalsRepository,
        MissionsRepository,
        CommonUsersRepository,
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
      ],
    }).compile();

    repository = module.get<ProposalsRepository>(ProposalsRepository);
    dbService = module.get<DbService>(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.proposal.deleteMany({});
  });

  describe('findById', () => {
    it('should retrieve a proposal from the db', async () => {
      const testId = '67783fec57e6850013e888d2';
      await dbService.proposal.createMany({
        data: {
          id: testId,
          clientName: 'Test',
        },
      });

      const proposal = await repository.findById(testId);

      expect(proposal.isOk()).toBe(true);
      expect(proposal.isOk() && proposal.value).toEqual(
        expect.objectContaining({
          id: testId,
          clientName: 'Test',
          adminReview: null,
        })
      );
    });
  });
});
