import { Prisma } from '@a_team/prisma';
import { Test, TestingModule } from '@nestjs/testing';

import { categoryIdFixture, roleIdFixture, roleCategoriesFixture, talentSkillsFixture } from '@fixtures/modules/missions/common.fixtures';
import { DbService } from '@lib/global/db.service';
import { MissionsRepository } from '@modules/missions/missions.repository';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

describe('MissionsRepository', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;

  let repository: MissionsRepository;

  beforeAll(async () => {
    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissionsRepository,
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
      ],
    }).compile();

    repository = module.get<MissionsRepository>(MissionsRepository);
    dbService = module.get<DbService>(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.mission.deleteMany({});
    await dbService.roleCategory.deleteMany({});
    await dbService.talentCategory.deleteMany({});
  });

  it('should create the mission with the required properties, update and check if properties are updated', async () => {
    const missionData: Prisma.MissionCreateInput = {
      title: 'Test title mission',
      description: 'Test description',
      status: 'Created',
      roles: [
        {
          id: roleIdFixture,
          headline: 'Test role headline',
          categoryId: categoryIdFixture,
          status: 'Open',
        },
      ],
    };

    const createResult = await repository.createMission(missionData);

    expect(createResult.isOk()).toBe(true);

    const createdMission = createResult._unsafeUnwrap();

    expect(createdMission).toBeDefined();
    expect(createdMission.status).toBe('Created');
    expect(createdMission.roles).toHaveLength(1);
    expect(createdMission.roles[0].id).toBe(roleIdFixture);

    const foundMissionResult = await repository.findMissionById(createdMission.mid);

    expect(foundMissionResult.isOk()).toBe(true);

    if (foundMissionResult.isOk()) {
      const foundMission = foundMissionResult.value;

      expect(foundMission).toBeDefined();
      expect(foundMission?.title).toBe(missionData.title);
    }

    const updateMissionData = {
      title: 'Updated integration mission',
      description: 'Updated integration description',
    };

    const updatedMissionResult = await repository.updateMission(createdMission.mid, updateMissionData);

    if (updatedMissionResult.isOk()) {
      const updatedMission = updatedMissionResult.value;

      expect(updatedMission.title).toBe(updateMissionData.title);
      expect(updatedMission.description).toBe(updateMissionData.description);
      expect(updatedMission.roles).toEqual(createdMission.roles);
    }

    const updateMissionRoleData = {
      headline: 'Updated integration headline',
    };

    const updatedMissionWithRoleResult = await repository.updateMissionRole(createdMission.mid, roleIdFixture, updateMissionRoleData);

    expect(updatedMissionWithRoleResult.isOk()).toBe(true);

    if (updatedMissionWithRoleResult.isOk()) {
      const updatedMissionWithRole = updatedMissionWithRoleResult.value;

      expect(updatedMissionWithRole.roles[0].headline).toBe(updateMissionRoleData.headline);
      expect(updatedMissionWithRole.roles[0].status).toBe('Open');
    }
  });

  it('should add a role to the mission and verify the role is added', async () => {
    const missionData: Prisma.MissionCreateInput = {
      title: 'Test title mission',
      description: 'Test description',
      status: 'Created',
    };

    const createResult = await repository.createMission(missionData);

    expect(createResult.isOk()).toBe(true);

    const createdMission = createResult._unsafeUnwrap();

    expect(createdMission).toBeDefined();

    const roleData: Prisma.MissionRoleCreateInput = {
      id: roleIdFixture,
      headline: 'New Role',
      categoryId: categoryIdFixture,
      status: 'Pending',
    };

    const updatedMissionResult = await repository.addRoleToMission(createdMission.mid, roleData);

    expect(updatedMissionResult.isOk()).toBe(true);

    if (updatedMissionResult.isOk()) {
      const updatedMission = updatedMissionResult.value;

      expect(updatedMission.roles).toHaveLength(1);
      expect(updatedMission.roles[0].id).toBe(roleIdFixture);
      expect(updatedMission.roles[0].headline).toBe(roleData.headline);
    }
  });

  it('should retrieve all role categories', async () => {
    await dbService.roleCategory.createMany({
      data: roleCategoriesFixture,
    });

    const categoriesResult = await repository.findAllRoleCategories();

    expect(categoriesResult.isOk()).toBe(true);

    if (categoriesResult.isOk()) {
      const categories = categoriesResult.value;

      expect(categories).toHaveLength(2);
      expect(categories.map((c) => c.title)).toContain('Developer');
      expect(categories.map((c) => c.title)).toContain('Designer');
    }
  });

  it('should retrieve only skill-type talent categories', async () => {
    await dbService.talentCategory.createMany({
      data: talentSkillsFixture,
    });

    const skillsResult = await repository.findAllTalentSkills();

    expect(skillsResult.isOk()).toBe(true);

    if (skillsResult.isOk()) {
      const skills = skillsResult.value;

      expect(skills).toHaveLength(2);
      expect(skills.map((s) => s.name)).toContain('JavaScript');
      expect(skills.map((s) => s.name)).toContain('Python');
      expect(skills.map((s) => s.name)).not.toContain('Angular');
    }
  });
});
