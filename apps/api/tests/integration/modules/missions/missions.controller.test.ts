import { Prisma } from '@a_team/prisma';
import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { RequestRoleRemovalSchemaDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonAccountsService } from '@common/accounts/accounts.service';
import { CommonHubspotService } from '@common/hubspot/hubspot.service';
import { CommonMailService } from '@common/mail/mail.service';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonSlackService } from '@common/slack/slack.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { categoryIdFixture, roleIdFixture } from '@fixtures/modules/missions/common.fixtures';
import { UploadcareClient } from '@lib/clients/uploadcare.client';
import { DbService } from '@lib/global/db.service';
import { SentryService } from '@lib/global/sentry.service';
import { MissionsController } from '@modules/missions/missions.controller';
import { MissionsRepository } from '@modules/missions/missions.repository';
import { MissionsService } from '@modules/missions/missions.service';
import { RoleRatesService } from '@modules/missions/role-rates.service';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

import { AccountJwtGuard, AuthenticatedAccountRequest } from '@lib/guards/account-jwt.guard';
import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

const mockReq = {
  user: { id: 'user‑1' },
  account: { id: 'account‑1' },
} as AuthenticatedAccountRequest;

describe('MissionsController', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;
  let controller: MissionsController;

  beforeAll(async () => {
    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MissionsController],
      providers: [
        MissionsService,
        MissionsRepository,
        CommonAccountsService,
        CommonAccountsRepository,
        CommonUsersRepository,
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
        {
          provide: CommonHubspotService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: CommonMailService,
          useValue: {
            sendEmail: jest.fn().mockResolvedValue(ok({ id: 'mock-email-id' })),
            getTemplateId: jest.fn(),
          },
        },
        {
          provide: CommonSlackService,
          useValue: {
            sendMessage: jest.fn(),
            findUserByEmail: jest.fn().mockResolvedValue(ok({ id: 'user-id' })),
          },
        },
        {
          provide: RoleRatesService,
          useValue: {
            getRoleRateGuidance: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {},
        },
        {
          provide: UploadcareClient,
          useValue: {
            generateVideoThumbnailByUrl: jest.fn().mockResolvedValue({}),
            deleteFileByUrl: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: CommonRBACService,
          useValue: {
            checkClientPermission: jest.fn().mockResolvedValue(ok()),
          },
        },
      ],
    })
      .overrideGuard(AccountJwtGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(AdminJwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<MissionsController>(MissionsController);
    dbService = module.get<DbService>(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.user.deleteMany({});
    await dbService.account.deleteMany({});
    await dbService.mission.deleteMany({});
  });

  it('should successfully request role removal for an existing mission', async () => {
    const teamAdvisorUser = await dbService.user.create({
      data: { email: '<EMAIL>', firstName: 'TA', status: 'Active', type: 'companyUser', scrubbed: 'Verified', tokenVersion: 1 },
    });

    const accountData: Prisma.AccountCreateInput = {
      members: [{ user: teamAdvisorUser.id, accessLevel: 'bdAdmin' }],
    };

    const createdAccount = await dbService.account.create({ data: accountData });
    const missionData: Prisma.MissionCreateInput = {
      title: 'Test Mission',
      status: 'Formation',
      accountModel: {
        connect: { id: createdAccount.id },
      },
      roles: [
        {
          id: roleIdFixture,
          categoryId: categoryIdFixture,
          status: 'Open',
        },
      ],
    };

    const createdMission = await dbService.mission.create({ data: missionData });
    const roleRemovalPayload: RequestRoleRemovalSchemaDto = {
      reason: 'Budget constraints',
      comment: 'Reduce the number of roles to save costs',
    };

    const result = await controller.requestRoleRemoval(mockReq)({
      params: { id: createdMission.mid, roleId: roleIdFixture },
      body: roleRemovalPayload,
    });

    expect(result.status).toBe(HttpStatus.OK);
    expect(result.body).toBeUndefined();

    const updatedMission = await dbService.mission.findUnique({
      where: { mid: createdMission.mid },
    });

    expect(updatedMission?.roles[0].status).toBe('Canceled');
    expect(updatedMission?.roles[0].removalRequest).toEqual({ ...roleRemovalPayload, createdAt: expect.any(Date) });
  });
});
