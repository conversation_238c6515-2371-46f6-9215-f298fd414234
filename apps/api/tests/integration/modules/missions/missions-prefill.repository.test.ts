import { Test, TestingModule } from '@nestjs/testing';

import { prefillUpdateDataFixture, userIdFixture } from '@fixtures/modules/missions/missions-prefill.repository.fixtures';
import { DbService } from '@lib/global/db.service';
import { MissionsPrefillRepository } from '@modules/missions/missions-prefill.repository';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

describe('MissionsPrefillRepository', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;

  let repository: MissionsPrefillRepository;

  beforeAll(async () => {
    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissionsPrefillRepository,
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
      ],
    }).compile();

    repository = module.get<MissionsPrefillRepository>(MissionsPrefillRepository);
    dbService = module.get<DbService>(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.missionPrefill.deleteMany({});
  });

  it('Should create a mission prefill with pending status', async () => {
    const missionPrefillData = {
      userId: userIdFixture,
    };

    const createdMissionPrefill = await repository.upsertMissionPrefill(missionPrefillData.userId);
    expect(createdMissionPrefill).toBeDefined();
  });

  it('Should create and update a mission prefill, and check fields have been updated', async () => {
    const missionPrefillData = {
      userId: userIdFixture,
    };

    await repository.upsertMissionPrefill(missionPrefillData.userId);
    const updatedMissionPrefillResult = await repository.upsertMissionPrefill(missionPrefillData.userId, prefillUpdateDataFixture);

    expect(updatedMissionPrefillResult.isOk()).toBe(true);

    const updatedMissionPrefill = updatedMissionPrefillResult._unsafeUnwrap();

    expect(updatedMissionPrefill).toBeDefined();
    expect(updatedMissionPrefill.missionName).toBe(prefillUpdateDataFixture.missionName);
    expect(updatedMissionPrefill.missionDescription).toBe(prefillUpdateDataFixture.missionDescription);
    expect(updatedMissionPrefill.timezone).toBe(prefillUpdateDataFixture.timezone);
    expect(updatedMissionPrefill.companyDescription).toBe(prefillUpdateDataFixture.companyDescription);
    expect(updatedMissionPrefill.plannedStart).toBe(prefillUpdateDataFixture.plannedStart);
  });
});
