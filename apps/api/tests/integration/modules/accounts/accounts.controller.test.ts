import { UserStatus } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { CommonUsersService } from '@common/users/users.service';
import { ConfigService } from '@config/config.service';
import { ClientAppV1Client } from '@lib/clients/client-app-v1.client';
import { OpenAIClient } from '@lib/clients/open-ai.client';
import { DbService } from '@lib/global/db.service';
import { SentryService } from '@lib/global/sentry.service';
import { AccountsController } from '@modules/accounts/accounts.controller';
import { AccountsRepository } from '@modules/accounts/accounts.repository';
import { AccountsService } from '@modules/accounts/accounts.service';
import { CoreInMemoryMongoServer } from '@tests/in-memory-server';

import { AuthenticatedRequest, JwtGuard } from '@lib/guards/jwt.guard';

const TEST_ACCOUNT_ID = '5550e5c7f8a44a0d3b54afa7';

const DEFAULT_TEAM_ADVISOR_ID = '1150e5c7f8a44a0d3b54afa7';

const AUTH_REQUEST = { user: { id: 'test-user-id' } } as AuthenticatedRequest;

describe('AccountsController', () => {
  let mongoServer: CoreInMemoryMongoServer;
  let dbService: DbService;
  let controller: AccountsController;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    mongoServer = new CoreInMemoryMongoServer();
    await mongoServer.onInit();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccountsController],
      providers: [
        AccountsService,
        AccountsRepository,
        CommonAccountsRepository,
        CommonUsersRepository,
        CommonUsersService,
        OpenAIClient,
        ClientAppV1Client,
        CommonMissionsRepository,
        {
          provide: CommonRBACService,
          useValue: {
            checkClientPermission: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: () => DEFAULT_TEAM_ADVISOR_ID,
          },
        },
        {
          provide: DbService,
          useValue: mongoServer.getDbService(),
        },
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get(AccountsController);
    dbService = module.get(DbService);
  });

  afterAll(async () => {
    await mongoServer.onDestroy();
  });

  afterEach(async () => {
    await dbService.account.deleteMany({});
    await dbService.user.deleteMany({});
  });

  describe('getAccountTeamAdvisor', () => {
    const DEFAULT_TEAM_ADVISOR = {
      id: DEFAULT_TEAM_ADVISOR_ID,
      email: '<EMAIL>',
      firstName: 'Jim',
      status: UserStatus.Active,
      type: 'companyUser',
      scrubbed: 'Verified',
      tokenVersion: 1,
    };

    it('should return default team advisor if there is no account advisor', async () => {
      await dbService.user.create({ data: DEFAULT_TEAM_ADVISOR });

      const { status, body } = await controller.getAccountTeamAdvisor(AUTH_REQUEST)({
        params: { accountId: TEST_ACCOUNT_ID },
      });

      expect(status).toBe(200);
      expect(body.id).toBe(DEFAULT_TEAM_ADVISOR_ID);
    });

    it('should return default team advisor if there are no account members', async () => {
      await dbService.user.create({ data: DEFAULT_TEAM_ADVISOR });
      await dbService.account.create({
        data: { id: TEST_ACCOUNT_ID, members: [] },
      });

      const { status, body } = await controller.getAccountTeamAdvisor(AUTH_REQUEST)({
        params: { accountId: TEST_ACCOUNT_ID },
      });

      expect(status).toBe(200);
      expect(body.id).toBe(DEFAULT_TEAM_ADVISOR_ID);
    });

    it('should return team advisor data', async () => {
      const teamAdvisorUser = await dbService.user.create({
        data: { email: '<EMAIL>', firstName: 'TA', status: 'Active', type: 'companyUser', scrubbed: 'Verified', tokenVersion: 1 },
      });
      await dbService.account.create({
        data: {
          id: TEST_ACCOUNT_ID,
          members: [
            { user: DEFAULT_TEAM_ADVISOR_ID, accessLevel: 'clientAdmin' },
            { user: teamAdvisorUser.id, accessLevel: 'bdAdmin' },
          ],
        },
      });

      const { status, body } = await controller.getAccountTeamAdvisor(AUTH_REQUEST)({
        params: { accountId: TEST_ACCOUNT_ID },
      });

      expect(status).toBe(200);
      expect(body.id).toBe(teamAdvisorUser.id);
    });

    it('should return default team advisor if admin is not active user', async () => {
      await dbService.user.create({ data: DEFAULT_TEAM_ADVISOR });

      const teamAdvisorUser = await dbService.user.create({
        data: { email: '<EMAIL>', firstName: 'TA', status: 'Deleted', type: 'companyUser', scrubbed: 'Verified', tokenVersion: 1 },
      });

      await dbService.account.create({
        data: {
          id: TEST_ACCOUNT_ID,
          members: [
            { user: DEFAULT_TEAM_ADVISOR_ID, accessLevel: 'clientAdmin' },
            { user: teamAdvisorUser.id, accessLevel: 'bdAdmin' },
          ],
        },
      });

      const { status, body } = await controller.getAccountTeamAdvisor(AUTH_REQUEST)({
        params: { accountId: TEST_ACCOUNT_ID },
      });

      expect(status).toBe(200);
      expect(body.id).toBe(DEFAULT_TEAM_ADVISOR_ID);
    });
  });
});
