import { MongoMemoryReplSet } from 'mongodb-memory-server';

import { CoreDbService } from '@lib/global/db.service';

class CoreDbTestingService extends CoreDbService {
  constructor(connectionUrl: string) {
    super(connectionUrl);
  }
}

export class CoreInMemoryMongoServer {
  private coreDbService: CoreDbTestingService | undefined;
  private mongoServer: MongoMemoryReplSet | undefined;

  getDbService = () => this.coreDbService;

  onInit = async () => {
    const dbName = `int-test-${new Date().getTime()}`;

    this.mongoServer = await MongoMemoryReplSet.create({ replSet: { count: 1, dbName } });

    const uri = this.mongoServer.getUri(dbName);
    this.coreDbService = new CoreDbTestingService(uri);

    await this.coreDbService.onModuleInit();
  };

  onDestroy = async () => {
    if (this.coreDbService) {
      await this.coreDbService.onModuleDestroy();
    }

    if (this.mongoServer) {
      await this.mongoServer.stop();
    }
  };
}
