import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@config/config.service';
import { UploadcareClient } from '@lib/clients/uploadcare.client';

describe('UploadcareClient', () => {
  let uploadcareClient: UploadcareClient;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UploadcareClient,
        {
          provide: ConfigService,
          useValue: {
            get: () => 'test',
          },
        },
      ],
    }).compile();

    uploadcareClient = module.get<UploadcareClient>(UploadcareClient);
  });

  describe('extractUuid', () => {
    it('should return empty string for empty URLs', () => {
      expect((uploadcareClient as any).extractUuid('')).toBe('');
    });

    it('should correctly extract UUID from Uploadcare CDN URL', () => {
      const uuid = '12345678-1234-1234-1234-123456789abc';
      const url = `https://ucarecdn.com/${uuid}/`;

      expect((uploadcareClient as any).extractUuid(url)).toBe(uuid);
    });

    it('should correctly extract UUID from alternative Uploadcare domain', () => {
      const uuid = 'abcdef12-3456-7890-abcd-ef1234567890';
      const url = `https://cdn.uploadcare.com/${uuid}/image.jpg`;

      expect((uploadcareClient as any).extractUuid(url)).toBe(uuid);
    });

    it('should extract UUID from URL with query parameters', () => {
      const uuid = '00112233-**************-aabbccddeeff';
      const url = `https://ucarecdn.com/${uuid}/-/resize/800x600/image.png?quality=80`;

      expect((uploadcareClient as any).extractUuid(url)).toBe(uuid);
    });

    it('should return empty string for URLs without valid UUID', () => {
      expect((uploadcareClient as any).extractUuid('https://example.com/image.jpg')).toBe('');
      expect((uploadcareClient as any).extractUuid('https://ucarecdn.com/not-a-uuid/')).toBe('');
      expect((uploadcareClient as any).extractUuid('random text with no URL')).toBe('');
    });

    it('should handle malformed UUIDs', () => {
      // Missing a character
      expect((uploadcareClient as any).extractUuid('https://ucarecdn.com/12345678-1234-1234-1234-12345678abc/')).toBe('');
      // Extra characters
      expect((uploadcareClient as any).extractUuid('https://ucarecdn.com/12345678-1234-1234-1234-123456789abcd/')).toBe('');
      // Invalid characters
      expect((uploadcareClient as any).extractUuid('https://ucarecdn.com/12345678-1234-1234-1234-123456789xyz/')).toBe('');
    });
  });
});
