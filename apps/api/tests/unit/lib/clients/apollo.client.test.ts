import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@config/config.service';
import { ApolloClient } from '@lib/clients/apollo.client';

import { apolloOrganizationBySearchFixture, apolloOrganizationEnrichedFixture } from '@fixtures/lib/clients/apollo.fixtures';

describe('ApolloClient', () => {
  let apolloClient: ApolloClient;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApolloClient,
        {
          provide: ConfigService,
          useValue: {
            get: () => 'test',
          },
        },
      ],
    }).compile();

    apolloClient = module.get(ApolloClient);
  });

  describe('getOrganizationByName', () => {
    it('should return the required organization because the data is present', async () => {
      jest.spyOn((apolloClient as any).client, 'get').mockResolvedValue({ data: { organizations: [apolloOrganizationBySearchFixture] } });

      const result = await apolloClient.getOrganizationByName('test');

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toBe(apolloOrganizationBySearchFixture);
      }
    });

    it('should return the required error because no organization was found', async () => {
      jest.spyOn((apolloClient as any).client, 'get').mockResolvedValue({ data: { organizations: [] } });

      const result = await apolloClient.getOrganizationByName('test');

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code === 'GET_ORGANIZATION_BY_NAME_NOT_FOUND').toBe(true);
      }
    });

    it('should return the required error because the api call failed', async () => {
      const error = 'Api failed';

      jest.spyOn((apolloClient as any).client, 'get').mockRejectedValue(error);

      const result = await apolloClient.getOrganizationByName('test');

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code === 'GET_ORGANIZATION_BY_NAME_API_ERROR').toBe(true);
        expect(result.error.isUnexpectedError).toBe(true);

        if (result.error.isUnexpectedError) {
          expect(result.error.originalError).toStrictEqual(new Error(error));
        }
      }
    });
  });

  describe('getOrganizationByUrl', () => {
    it('should return the required organization because the data is present', async () => {
      jest.spyOn((apolloClient as any).client, 'get').mockResolvedValue({ data: { organization: apolloOrganizationEnrichedFixture } });

      const result = await apolloClient.getOrganizationByUrl('test');

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toBe(apolloOrganizationEnrichedFixture);
      }
    });

    it('should return the required error because no organization was found', async () => {
      jest.spyOn((apolloClient as any).client, 'get').mockResolvedValue({ data: { organization: undefined } });

      const result = await apolloClient.getOrganizationByUrl('test');

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code === 'GET_ORGANIZATION_BY_URL_NOT_FOUND').toBe(true);
      }
    });

    it('should return the required error because the api call failed', async () => {
      const error = 'Api failed';

      jest.spyOn((apolloClient as any).client, 'get').mockRejectedValue(error);

      const result = await apolloClient.getOrganizationByUrl('test');

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code === 'GET_ORGANIZATION_BY_URL_API_ERROR').toBe(true);
        expect(result.error.isUnexpectedError).toBe(true);

        if (result.error.isUnexpectedError) {
          expect(result.error.originalError).toStrictEqual(new Error(error));
        }
      }
    });
  });
});
