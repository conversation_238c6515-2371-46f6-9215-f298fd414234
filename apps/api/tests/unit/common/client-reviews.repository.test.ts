import { UserReviewStatus } from '@a_team/prisma';
import { Test, TestingModule } from '@nestjs/testing';

import { CommonClientReviewsRepository } from '@common/client-reviews/client-reviews.repository';
import { roleIdFixture } from '@fixtures/modules/missions/common.fixtures';
import { DbService } from '@lib/global/db.service';

describe('CommonClientReviewsRepository', () => {
  let repository: CommonClientReviewsRepository;
  let mockDbService: any;

  beforeAll(async () => {
    mockDbService = {
      userReview: {
        findMany: jest.fn(),
        count: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [CommonClientReviewsRepository, { provide: DbService, useValue: mockDbService }],
    }).compile();

    repository = module.get(CommonClientReviewsRepository);
  });

  describe('getClientReviewsByUserId', () => {
    it('should successfully fetch client reviews', async () => {
      const userId = 'user-1';
      const review = {
        id: 'review-1',
        publicFeedback: 'Great work!',
        rid: roleIdFixture,
        starRating: 5,
        status: UserReviewStatus.completed,
      };
      mockDbService.userReview.findMany.mockResolvedValue([review]);
      mockDbService.userReview.count.mockResolvedValue(1);

      const result = await repository.getClientReviewsByUserId(userId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const reviews = result.value;
        expect(reviews).toHaveLength(1);
        expect(reviews[0]).toMatchObject({
          id: review.id,
          publicFeedback: 'Great work!',
          rid: roleIdFixture,
          starRating: 5,
          status: UserReviewStatus.completed,
        });
      }

      const countResult = await repository.getClientReviewsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(1);
    });

    it('should return empty array when user has no reviews', async () => {
      const userId = 'user-2';
      mockDbService.userReview.findMany.mockResolvedValue([]);
      mockDbService.userReview.count.mockResolvedValue(0);

      const result = await repository.getClientReviewsByUserId(userId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }

      const countResult = await repository.getClientReviewsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(0);
    });
  });

  describe('getClientReviewsCount', () => {
    it('should return correct count for user with reviews', async () => {
      const userId = 'user-with-reviews';
      const expectedCount = 5;
      mockDbService.userReview.count.mockResolvedValue(expectedCount);

      const countResult = await repository.getClientReviewsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(expectedCount);
      expect(mockDbService.userReview.count).toHaveBeenCalledWith({ where: { toUser: userId, status: UserReviewStatus.completed } });
    });

    it('should return 0 for user with no reviews', async () => {
      const userId = 'user-without-reviews';
      const expectedCount = 0;
      mockDbService.userReview.count.mockResolvedValue(expectedCount);

      const countResult = await repository.getClientReviewsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(expectedCount);
      expect(mockDbService.userReview.count).toHaveBeenCalledWith({ where: { toUser: userId, status: UserReviewStatus.completed } });
    });
  });
});
