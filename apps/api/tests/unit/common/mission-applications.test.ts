import { MissionApplication } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { CommonMissionApplicationsRepository } from '@common/missions/mission-applications.repository';
import { CommonMissionApplicationsService } from '@common/missions/mission-applications.service';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { OpenAIClient } from '@lib/clients/open-ai.client';

describe('MissionApplicationsService', () => {
  let commonMissionsRepository: CommonMissionsRepository;
  let missionApplicationsService: CommonMissionApplicationsService;
  let commonUsersRepository: CommonUsersRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CommonMissionApplicationsService,
        {
          provide: CommonMissionApplicationsRepository,
          useValue: {
            findApplicationById: jest.fn(),
          },
        },
        {
          provide: CommonMissionsRepository,
          useValue: {
            findRoleById: jest.fn(),
          },
        },
        {
          provide: CommonUsersRepository,
          useValue: {
            findUserByIdNeverthrow: jest.fn(),
          },
        },
        {
          provide: OpenAIClient,
          useValue: {
            generateObject: jest.fn(),
            generateArray: jest.fn(),
          },
        },
      ],
    }).compile();

    commonMissionsRepository = module.get(CommonMissionsRepository);
    missionApplicationsService = module.get(CommonMissionApplicationsService);
    commonUsersRepository = module.get(CommonUsersRepository);

    jest.spyOn(Logger, 'error').mockImplementation(jest.fn());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getApplicationStatusStage', () => {
    it('should return the accepted status when the application user is assigned to the role', async () => {
      jest.spyOn(commonMissionsRepository, 'findRoleById').mockResolvedValue(ok({ user: 'uid' } as any));
      const missionApplication = { aid: 'aid', rid: 'rid', uid: 'uid' } as MissionApplication;
      const result = await missionApplicationsService.getApplicationStatusStage(missionApplication, []);

      expect(result.isOk() && result.value).toEqual('Accepted');
    });

    it('should return the not available status when the application user is not assigned to the role', async () => {
      jest.spyOn(commonMissionsRepository, 'findRoleById').mockResolvedValue(ok({ user: 'other-uid' } as any));
      const missionApplication = { aid: 'aid', rid: 'rid', uid: 'uid', reviewStatus: { other: ['Unavailable'] } } as MissionApplication;
      const result = await missionApplicationsService.getApplicationStatusStage(missionApplication, []);

      expect(result.isOk() && result.value).toEqual('NotAvailable');
    });

    it('should return the exclusive status when the application user is the exclusive applicant', async () => {
      jest.spyOn(commonUsersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok({ exclusiveApplication: { aid: 'aid' } } as any));
      const missionApplication = { aid: 'aid', rid: 'rid', uid: 'uid' } as MissionApplication;
      const result = await missionApplicationsService.getApplicationStatusStage(missionApplication, []);

      expect(result.isOk() && result.value).toEqual('Exclusive');
    });

    it('should return the on hold status when the application user is not the exclusive applicant', async () => {
      jest.spyOn(commonUsersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok({ exclusiveApplication: { aid: 'other-aid' } } as any));
      const missionApplication = { aid: 'aid', rid: 'rid', uid: 'uid' } as MissionApplication;
      const result = await missionApplicationsService.getApplicationStatusStage(missionApplication, []);

      expect(result.isOk() && result.value).toEqual('OnHold');
    });
  });
});
