import { Test, TestingModule } from '@nestjs/testing';

import { CommonLinkedinRecommendationsRepository } from '@common/linkedin-recommendations/linkedin-recommendations.repository';
import { DbService } from '@lib/global/db.service';

describe('CommonLinkedinRecommendationsRepository', () => {
  let repository: CommonLinkedinRecommendationsRepository;
  let mockDbService: any;

  beforeAll(async () => {
    mockDbService = {
      linkedInRecommendation: {
        findMany: jest.fn(),
        count: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [CommonLinkedinRecommendationsRepository, { provide: DbService, useValue: mockDbService }],
    }).compile();

    repository = module.get(CommonLinkedinRecommendationsRepository);
  });

  describe('getLinkedinRecommendationsByUserId', () => {
    it('should successfully fetch linkedin recommendations', async () => {
      const userId = 'user-1';
      const recommendation = {
        id: 'rec-1',
        recommendationText: 'Highly recommended.',
        recommenderName: 'Jane Doe',
        recommenderTitle: 'CEO at Example Inc.',
      };

      mockDbService.linkedInRecommendation.findMany.mockResolvedValue([recommendation]);
      mockDbService.linkedInRecommendation.count.mockResolvedValue(1);

      const result = await repository.getLinkedinRecommendationsByUserId(userId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const recommendations = result.value;

        expect(recommendations).toHaveLength(1);
        expect(recommendations[0]).toMatchObject({
          id: recommendation.id,
          recommendationText: 'Highly recommended.',
          recommenderName: 'Jane Doe',
          recommenderTitle: 'CEO at Example Inc.',
        });
      }

      const countResult = await repository.getLinkedinRecommendationsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(1);
    });

    it('should return empty array when user has no linkedin recommendations', async () => {
      const userId = 'user-2';
      mockDbService.linkedInRecommendation.findMany.mockResolvedValue([]);
      mockDbService.linkedInRecommendation.count.mockResolvedValue(0);

      const result = await repository.getLinkedinRecommendationsByUserId(userId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual([]);
      }

      const countResult = await repository.getLinkedinRecommendationsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(0);
    });
  });

  describe('getLinkedinRecommendationsCount', () => {
    it('should return correct count for user with recommendations', async () => {
      const userId = 'user-with-recs';
      const expectedCount = 5;
      mockDbService.linkedInRecommendation.count.mockResolvedValue(expectedCount);

      const countResult = await repository.getLinkedinRecommendationsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(expectedCount);
      expect(mockDbService.linkedInRecommendation.count).toHaveBeenCalledWith({ where: { userId } });
    });

    it('should return 0 for user with no recommendations', async () => {
      const userId = 'user-without-recs';
      const expectedCount = 0;
      mockDbService.linkedInRecommendation.count.mockResolvedValue(expectedCount);

      const countResult = await repository.getLinkedinRecommendationsCount(userId);

      expect(countResult.isOk()).toBe(true);
      expect(countResult._unsafeUnwrap()).toBe(expectedCount);
      expect(mockDbService.linkedInRecommendation.count).toHaveBeenCalledWith({ where: { userId } });
    });
  });
});
