import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';

import { ConfigService } from '@config/config.service';
import { LangfuseClient } from '@lib/clients/langfuse.client';
import { SentryService } from '@lib/global/sentry.service';
import { ProposalsPromptsService } from '@modules/proposals/proposals-prompts.service';

describe('ProposalsPromptsService', () => {
  let promptService: ProposalsPromptsService;
  let langfuseClient: LangfuseClient;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProposalsPromptsService,
        LangfuseClient,
        {
          provide: SentryService,
          useValue: {
            logAndCaptureError: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: () => 'test',
          },
        },
      ],
    }).compile();

    promptService = module.get(ProposalsPromptsService);
    langfuseClient = module.get(LangfuseClient);
  });

  describe('getPrompt', () => {
    it('should return the prompt from langfuse', async () => {
      jest.spyOn((langfuseClient as any).client, 'get').mockResolvedValue({ data: { prompt: '{{builderFirstName}} prompt' } });

      const prompt = await promptService.getPrompt({
        promptName: 'requirements-section',
        variables: {
          builderProfile: 'test',
          builderFirstName: 'langfuse',
          roleName: 'test',
          roleDescription: 'test',
        },
      });

      expect(prompt).toEqual('langfuse prompt');
    });

    it('should return the prompt from the prompt function if langfuse is down', async () => {
      jest.spyOn((langfuseClient as any).client, 'get').mockRejectedValue(new Error('langfuse is down'));

      const prompt = await promptService.getPrompt({
        promptName: 'requirements-section',
        variables: {
          builderProfile: 'test',
          builderFirstName: 'test',
          roleName: 'test',
          roleDescription: 'test',
        },
      });

      expect(prompt).toEqual(expect.stringContaining('candidate'));
    });
  });
});
