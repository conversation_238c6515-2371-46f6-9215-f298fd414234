import { Account, User } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { CommonUsersService } from '@common/users/users.service';
import { ConfigService } from '@config/config.service';
import { userCreationDataFixture } from '@fixtures/modules/missions/missions-prefill.service.fixtures';
import { ClientAppV1Client } from '@lib/clients/client-app-v1.client';
import { OpenAIClient } from '@lib/clients/open-ai.client';
import { DbService } from '@lib/global/db.service';
import { SentryService } from '@lib/global/sentry.service';
import { AccountsController } from '@modules/accounts/accounts.controller';
import { AccountsRepository } from '@modules/accounts/accounts.repository';
import { AccountsService } from '@modules/accounts/accounts.service';

import { Errors } from '@lib/errors';
import { JwtGuard } from '@lib/guards/jwt.guard';

const TEST_ACCOUNT_ID = '5550e5c7f8a44a0d3b54afa7';

describe('AccountsService', () => {
  let service: AccountsService;
  let accountsRepository: CommonAccountsRepository;
  let usersRepository: CommonUsersRepository;
  let missionsRepository: CommonMissionsRepository;
  let clientAppV1Client: ClientAppV1Client;

  beforeAll(async () => {
    jest.spyOn(Logger, 'error').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AccountsController],
      providers: [
        AccountsService,
        AccountsRepository,
        CommonAccountsRepository,
        CommonUsersRepository,
        CommonUsersService,
        OpenAIClient,
        ClientAppV1Client,
        CommonMissionsRepository,
        {
          provide: CommonRBACService,
          useValue: {
            checkClientPermission: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: () => 'test',
          },
        },
        {
          provide: DbService,
          useValue: {},
        },
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    service = module.get(AccountsService);
    accountsRepository = module.get(CommonAccountsRepository);
    usersRepository = module.get(CommonUsersRepository);
    missionsRepository = module.get(CommonMissionsRepository);
    clientAppV1Client = module.get(ClientAppV1Client);
  });

  describe('getAccountCollaborators', () => {
    const user = userCreationDataFixture as unknown as User;

    it('should return collaborators', async () => {
      jest.spyOn(usersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok(user));
      jest.spyOn(accountsRepository, 'findAccountById').mockResolvedValue(
        ok({
          id: TEST_ACCOUNT_ID,
          members: [{ user: 'test', accessLevel: 'clientAdmin' }],
        } as unknown as Account)
      );

      const result = await service.getAccountCollaborators(TEST_ACCOUNT_ID);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const collaborators = result.value;

        expect(collaborators.length).toBe(1);
        expect(collaborators[0].email).toEqual(userCreationDataFixture.email);
        expect(collaborators[0].firstName).toEqual(userCreationDataFixture.firstName);
        expect(collaborators[0].lastName).toEqual(userCreationDataFixture.lastName);
        expect(collaborators[0].pictureURL).toEqual(userCreationDataFixture.pictureURL);
        expect(collaborators[0].accessLevel).toEqual('clientAdmin');
      }
    });

    it('should return empty array if there are no collaborators', async () => {
      jest.spyOn(usersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok(user));
      jest.spyOn(accountsRepository, 'findAccountById').mockResolvedValue(
        ok({
          id: TEST_ACCOUNT_ID,
          members: [],
        } as unknown as Account)
      );

      const result = await service.getAccountCollaborators(TEST_ACCOUNT_ID);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value.length).toBe(0);
      }
    });
  });

  describe('inviteMissionCollaborator', () => {
    it('should invite a mission collaborator', async () => {
      jest.spyOn(missionsRepository, 'findMissionById').mockResolvedValue(ok({ missionSpecId: 'test-mission-spec-id' } as any));
      const inviteMissionCollaboratorSpy = jest
        .spyOn(clientAppV1Client, 'inviteMissionCollaborator')
        .mockResolvedValue(ok({ message: 'Collaborator invited' }));

      const result = await service.inviteMissionCollaborator(
        'mission-id',
        {
          email: '<EMAIL>',
          role: 'missionadmin',
          fullName: 'Test User',
          skipEmail: false,
        },
        'test'
      );

      expect(result.isOk()).toBe(true);
      expect(inviteMissionCollaboratorSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          missionSpecId: 'test-mission-spec-id',
        }),
        'test'
      );
    });

    it('should return an error if the mission is not found', async () => {
      jest.spyOn(missionsRepository, 'findMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await service.inviteMissionCollaborator(
        'mission-id',
        { email: '<EMAIL>', role: 'missionadmin', fullName: 'Test User', skipEmail: false },
        'test'
      );

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('FIND_MISSION_BY_ID_NOT_FOUND');
    });

    it('should return an error if the mission does not have a mission spec id', async () => {
      jest.spyOn(missionsRepository, 'findMissionById').mockResolvedValue(ok({} as any));

      const result = await service.inviteMissionCollaborator(
        'mission-id',
        { email: '<EMAIL>', role: 'missionadmin', fullName: 'Test User', skipEmail: false },
        'test'
      );

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('INVITE_MISSION_COLLABORATOR_NO_MISSION_SPEC_ID');
    });

    it('should return an error if the client app v1 client throws an error', async () => {
      jest.spyOn(missionsRepository, 'findMissionById').mockResolvedValue(ok({ missionSpecId: 'test-mission-spec-id' } as any));
      jest.spyOn(clientAppV1Client, 'inviteMissionCollaborator').mockResolvedValue(Errors.createError('INVITE_MISSION_COLLABORATOR_API_ERROR'));

      const result = await service.inviteMissionCollaborator(
        'mission-id',
        { email: '<EMAIL>', role: 'missionadmin', fullName: 'Test User', skipEmail: false },
        'test'
      );

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('INVITE_MISSION_COLLABORATOR_API_ERROR');
    });
  });
});
