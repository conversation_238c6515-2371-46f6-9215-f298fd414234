import { CalendarProvider } from '@a_team/prisma';
import { Test, TestingModule } from '@nestjs/testing';
import { ok, err } from 'neverthrow';

import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import {
  calendarFixture,
  nullCredentialsCalendarFixture,
  idTokenOnlyCalendarFixture,
  accessTokenOnlyCalendarFixture,
  updatedCalendarFixture,
  calendarCreationErrorResponse,
  calendarUpdateErrorResponse,
} from '@fixtures/modules/interview-scheduling';
import { CalendarsRepository } from '@modules/interview-scheduling/calendars.repository';
import { CalendarsService } from '@modules/interview-scheduling/calendars.service';
import { VideoCallService } from '@modules/interview-scheduling/video-call.service';

import { mockCalendarsRepository, mockCommonUsersRepository, mockConfigService, mockVideoCallService } from './mocks';

const validCode = 'valid-auth-code';
const userId = '62b31586f2704800118b36c6';
const getState = (url: string) => url.split('state=')[1];

export const invalidStateResponse = {
  valid: false,
  error: 'Invalid state',
};

// Mock the google auth and calendar APIs
jest.mock('googleapis', () => {
  const mockOAuth2Client = {
    generateAuthUrl: jest.fn().mockImplementation(({ state }: { state: string }) => {
      return `https://mock-google-auth-url.com?state=${state}`;
    }),
    getToken: jest.fn().mockResolvedValue({
      tokens: {
        access_token: 'new-access-token',
        refresh_token: 'new-refresh-token',
        id_token: 'new-id-token',
        expiry_date: 1745590819298,
      },
    }),
    setCredentials: jest.fn(),
    refreshAccessToken: jest.fn().mockResolvedValue({
      credentials: {
        access_token: 'refreshed-access-token',
        expiry_date: 1745590819298,
      },
    }),
  };

  return {
    google: {
      auth: {
        OAuth2: jest.fn().mockReturnValue(mockOAuth2Client),
      },
      calendar: jest.fn().mockReturnValue({
        freebusy: {
          query: jest.fn().mockResolvedValue({
            data: {
              calendars: {
                primary: {
                  busy: [
                    { start: '2025-05-01T10:00:00Z', end: '2025-05-01T11:00:00Z' },
                    { start: '2025-05-01T14:00:00Z', end: '2025-05-01T15:30:00Z' },
                  ],
                },
              },
            },
          }),
        },
        calendars: {
          get: jest.fn().mockResolvedValue({
            data: {
              timeZone: 'Europe/London',
            },
          }),
        },
        events: {
          insert: jest.fn().mockResolvedValue({
            data: {
              id: 'test-event-id',
              status: 'confirmed',
            },
          }),
          update: jest.fn().mockResolvedValue({
            data: {
              id: 'test-event-id',
              status: 'confirmed',
            },
          }),
        },
      }),
    },
  };
});

describe('CalendarsService', () => {
  let service: CalendarsService;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalendarsService,
        {
          provide: CalendarsRepository,
          useValue: mockCalendarsRepository,
        },
        {
          provide: CommonUsersRepository,
          useValue: mockCommonUsersRepository,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: VideoCallService,
          useValue: mockVideoCallService,
        },
      ],
    }).compile();

    service = module.get<CalendarsService>(CalendarsService);
  });

  describe('initiateGoogleAuth', () => {
    it('should generate a Google auth URL with correct parameters', async () => {
      const url = service.initiateGoogleAuth(userId);
      const state = getState(url);

      expect(url).toEqual(`https://mock-google-auth-url.com?state=${state}`);
    });
  });

  describe('handleGoogleCallback', () => {
    it('should handle invalid state', async () => {
      const result = await service.handleGoogleCallback(validCode, 'invalid-state');

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('HANDLE_GOOGLE_CALLBACK_INVALID_STATE');
    });

    it('should update existing calendar when found', async () => {
      mockCalendarsRepository.findCalendarByUserIdAndProvider.mockResolvedValue(ok(calendarFixture));
      mockCalendarsRepository.updateCalendarByUserId.mockResolvedValue(ok(updatedCalendarFixture));
      const state = getState(service.initiateGoogleAuth(userId));

      const result = await service.handleGoogleCallback(validCode, state);

      expect(result.isOk()).toBe(true);
      expect(mockCalendarsRepository.findCalendarByUserIdAndProvider).toHaveBeenCalledWith(userId, CalendarProvider.google);
      expect(mockCalendarsRepository.updateCalendarByUserId).toHaveBeenCalled();
      expect(mockCalendarsRepository.createCalendar).not.toHaveBeenCalled();
    });

    it('should create new calendar when not found', async () => {
      mockCalendarsRepository.findCalendarByUserIdAndProvider.mockResolvedValue(err({ code: 'FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_NOT_FOUND' }));
      mockCalendarsRepository.createCalendar.mockResolvedValue(ok(updatedCalendarFixture));
      const state = getState(service.initiateGoogleAuth(userId));

      const result = await service.handleGoogleCallback(validCode, state);

      expect(result.isOk()).toBe(true);
      expect(mockCalendarsRepository.createCalendar).toHaveBeenCalledWith({
        userId,
        provider: CalendarProvider.google,
        calendarId: 'primary',
        credentials: expect.any(Object),
      });
    });

    it('should handle calendar creation error', async () => {
      mockCalendarsRepository.findCalendarByUserIdAndProvider.mockResolvedValue(err({ code: 'FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_NOT_FOUND' }));
      mockCalendarsRepository.createCalendar.mockResolvedValue(err(calendarCreationErrorResponse));
      const state = getState(service.initiateGoogleAuth(userId));

      const result = await service.handleGoogleCallback(validCode, state);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CREATE_CALENDAR_DB_ERROR');
    });
  });

  describe('getStatus', () => {
    it('should return not connected status when calendar not found', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(err({ code: 'FIND_CALENDAR_BY_USER_ID_NOT_FOUND' }));

      const result = await service.getStatus(userId);

      expect(result.isOk()).toBe(false);
      expect(result._unsafeUnwrapErr().code).toBe('FIND_CALENDAR_BY_USER_ID_NOT_FOUND');
    });

    it('should return connected status with provider when valid credentials exist', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(calendarFixture));

      const result = await service.getStatus(userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        isConnected: true,
        provider: CalendarProvider.google,
      });
    });

    it('should return not connected status when credentials are invalid', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(nullCredentialsCalendarFixture));

      const result = await service.getStatus(userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        isConnected: false,
        provider: null,
      });
    });

    it('should return connected status when only id_token is present', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(idTokenOnlyCalendarFixture));

      const result = await service.getStatus(userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        isConnected: true,
        provider: CalendarProvider.google,
      });
    });

    it('should return connected status when only access_token is present', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(accessTokenOnlyCalendarFixture));

      const result = await service.getStatus(userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        isConnected: true,
        provider: CalendarProvider.google,
      });
    });
  });

  describe('disconnect', () => {
    it('should return error when calendar not found', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(err({ code: 'FIND_CALENDAR_BY_USER_ID_NOT_FOUND' }));

      const result = await service.disconnect(userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('FIND_CALENDAR_BY_USER_ID_NOT_FOUND');
    });

    it('should clear credentials and return disconnected status', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(calendarFixture));
      mockCalendarsRepository.updateCalendarById.mockResolvedValue(ok(nullCredentialsCalendarFixture));

      const result = await service.disconnect(userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        isConnected: false,
        provider: null,
      });
      expect(mockCalendarsRepository.updateCalendarById).toHaveBeenCalledWith(calendarFixture.id, {
        credentials: {
          access_token: null,
          refresh_token: null,
          id_token: null,
          expiry_date: null,
        },
      });
    });

    it('should handle update error', async () => {
      mockCalendarsRepository.findCalendarByUserId.mockResolvedValue(ok(calendarFixture));
      mockCalendarsRepository.updateCalendarById.mockResolvedValue(err(calendarUpdateErrorResponse));

      const result = await service.disconnect(userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('UPDATE_CALENDAR_BY_ID_DB_ERROR');
    });
  });

  describe('getBusySlots', () => {
    const mockCalendarApi = {
      freebusy: {
        query: jest.fn().mockResolvedValue({
          data: {
            calendars: {
              primary: {
                busy: [
                  { start: '2025-05-01T10:00:00Z', end: '2025-05-01T11:00:00Z' },
                  { start: '2025-05-01T14:00:00Z', end: '2025-05-01T15:30:00Z' },
                ],
              },
            },
          },
        }),
      },
      calendars: {
        get: jest.fn().mockResolvedValue({
          data: {
            timeZone: 'Europe/London',
          },
        }),
      },
    };

    // Mock user with timezone preference
    const mockUser = {
      id: userId,
      timezone: {
        name: 'America/New_York',
      },
    };

    // Mock user without timezone preference
    const mockUserNoTimezone = {
      id: userId,
      timezone: null,
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (service as any).getAuthenticatedCalendarClient = jest.fn();
      mockCommonUsersRepository.findUserByIdNeverthrow.mockReset();
    });

    it('should return error when calendar client cannot be authenticated', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(err({ code: 'GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED' }));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED');
    });

    it('should return error when user cannot be found', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok(mockCalendarApi));
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(err({ code: 'FIND_USER_BY_ID_NOT_FOUND' }));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('FIND_USER_BY_ID_NOT_FOUND');
    });

    it('should return busy slots and calendar timezone when successful', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApi,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'Europe/London',
        slots: [
          { utcStart: '2025-05-01T10:00:00Z', utcEnd: '2025-05-01T11:00:00Z' },
          { utcStart: '2025-05-01T14:00:00Z', utcEnd: '2025-05-01T15:30:00Z' },
        ],
      });

      expect(mockCalendarApi.freebusy.query).toHaveBeenCalledWith({
        requestBody: {
          timeMin: '2025-05-01T00:00:00Z',
          timeMax: '2025-05-01T23:59:59Z',
          items: [{ id: 'primary' }],
          timeZone: 'UTC',
        },
      });
      expect(mockCalendarApi.calendars.get).toHaveBeenCalledWith({ calendarId: 'primary' });
    });

    it('should fallback to user timezone when no calendar timezone is provided', async () => {
      const mockCalendarApiNoTimezone = {
        ...mockCalendarApi,
        calendars: {
          get: jest.fn().mockResolvedValue({
            data: {},
          }),
        },
      };

      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApiNoTimezone,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'America/New_York',
        slots: [
          { utcStart: '2025-05-01T10:00:00Z', utcEnd: '2025-05-01T11:00:00Z' },
          { utcStart: '2025-05-01T14:00:00Z', utcEnd: '2025-05-01T15:30:00Z' },
        ],
      });
    });

    it('should default to UTC when no calendar timezone and no user timezone', async () => {
      const mockCalendarApiNoTimezone = {
        ...mockCalendarApi,
        calendars: {
          get: jest.fn().mockResolvedValue({
            data: {},
          }),
        },
      };

      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApiNoTimezone,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUserNoTimezone));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'UTC',
        slots: [
          { utcStart: '2025-05-01T10:00:00Z', utcEnd: '2025-05-01T11:00:00Z' },
          { utcStart: '2025-05-01T14:00:00Z', utcEnd: '2025-05-01T15:30:00Z' },
        ],
      });
    });

    it('should handle empty busy slots', async () => {
      const mockCalendarApiEmptyBusy = {
        ...mockCalendarApi,
        freebusy: {
          query: jest.fn().mockResolvedValue({
            data: {
              calendars: {
                primary: {
                  busy: [],
                },
              },
            },
          }),
        },
      };

      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApiEmptyBusy,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'Europe/London',
        slots: [],
      });
    });

    it('should handle missing busy data', async () => {
      const mockCalendarApiNoBusy = {
        ...mockCalendarApi,
        freebusy: {
          query: jest.fn().mockResolvedValue({
            data: {
              calendars: {
                primary: {},
              },
            },
          }),
        },
      };

      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApiNoBusy,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'Europe/London',
        slots: [],
      });
    });

    it('should skip busy slots with missing start or end times', async () => {
      const mockCalendarApiPartialSlots = {
        ...mockCalendarApi,
        freebusy: {
          query: jest.fn().mockResolvedValue({
            data: {
              calendars: {
                primary: {
                  busy: [
                    { start: '2025-05-01T10:00:00Z', end: '2025-05-01T11:00:00Z' },
                    { start: '2025-05-01T14:00:00Z' }, // Missing end
                    { end: '2025-05-01T16:00:00Z' }, // Missing start
                  ],
                },
              },
            },
          }),
        },
      };

      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApiPartialSlots,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        timezone: 'Europe/London',
        slots: [{ utcStart: '2025-05-01T10:00:00Z', utcEnd: '2025-05-01T11:00:00Z' }],
      });
    });

    it('should handle API errors', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(
        ok({
          client: mockCalendarApi,
          calendarId: 'primary',
        })
      );
      mockCommonUsersRepository.findUserByIdNeverthrow.mockResolvedValue(ok(mockUser));

      // Override the default mock implementation to simulate an error
      mockCalendarApi.freebusy.query.mockRejectedValueOnce(new Error('API error'));

      const result = await service.getBusySlots(userId, '2025-05-01T00:00:00Z', '2025-05-01T23:59:59Z');

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_BUSY_SLOTS_WITH_TIMEZONE_ERROR');
      expect(result._unsafeUnwrapErr().isUnexpectedError).toBe(true);
    });
  });

  describe('createEvent', () => {
    const mockCalendarApi = {
      events: {
        insert: jest.fn().mockResolvedValue({
          data: {
            id: 'test-event-id',
            status: 'confirmed',
          },
        }),
      },
    };

    const mockEventRequest = {
      summary: 'Test Interview',
      description: 'Interview with candidate',
      start: {
        dateTime: '2025-05-15T10:00:00Z',
        timezone: 'UTC',
      },
      end: {
        dateTime: '2025-05-15T11:00:00Z',
        timezone: 'UTC',
      },
      attendees: [
        {
          email: '<EMAIL>',
          displayName: 'Candidate',
          organizer: false,
        },
      ],
      organizer: {
        email: '<EMAIL>',
        displayName: 'Interviewer',
      },
    };

    const mockVideoCallUrl = 'https://example.daily.co/test-room-id';

    beforeEach(() => {
      jest.clearAllMocks();
      (service as any).getAuthenticatedCalendarClient = jest.fn();
      (service as any).videoCallService = {
        getVideoCallProviderUrl: jest.fn(),
      };
    });

    it('should return error when calendar client cannot be authenticated', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(err({ code: 'GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED' }));

      const result = await service.createEvent(userId, mockEventRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED');
    });

    it('should return error when video link cannot be generated', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      (service as any).videoCallService.getVideoCallProviderUrl.mockResolvedValue(err({ code: 'GET_VIDEO_CALL_URL_API_ERROR' }));

      const result = await service.createEvent(userId, mockEventRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_VIDEO_CALL_URL_API_ERROR');
    });

    it('should return error when calendar API call fails', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      (service as any).videoCallService.getVideoCallProviderUrl.mockResolvedValue(ok(mockVideoCallUrl));
      mockCalendarApi.events.insert.mockRejectedValueOnce(new Error('API error'));

      const result = await service.createEvent(userId, mockEventRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_CREATE_EVENT_ERROR');
    });

    it('should return error when event ID is missing in response', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      (service as any).videoCallService.getVideoCallProviderUrl.mockResolvedValue(ok(mockVideoCallUrl));
      mockCalendarApi.events.insert.mockResolvedValueOnce({
        data: { status: 'confirmed' }, // No ID in response
      });

      const result = await service.createEvent(userId, mockEventRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_CREATE_EVENT_ERROR');
    });

    it('should successfully create an event with video link', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      (service as any).videoCallService.getVideoCallProviderUrl.mockResolvedValue(ok(mockVideoCallUrl));

      const result = await service.createEvent(userId, mockEventRequest);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        calendarEventId: 'test-event-id',
        calendarId: 'primary',
        status: 'confirmed',
        callUrl: mockVideoCallUrl,
        callId: expect.any(String),
        callType: 'daily.co',
      });

      expect(mockCalendarApi.events.insert).toHaveBeenCalledWith({
        calendarId: 'primary',
        sendNotifications: false,
        sendUpdates: 'none',
        requestBody: {
          ...mockEventRequest,
          status: 'confirmed',
          conferenceData: {
            createRequest: {
              requestId: expect.any(String),
            },
          },
          location: mockVideoCallUrl,
          reminders: {
            useDefault: false,
          },
        },
      });

      expect((service as any).videoCallService.getVideoCallProviderUrl).toHaveBeenCalledWith(
        'daily.co',
        expect.any(String),
        expect.any(Number),
        expect.any(Number)
      );
    });
  });

  describe('rescheduleEvent', () => {
    const mockCalendarApi = {
      events: {
        update: jest.fn().mockResolvedValue({
          data: {
            id: 'test-event-id',
            status: 'confirmed',
          },
        }),
      },
    };

    const mockRescheduleRequest = {
      calendarOwnerId: userId,
      calendarEventId: 'test-event-id',
      newStart: {
        dateTime: '2025-05-16T14:00:00Z',
        timeZone: 'UTC',
      },
      newEnd: {
        dateTime: '2025-05-16T15:00:00Z',
        timeZone: 'UTC',
      },
    };

    beforeEach(() => {
      jest.clearAllMocks();
      (service as any).getAuthenticatedCalendarClient = jest.fn();
    });

    it('should return error when calendar client cannot be authenticated', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(err({ code: 'GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED' }));

      const result = await service.rescheduleEvent('test-event-id', mockRescheduleRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED');
    });

    it('should return error when calendar API call fails', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      mockCalendarApi.events.update.mockRejectedValueOnce(new Error('API error'));

      const result = await service.rescheduleEvent('test-event-id', mockRescheduleRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_UPDATE_EVENT_ERROR');
    });

    it('should return error when event ID is missing in response', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      mockCalendarApi.events.update.mockResolvedValueOnce({
        data: { status: 'confirmed' }, // No ID in response
      });

      const result = await service.rescheduleEvent('test-event-id', mockRescheduleRequest);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_UPDATE_EVENT_ERROR');
    });

    it('should successfully reschedule an event', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));

      const result = await service.rescheduleEvent('test-event-id', mockRescheduleRequest);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        calendarEventId: 'test-event-id',
        calendarId: 'primary',
        status: 'confirmed',
      });

      expect(mockCalendarApi.events.update).toHaveBeenCalledWith({
        calendarId: 'primary',
        eventId: 'test-event-id',
        requestBody: {
          start: mockRescheduleRequest.newStart,
          end: mockRescheduleRequest.newEnd,
        },
      });
    });
  });

  describe('cancelEvent', () => {
    const mockCalendarApi = {
      events: {
        update: jest.fn().mockResolvedValue({
          data: {
            id: 'test-event-id',
            status: 'cancelled',
          },
        }),
      },
    };

    const calendarEventId = 'test-event-id';

    beforeEach(() => {
      jest.clearAllMocks();
      (service as any).getAuthenticatedCalendarClient = jest.fn();
    });

    it('should return error when calendar client cannot be authenticated', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(err({ code: 'GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED' }));

      const result = await service.cancelEvent(calendarEventId, userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED');
    });

    it('should return error when calendar API call fails', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      mockCalendarApi.events.update.mockRejectedValueOnce(new Error('API error'));

      const result = await service.cancelEvent(calendarEventId, userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_UPDATE_EVENT_ERROR');
    });

    it('should return error when event ID is missing in response', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));
      mockCalendarApi.events.update.mockResolvedValueOnce({
        data: { status: 'cancelled' }, // No ID in response
      });

      const result = await service.cancelEvent(calendarEventId, userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CALENDAR_CLIENT_UPDATE_EVENT_ERROR');
    });

    it('should successfully cancel an event', async () => {
      (service as any).getAuthenticatedCalendarClient.mockResolvedValue(ok({ client: mockCalendarApi, calendarId: 'primary' }));

      const result = await service.cancelEvent(calendarEventId, userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual({
        calendarEventId: 'test-event-id',
        calendarId: 'primary',
        status: 'cancelled',
      });

      expect(mockCalendarApi.events.update).toHaveBeenCalledWith({
        calendarId: 'primary',
        eventId: calendarEventId,
        requestBody: {
          status: 'cancelled',
        },
      });
    });
  });
});
