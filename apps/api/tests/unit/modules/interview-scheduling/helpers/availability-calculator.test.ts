import { toDate } from 'date-fns';
import TimezoneMock from 'timezone-mock';

import { AvailabilityCalculator } from '@modules/interview-scheduling/helpers/availability-calculator';

type BusySlot = {
  utcStart: string;
  utcEnd: string;
};

const MINUTES_IN_MS = 60000;

describe('AvailabilityCalculator', () => {
  beforeEach(() => {
    TimezoneMock.register('US/Eastern');
  });

  afterEach(() => {
    TimezoneMock.unregister();
  });

  describe('calculateAvailabilitySlots', () => {
    it('should correctly calculate available slots with busy periods', () => {
      // Define test data based on the provided example
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots = [
        {
          utcStart: '2025-05-07T07:00:00Z',
          utcEnd: '2025-05-07T07:30:00Z',
        },
        {
          utcStart: '2025-05-07T09:00:00Z',
          utcEnd: '2025-05-07T09:30:00Z',
        },
      ];

      const availabilityPreferences = [
        { days: [3], startTime: '09:00:00', endTime: '13:15:00' },
        { days: [3], startTime: '15:00:00', endTime: '17:00:00' },
      ];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      // Call the method under test
      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Assert expected results based on the provided sample output
      expect(result).toHaveLength(4);
      expect(result[0].utcStart).toBe('2025-05-07T08:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T08:30:00.000Z');
      expect(result[1].utcStart).toBe('2025-05-07T10:00:00.000Z');
      expect(result[1].utcEnd).toBe('2025-05-07T10:30:00.000Z');
      expect(result[2].utcStart).toBe('2025-05-07T13:00:00.000Z');
      expect(result[2].utcEnd).toBe('2025-05-07T13:30:00.000Z');
      expect(result[3].utcStart).toBe('2025-05-07T14:00:00.000Z');
      expect(result[3].utcEnd).toBe('2025-05-07T14:30:00.000Z');
    });

    it('should return empty array when no availability preferences exist for the day', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      // No preference for Wednesday (day 3)
      const availabilityPreferences = [{ days: [1, 2, 4, 5], startTime: '09:00:00', endTime: '17:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      expect(result).toHaveLength(0);
    });

    it('should handle empty busy slots and create slots for the entire window', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '13:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Check that slots are created at 30-minute intervals with 15-minute buffers on each side
      // For Berlin timezone (UTC+2), 09:00-13:00 local is 07:00-11:00 UTC
      // Should get slots at: 7:00, 8:00, 9:00, 10:00
      expect(result).toHaveLength(4);
      expect(result[0].utcStart).toBe('2025-05-07T07:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T07:30:00.000Z');
      expect(result[1].utcStart).toBe('2025-05-07T08:00:00.000Z');
      expect(result[1].utcEnd).toBe('2025-05-07T08:30:00.000Z');
      expect(result[2].utcStart).toBe('2025-05-07T09:00:00.000Z');
      expect(result[2].utcEnd).toBe('2025-05-07T09:30:00.000Z');
      expect(result[3].utcStart).toBe('2025-05-07T10:00:00.000Z');
      expect(result[3].utcEnd).toBe('2025-05-07T10:30:00.000Z');
    });

    it('should respect date-specific preferences that override day preferences', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const dateObj = new Date('2025-05-07');
      const availabilityPreferences = [
        { days: [3], startTime: '09:00:00', endTime: '13:00:00' },
        { days: [], date: dateObj, startTime: '14:00:00', endTime: '18:00:00' },
      ];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should use date-specific preference (14:00-18:00) instead of day preference (09:00-13:00)
      // For Berlin timezone (UTC+2), 14:00-18:00 local is 12:00-16:00 UTC
      expect(result.length).toBeGreaterThan(0);
      expect(result[0].utcStart).toBe('2025-05-07T12:00:00.000Z');
      const resultDate = toDate(result[result.length - 1].utcEnd);
      const compareDate = toDate('2025-05-07T16:00:00.000Z');
      expect(resultDate.getTime()).toBeLessThanOrEqual(compareDate.getTime());
    });

    it('should mark date as unavailable when preference has 00:00 times', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-11T00:00:00Z',
        utcEndTimeString: '2025-05-11T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const dateObj = new Date('2025-05-11');
      const availabilityPreferences = [
        { days: [0], startTime: '09:00:00', endTime: '13:00:00' },
        { days: [], date: dateObj, startTime: '00:00:00', endTime: '00:00:00' },
      ];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should return no slots as the date is marked unavailable
      expect(result).toHaveLength(0);
    });

    it('should handle multiple availability windows in a single day', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [
        { days: [3], startTime: '09:00:00', endTime: '11:00:00' },
        { days: [3], startTime: '14:00:00', endTime: '16:00:00' },
      ];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should create slots for both time windows
      // For Berlin timezone (UTC+2), 9:00-11:00 local is 7:00-9:00 UTC
      // For Berlin timezone (UTC+2), 14:00-16:00 local is 12:00-14:00 UTC
      expect(result.length).toEqual(4);

      // Check first window slots
      expect(result[0].utcStart).toBe('2025-05-07T07:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T07:30:00.000Z');

      // Check second window slots
      const secondWindowSlot = result.find((slot) => slot.utcStart === '2025-05-07T08:00:00.000Z');
      expect(secondWindowSlot?.utcStart).toBe('2025-05-07T08:00:00.000Z');
      expect(secondWindowSlot?.utcEnd).toBe('2025-05-07T08:30:00.000Z');

      // Check third window slots
      const thirdWindowSlot = result.find((slot) => slot.utcStart === '2025-05-07T12:00:00.000Z');
      expect(thirdWindowSlot?.utcStart).toBe('2025-05-07T12:00:00.000Z');
      expect(thirdWindowSlot?.utcEnd).toBe('2025-05-07T12:30:00.000Z');

      // Check last window slots
      const lastWIndowSlot = result[result.length - 1];
      expect(lastWIndowSlot?.utcStart).toBe('2025-05-07T13:00:00.000Z');
      expect(lastWIndowSlot?.utcEnd).toBe('2025-05-07T13:30:00.000Z');
    });

    it('should handle availability windows spanning multiple days', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-09T23:59:59Z',
      };

      const busySlots = [
        {
          utcStart: '2025-05-08T09:00:00Z',
          utcEnd: '2025-05-08T10:30:00Z',
        },
      ];

      const availabilityPreferences = [{ days: [3, 4], startTime: '09:00:00', endTime: '13:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should have slots for both days, with busy period affecting day 2
      // Check if we have slots for day 1 (May 7)
      const day1Slots = result.filter((slot) => slot.utcStart.startsWith('2025-05-07'));
      // Check if we have slots for day 2 (May 8)
      const day2Slots = result.filter((slot) => slot.utcStart.startsWith('2025-05-08'));

      expect(day1Slots.length).toBeGreaterThan(0);
      expect(day2Slots.length).toBeGreaterThan(0);

      // There should be no slots during the busy period on day 2
      const busyPeriodSlots = result.filter((slot) => slot.utcStart >= '2025-05-08T09:00:00Z' && slot.utcEnd <= '2025-05-08T10:30:00Z');
      expect(busyPeriodSlots).toHaveLength(0);
    });

    it('should respect window boundaries', () => {
      // Set a narrow availability window
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:30:00Z',
        utcEndTimeString: '2025-05-07T11:30:00Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '17:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should only have slots within the window boundaries
      result.forEach((slot) => {
        expect(toDate(slot.utcStart).getTime()).toBeGreaterThanOrEqual(toDate(availabilityWindow.utcStartTimeString).getTime());
        expect(toDate(slot.utcEnd).getTime()).toBeLessThanOrEqual(toDate(availabilityWindow.utcEndTimeString).getTime());
      });
    });

    it('should handle different timezone calculations correctly', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '13:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      // Test with New York timezone (UTC-4 during DST)
      const userTimezone = 'America/New_York';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // For New York timezone (UTC-4), 09:00-13:00 local is 13:00-17:00 UTC
      expect(result.length).toBe(4); // 30 minutes call + 15 minutes before buffer + 15 minutes after buffer, so one hour per call
      expect(result[0].utcStart).toBe('2025-05-07T13:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T13:30:00.000Z');
      // last call ends at 16:30 UTC as after adding 15 minute after buffer new call can't be booked
      expect(result[result.length - 1].utcEnd).toBe('2025-05-07T16:30:00.000Z');
    });

    it('should account for buffers correctly when calculating slots', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '11:00:00' }];

      // Create a scenario with large buffers that should only allow one slot
      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 10,
        afterEventBuffer: 10,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // For Berlin timezone (UTC+2), 09:00-1100 local is 07:00-09:00 UTC
      // With 10 min buffers on each side and 30 min booking, we should get slots at:
      // 7:00, 7:50 (two slot)
      expect(result).toHaveLength(2);
      expect(result[0].utcStart).toBe('2025-05-07T07:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T07:30:00.000Z');
      // call ends at 07:30 UTC (30 minutes call).
      // add 10 minutes after the last call and 10 minutes before next call = 20 minutes
      // next call starts at 07:50 UTC
      expect(result[1].utcStart).toBe('2025-05-07T07:50:00.000Z');
      // call ends after 30 minutes so 08:20 UTC
      expect(result[1].utcEnd).toBe('2025-05-07T08:20:00.000Z');
    });

    it('should skip time slots that would be too small for the event', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '09:15:00' }];

      // Event duration is longer than the availability window
      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 0,
        afterEventBuffer: 0,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should have no slots since the availability window is only 15 minutes
      // but the event is 30 minutes
      expect(result).toHaveLength(0);
    });

    it('should filter relevant busy slots correctly', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T00:00:00Z',
        utcEndTimeString: '2025-05-07T23:59:59Z',
      };

      // Add busy slots outside the availability preference window
      const busySlots = [
        {
          utcStart: '2025-05-07T06:00:00Z',
          utcEnd: '2025-05-07T06:30:00Z',
        },
        {
          utcStart: '2025-05-07T08:00:00Z',
          utcEnd: '2025-05-07T08:30:00Z',
        },
        {
          utcStart: '2025-05-07T14:00:00Z',
          utcEnd: '2025-05-07T14:30:00Z',
        },
      ];

      const availabilityPreferences = [{ days: [3], startTime: '09:00:00', endTime: '13:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'Europe/Berlin';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // For Berlin timezone (UTC+2), 09:00-13:00 local is 07:00-11:00 UTC
      // The busy slot at 08:00 UTC should affect available slots,
      // but the ones at 06:00 and 14:00 should be ignored

      // Should have slots at 7:00, 9:00, 10:00 (3 slots)
      expect(result).toHaveLength(3);

      // Check if the slot at 8:00 UTC is missing due to the busy period
      const slot800 = result.find((slot) => slot.utcStart === '2025-05-07T08:00:00.000Z');
      expect(slot800).toBeUndefined();

      // Check if we have a slot at 7:00 UTC (which is before the busy slot)
      const slot700 = result.find((slot) => slot.utcStart === '2025-05-07T07:00:00.000Z');
      expect(slot700).toBeDefined();
    });
  });

  describe('findAvailableSlotsInWindow edge cases', () => {
    it('should tile the entire window when no busy slots exist', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T12:00:00Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '14:00:00' }];

      // Testing with precise buffer and booking time values
      const eventType = {
        bookingTime: 20, // 20-minute calls
        beforeEventBuffer: 5, // 5-minute buffer before
        afterEventBuffer: 5, // 5-minute buffer after
      };

      const userTimezone = 'UTC'; // Using UTC for precise time comparison

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // For a 2-hour window (10:00-12:00), with 20-minute calls and 10 minutes total buffer,
      // we should get exactly 4 slots (30 minutes per slot including buffers)
      expect(result).toHaveLength(4);
      expect(result[0].utcStart).toBe('2025-05-07T10:00:00.000Z');
      expect(result[0].utcEnd).toBe('2025-05-07T10:20:00.000Z');
      expect(result[1].utcStart).toBe('2025-05-07T10:30:00.000Z');
      expect(result[1].utcEnd).toBe('2025-05-07T10:50:00.000Z');
      expect(result[2].utcStart).toBe('2025-05-07T11:00:00.000Z');
      expect(result[2].utcEnd).toBe('2025-05-07T11:20:00.000Z');
      expect(result[3].utcStart).toBe('2025-05-07T11:30:00.000Z');
      expect(result[3].utcEnd).toBe('2025-05-07T11:50:00.000Z');
    });

    it('should handle a single busy slot correctly with slots before and after', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T13:00:00Z',
      };

      const busySlots = [
        {
          utcStart: '2025-05-07T11:00:00Z',
          utcEnd: '2025-05-07T12:00:00Z',
        },
      ];

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '13:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 0, // No buffer for easier calculation
        afterEventBuffer: 0,
      };

      const userTimezone = 'UTC';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should have slots before and after the busy period, but none during
      // Expect 2 slots before (10:00-10:30, 10:30-11:00) and 2 slots after (12:00-12:30, 12:30-13:00)
      expect(result).toHaveLength(4);

      // Check for slots before busy period
      expect(result.some((slot) => slot.utcStart === '2025-05-07T10:00:00.000Z')).toBe(true);
      expect(result.some((slot) => slot.utcStart === '2025-05-07T10:30:00.000Z')).toBe(true);

      // Check for slots after busy period
      expect(result.some((slot) => slot.utcStart === '2025-05-07T12:00:00.000Z')).toBe(true);
      expect(result.some((slot) => slot.utcStart === '2025-05-07T12:30:00.000Z')).toBe(true);

      // Verify no slots overlap with busy period
      const overlappingSlots = result.filter((slot) => {
        const start = new Date(slot.utcStart).getTime();
        const end = new Date(slot.utcEnd).getTime();
        const busyStart = new Date(busySlots[0].utcStart).getTime();
        const busyEnd = new Date(busySlots[0].utcEnd).getTime();

        return start < busyEnd && end > busyStart;
      });

      expect(overlappingSlots).toHaveLength(0);
    });

    it('should handle multiple busy slots by correctly resetting nextStart after each period', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T16:00:00Z',
      };

      const busySlots = [
        {
          utcStart: '2025-05-07T11:00:00Z',
          utcEnd: '2025-05-07T12:00:00Z',
        },
        {
          utcStart: '2025-05-07T13:00:00Z',
          utcEnd: '2025-05-07T14:00:00Z',
        },
      ];

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '16:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 10,
        afterEventBuffer: 10,
      };

      const userTimezone = 'UTC';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // There should be slots in three segments: before first busy, between busy periods, after second busy

      // Group slots by time segment
      const firstSegment = result.filter((slot) => new Date(slot.utcStart) < new Date(busySlots[0].utcStart));
      const middleSegment = result.filter(
        (slot) => new Date(slot.utcStart) >= new Date(busySlots[0].utcEnd) && new Date(slot.utcStart) < new Date(busySlots[1].utcStart)
      );
      const lastSegment = result.filter((slot) => new Date(slot.utcStart) >= new Date(busySlots[1].utcEnd));

      // Check if we have slots in each segment
      expect(firstSegment.length).toBeGreaterThan(0);
      expect(middleSegment.length).toBeGreaterThan(0);
      expect(lastSegment.length).toBeGreaterThan(0);

      // Verify the total matches
      expect(firstSegment.length + middleSegment.length + lastSegment.length).toBe(result.length);

      // Verify no slots overlap with busy periods
      const noOverlaps = result.every((slot) => {
        const start = new Date(slot.utcStart).getTime();
        const end = new Date(slot.utcEnd).getTime();

        return !busySlots.some((busySlot) => {
          const busyStart = new Date(busySlot.utcStart).getTime();
          const busyEnd = new Date(busySlot.utcEnd).getTime();
          return start < busyEnd && end > busyStart;
        });
      });

      expect(noOverlaps).toBe(true);
    });

    it('should handle edge boundaries when a slot would exactly end at windowEnd minus afterBuffer', () => {
      const availabilityWindow = {
        // Set up a window where the last slot would exactly end at windowEnd - afterBuffer
        utcStartTimeString: '2025-05-07T10:00:00Z',
        /** End exactly at the edge case.
         * NOTE, we will have to add the after event buffer
         * so even tho the call would end at 11:10,
         * we will have to add the after event buffer so it will end at 11:20
         */
        utcEndTimeString: '2025-05-07T11:20:00Z',
      };

      const busySlots: BusySlot[] = [];

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '12:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 0,
        afterEventBuffer: 10, // 10-minute after buffer
      };

      const userTimezone = 'UTC';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Check if the last slot ends at 11:00, which would leave exactly the afterBuffer (10 min) until windowEnd
      // Based on the implementation, it should exclude this slot as it would leave exactly the afterBuffer
      const expectedSlots = 2; // Should be 10:00-10:30 and 10:40-11:10

      expect(result).toHaveLength(expectedSlots);

      // Check the last slot
      const lastSlot = result[result.length - 1];
      expect(lastSlot.utcEnd).toBe('2025-05-07T11:10:00.000Z');

      // Verify the last slot's end time plus afterBuffer equals windowEnd
      const lastSlotEndPlusBuffer = new Date(lastSlot.utcEnd).getTime() + eventType.afterEventBuffer * MINUTES_IN_MS;
      const windowEndTime = new Date(availabilityWindow.utcEndTimeString).getTime();

      expect(lastSlotEndPlusBuffer).toBe(windowEndTime);
    });

    it('should return empty array for zero-length windows or when callTime is too large', () => {
      // Test 1: Zero-length window
      const zeroLengthWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T10:00:00Z', // Same as start time
      };

      const busySlots: BusySlot[] = [];
      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '12:00:00' }];
      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 5,
        afterEventBuffer: 5,
      };
      const userTimezone = 'UTC';

      const zeroLengthResult = AvailabilityCalculator.calculateAvailabilitySlots(zeroLengthWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      expect(zeroLengthResult).toHaveLength(0);

      // Test 2: Call time too large for window
      const smallWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T10:20:00Z', // Only 20 minutes
      };

      const largeEventType = {
        bookingTime: 30, // 30-minute call is too long for a 20-minute window
        beforeEventBuffer: 0,
        afterEventBuffer: 0,
      };

      const tooLargeResult = AvailabilityCalculator.calculateAvailabilitySlots(smallWindow, busySlots, availabilityPreferences, largeEventType, userTimezone);

      expect(tooLargeResult).toHaveLength(0);
    });
  });

  describe('Advanced availability calculation scenarios', () => {
    it('should clamp preference window to availability window edges', () => {
      // Availability window is only 10:00-14:00 UTC
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T14:00:00Z',
      };

      const busySlots: BusySlot[] = [];

      // Preference window is much wider than availability window (08:00-16:00 UTC)
      const availabilityPreferences = [{ days: [3], startTime: '08:00:00', endTime: '16:00:00' }];

      const eventType = {
        bookingTime: 60,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'UTC';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Should still produce slots, but only within the availability window
      expect(result.length).toBeGreaterThan(0);

      // All slots should be within the availability window
      result.forEach((slot) => {
        const slotStart = new Date(slot.utcStart).getTime();
        const slotEnd = new Date(slot.utcEnd).getTime();
        const windowStart = new Date(availabilityWindow.utcStartTimeString).getTime();
        const windowEnd = new Date(availabilityWindow.utcEndTimeString).getTime();

        expect(slotStart).toBeGreaterThanOrEqual(windowStart);
        expect(slotEnd).toBeLessThanOrEqual(windowEnd);
      });

      // First slot should start at window start (10:00 UTC)
      expect(result[0].utcStart).toBe('2025-05-07T10:00:00.000Z');

      // Last slot should end with enough time for afterEventBuffer before window end
      const lastSlot = result[result.length - 1];
      const lastSlotEnd = new Date(lastSlot.utcEnd).getTime();
      const windowEnd = new Date(availabilityWindow.utcEndTimeString).getTime();
      const bufferMs = eventType.afterEventBuffer * 60 * 1000;

      expect(lastSlotEnd + bufferMs).toBeLessThanOrEqual(windowEnd);
    });

    it('should handle busy slots with exact buffer boundaries', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T14:00:00Z',
      };

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15, // 15-minute before buffer
        afterEventBuffer: 15, // 15-minute after buffer
      };

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '14:00:00' }];
      const userTimezone = 'UTC';

      // Scenario 1: Busy slot ends exactly at (nextStart - beforeBuffer)
      // If a slot ends at 11:00, and beforeBuffer is 15 min, busy slot ending at 10:45
      const busySlots1 = [
        {
          utcStart: '2025-05-07T10:15:00Z',
          utcEnd: '2025-05-07T10:45:00Z', // Ends exactly at next potential slot start minus beforeBuffer
        },
      ];

      const result1 = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots1, availabilityPreferences, eventType, userTimezone);

      // Should have a slot at 11:15 (after the busy period + afterEventBuffer + beforeEventBuffer)
      expect(result1.some((slot) => slot.utcStart === '2025-05-07T11:15:00.000Z')).toBe(true);
      expect(result1.some((slot) => slot.utcEnd === '2025-05-07T11:45:00.000Z')).toBe(true);

      // Scenario 2: Busy slot begins exactly at (slotEnd + afterBuffer)
      // If a slot ends at 11:00, and afterBuffer is 15 min, busy slot starting at 11:15
      const busySlots2 = [
        {
          utcStart: '2025-05-07T11:15:00Z', // Starts exactly at previous potential slot end plus afterBuffer
          utcEnd: '2025-05-07T11:45:00Z',
        },
      ];

      const result2 = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots2, availabilityPreferences, eventType, userTimezone);

      // Should have a slot ending at 10:30
      /**
       * Explanation: if a slot ends at 10:30 and we add 15 minutes after buffer (1045),
       * for next call, we'll have to add 15 minutes before buffer as well (1100),
       * however, we have a busy slot starting at 11:15 and our call time is 30 minutes,
       * so we can't book a call at 1100 amd and hence the next slot would begin at 1215
       * 11:45 + 15 minutes after buffer = 1200
       * 1200 + 15 minutes before buffer = 1215
       */
      expect(result2.some((slot) => slot.utcEnd === '2025-05-07T10:30:00.000Z')).toBe(true);
    });

    it('should handle unsorted and overlapping busy slots', () => {
      const availabilityWindow = {
        utcStartTimeString: '2025-05-07T10:00:00Z',
        utcEndTimeString: '2025-05-07T16:00:00Z',
      };

      // Deliberately provide unsorted and overlapping busy slots
      const busySlots = [
        // Out of order
        {
          utcStart: '2025-05-07T13:00:00Z',
          utcEnd: '2025-05-07T14:00:00Z',
        },
        {
          utcStart: '2025-05-07T11:00:00Z',
          utcEnd: '2025-05-07T12:00:00Z',
        },
        // Overlapping with the second slot
        {
          utcStart: '2025-05-07T11:30:00Z',
          utcEnd: '2025-05-07T12:30:00Z',
        },
      ];

      const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '16:00:00' }];

      const eventType = {
        bookingTime: 30,
        beforeEventBuffer: 15,
        afterEventBuffer: 15,
      };

      const userTimezone = 'UTC';

      const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

      // Verify we get slots in expected free periods
      // Group slots into expected time regions
      /**
       * we have a busy slot starting at 11:00 so there can't be any slot after 10.30am
       */
      const beforeFirstBusy = result.filter((slot) => new Date(slot.utcEnd).getTime() <= new Date('2025-05-07T10:45:00Z').getTime());

      /**
       * since we have a busy slot from 1300 - 1400, the next slot can't be until 1430
       * because we have 15 minutes before buffer and 30 minutes call time
       */
      const afterLastBusy = result.filter((slot) => new Date(slot.utcStart).getTime() >= new Date('2025-05-07T14:15:00Z').getTime());

      // Verify we have slots in the first and last regions at minimum
      expect(beforeFirstBusy.length).toBeGreaterThan(0);
      expect(afterLastBusy.length).toBeGreaterThan(0);

      // Verify no slots overlap with any busy period (including the overlapping ones)
      const noOverlaps = result.every((slot) => {
        const slotStart = new Date(slot.utcStart).getTime();
        const slotEnd = new Date(slot.utcEnd).getTime();

        return !busySlots.some((busySlot) => {
          const busyStart = new Date(busySlot.utcStart).getTime();
          const busyEnd = new Date(busySlot.utcEnd).getTime();

          // Check if there's any overlap
          return slotStart < busyEnd && slotEnd > busyStart;
        });
      });

      expect(noOverlaps).toBe(true);
    });
  });

  it('should handle zero vs non-zero buffer scenarios correctly', () => {
    const availabilityWindow = {
      utcStartTimeString: '2025-05-07T10:00:00Z',
      utcEndTimeString: '2025-05-07T12:00:00Z',
    };

    const busySlots = [
      {
        utcStart: '2025-05-07T11:00:00Z',
        utcEnd: '2025-05-07T11:30:00Z',
      },
    ];

    const availabilityPreferences = [{ days: [3], startTime: '10:00:00', endTime: '12:00:00' }];
    const userTimezone = 'UTC';

    // Scenario 1: Both buffers are zero
    const zeroBufferEventType = {
      bookingTime: 30,
      beforeEventBuffer: 0,
      afterEventBuffer: 0,
    };

    const zeroBufferResult = AvailabilityCalculator.calculateAvailabilitySlots(
      availabilityWindow,
      busySlots,
      availabilityPreferences,
      zeroBufferEventType,
      userTimezone
    );

    // With zero buffers, we should be able to book slots back-to-back
    // For a 2-hour window (10:00-12:00) with one 30-minute busy slot,
    // we should get 3 slots (each 30 minutes, no buffer)
    expect(zeroBufferResult).toHaveLength(3);

    // Check for adjacent slots with no buffer
    const slot1 = zeroBufferResult.find((slot) => slot.utcEnd === '2025-05-07T10:30:00.000Z');
    const slot2 = zeroBufferResult.find((slot) => slot.utcStart === '2025-05-07T10:30:00.000Z');

    expect(slot1).toBeDefined();
    expect(slot2).toBeDefined();

    // Scenario 2: Only before buffer is non-zero
    const onlyBeforeBufferEventType = {
      bookingTime: 30,
      beforeEventBuffer: 15,
      afterEventBuffer: 0,
    };

    const beforeBufferResult = AvailabilityCalculator.calculateAvailabilitySlots(
      availabilityWindow,
      busySlots,
      availabilityPreferences,
      onlyBeforeBufferEventType,
      userTimezone
    );

    // Check that before buffer is applied, but not after buffer
    // There should be no slot starting at 10:45 (due to before buffer for busy at 11:00)
    const slot10_45 = beforeBufferResult.find((slot) => slot.utcStart === '2025-05-07T10:45:00.000Z');
    expect(slot10_45).toBeUndefined();

    /**
     * There should be only one slot available.
     * Why? because we'll have first slot from 1000 -1030
     * the next event can't start until 1045 because we have 15 minutes before buffer
     * the call is for 30 minutes so we'll overlap with the busy slot which is from 1100 - 1130
     * the next available slot could be at 1145 but the user has availability preference till 1200
     * which means no other call can be booked
     */
    expect(beforeBufferResult).toHaveLength(1);

    // Scenario 3: Only after buffer is non-zero
    const onlyAfterBufferEventType = {
      bookingTime: 30,
      beforeEventBuffer: 0,
      afterEventBuffer: 15,
    };

    const afterBufferResult = AvailabilityCalculator.calculateAvailabilitySlots(
      availabilityWindow,
      busySlots,
      availabilityPreferences,
      onlyAfterBufferEventType,
      userTimezone
    );

    // Check that after buffer is applied, but not before buffer
    // There should be no slot starting at 11:30 (due to after buffer for busy ending at 11:30)
    const slotAt11_30 = afterBufferResult.find((slot) => slot.utcStart === '2025-05-07T11:30:00.000Z');
    expect(slotAt11_30).toBeUndefined();

    /**
     * There should be only one slot before the busy period and no slot after the busy period
     * Why? Because we have 15 minutes after buffer, so the next slot can't start until 1045
     * and the call is for 30 minutes so it would end at 1115, however,
     * the user is busy from 1100 - 1130 so we can't book a call at 1045.
     * if we add 15 minutes buffer after the busy slot the next possible slot would be
     * at 1145 but user availability ends at 1200 so we can't book a call at 1145.
     */
    expect(afterBufferResult).toHaveLength(1);
  });

  it('should handle DST transition days correctly', () => {
    // Test for "spring forward" DST transition (lose one hour)
    // March 09, 2025 for US DST transition

    const availabilityWindow = {
      utcStartTimeString: '2025-03-08T00:00:00Z',
      utcEndTimeString: '2025-03-10T00:00:00Z', // Span across the DST transition
    };

    const busySlots: BusySlot[] = [];

    const availabilityPreferences = [
      { days: [0, 6], startTime: '09:00:00', endTime: '17:00:00' }, // Sunday & Saturday
    ];

    const eventType = {
      bookingTime: 60,
      beforeEventBuffer: 15,
      afterEventBuffer: 15,
    };

    const userTimezone = 'America/New_York'; // New York has DST changes

    const result = AvailabilityCalculator.calculateAvailabilitySlots(availabilityWindow, busySlots, availabilityPreferences, eventType, userTimezone);

    // Group slots by date
    const march08Slots = result.filter((slot) => slot.utcStart.startsWith('2025-03-08'));
    const march09Slots = result.filter((slot) => slot.utcStart.startsWith('2025-03-09'));

    // Both days should have slots
    expect(march08Slots.length).toBeGreaterThan(0);
    expect(march09Slots.length).toBeGreaterThan(0);

    // March 08 is before DST, March 09 is after "spring forward"
    // March 08: 9am ET = 14:00 UTC
    // March 09: 9am ET = 13:00 UTC (after springing forward)

    // Verify correct UTC times based on EDT/EST difference
    const march08FirstSlot = march08Slots[0];
    const march09FirstSlot = march09Slots[0];

    // First slot on March 08 should be at 14:00 UTC (9am ET before DST)
    expect(march08FirstSlot.utcStart).toBe('2025-03-08T14:00:00.000Z');

    // First slot on March 09 should be at 13:00 UTC (9am ET after DST)
    expect(march09FirstSlot.utcStart).toBe('2025-03-09T13:00:00.000Z');

    // Verify total counts are proportional to the available hours (considering DST change)
    const hourDiff = Math.abs(march08Slots.length - march09Slots.length);
    expect(hourDiff).toBeLessThanOrEqual(1); // Allow for at most 1 slot difference due to DST
  });
});
