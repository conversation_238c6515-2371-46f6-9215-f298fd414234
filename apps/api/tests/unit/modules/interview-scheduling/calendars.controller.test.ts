import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import {
  mockRequest,
  mockGoogleAuthCallbackQuery,
  authUrlResponseFixture,
  connectedStatusFixture,
  disconnectedCalendartatusFixture,
  unexpectedGoogleCallbackErrorFixture,
  expectedGoogleCallbackErrorFixture,
  unexpectedCalendarStatusErrorFixture,
  expectedCalendarStatusErrorFixture,
  unexpectedCalendarDisconnectErrorFixture,
  expectedCalendarDisconnectErrorFixture,
} from '@fixtures/modules/interview-scheduling';
import { SentryService } from '@lib/global/sentry.service';
import { CalendarsController } from '@modules/interview-scheduling/calendars.controller';
import { CalendarsService } from '@modules/interview-scheduling/calendars.service';

import { JwtGuard } from '@lib/guards/jwt.guard';

describe('CalendarsController', () => {
  let calendarsController: CalendarsController;
  let calendarsService: CalendarsService;
  let sentryService: SentryService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CalendarsController],
      providers: [
        {
          provide: CalendarsService,
          useValue: {
            initiateGoogleAuth: jest.fn(),
            handleGoogleCallback: jest.fn(),
            getStatus: jest.fn(),
            disconnect: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {
            logAndCaptureError: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    calendarsController = module.get<CalendarsController>(CalendarsController);
    calendarsService = module.get<CalendarsService>(CalendarsService);
    sentryService = module.get<SentryService>(SentryService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('initiateGoogleAuth', () => {
    it('should return auth URL when successful', async () => {
      jest.spyOn(calendarsService, 'initiateGoogleAuth').mockReturnValueOnce(authUrlResponseFixture.authUrl);

      const result = await calendarsController.initiateGoogleAuth(mockRequest)({});

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(authUrlResponseFixture);
      expect(calendarsService.initiateGoogleAuth).toHaveBeenCalledWith(mockRequest.user.id);
    });
  });

  describe('googleAuthCallback', () => {
    it('should handle successful callback', async () => {
      jest.spyOn(calendarsService, 'handleGoogleCallback').mockResolvedValue(ok());

      const result = await calendarsController.googleAuthCallback()({ query: mockGoogleAuthCallbackQuery });

      expect(result.status).toBe(HttpStatus.OK);
      expect(calendarsService.handleGoogleCallback).toHaveBeenCalledWith(mockGoogleAuthCallbackQuery.code, mockGoogleAuthCallbackQuery.state);
    });

    it('should handle errors and log unexpected errors', async () => {
      jest.spyOn(calendarsService, 'handleGoogleCallback').mockResolvedValue(unexpectedGoogleCallbackErrorFixture);
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.googleAuthCallback()({ query: mockGoogleAuthCallbackQuery });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });

    it('should handle expected errors without logging to Sentry', async () => {
      jest.spyOn(calendarsService, 'handleGoogleCallback').mockResolvedValue(expectedGoogleCallbackErrorFixture);
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.googleAuthCallback()({ query: mockGoogleAuthCallbackQuery });

      expect(result.status).toBe(HttpStatus.BAD_REQUEST);
      expect(result.body).toEqual({ message: 'Request expired. Please try again' });
      expect(logSpy).not.toHaveBeenCalled();
    });
  });

  describe('getStatus', () => {
    it('should return connection status when successful', async () => {
      jest.spyOn(calendarsService, 'getStatus').mockResolvedValue(ok(connectedStatusFixture));

      const result = await calendarsController.getStatus(mockRequest)({});

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(connectedStatusFixture);
      expect(calendarsService.getStatus).toHaveBeenCalledWith(mockRequest.user.id);
    });

    it('should handle errors and log unexpected errors', async () => {
      jest.spyOn(calendarsService, 'getStatus').mockResolvedValue(unexpectedCalendarStatusErrorFixture);

      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.getStatus(mockRequest)({});

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });

    it('should handle expected errors without logging to sentry', async () => {
      jest.spyOn(calendarsService, 'getStatus').mockResolvedValue(expectedCalendarStatusErrorFixture);
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.getStatus(mockRequest)({});

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Calendar not found' });
      expect(logSpy).not.toHaveBeenCalled();
    });
  });

  describe('disconnect', () => {
    it('should return disconnected status when successful', async () => {
      jest.spyOn(calendarsService, 'disconnect').mockResolvedValue(ok(disconnectedCalendartatusFixture));

      const result = await calendarsController.disconnect(mockRequest)({});

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(disconnectedCalendartatusFixture);
      expect(calendarsService.disconnect).toHaveBeenCalledWith(mockRequest.user.id);
    });

    it('should handle errors and log unexpected errors', async () => {
      jest.spyOn(calendarsService, 'disconnect').mockResolvedValue(unexpectedCalendarDisconnectErrorFixture);
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.disconnect(mockRequest)({});

      const error = unexpectedCalendarDisconnectErrorFixture._unsafeUnwrapErr();
      const message = error.isUnexpectedError ? error.originalError.message : '';

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message });
      expect(logSpy).toHaveBeenCalled();
    });

    it('should handle expected errors without logging to Sentry', async () => {
      jest.spyOn(calendarsService, 'disconnect').mockResolvedValue(expectedCalendarDisconnectErrorFixture);
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarsController.disconnect(mockRequest)({});

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Calendar not found' });
      expect(logSpy).not.toHaveBeenCalled();
    });
  });
});
