import { Test, TestingModule } from '@nestjs/testing';

import {
  calendarFixture,
  calendarDbError,
  createCalendarError,
  updateCalendarError,
  recordNotFoundError,
  createCalendarData,
  updateCalendarData,
  updatedCalendarFixture,
} from '@fixtures/modules/interview-scheduling';
import { DbService } from '@lib/global/db.service';
import { CalendarsRepository } from '@modules/interview-scheduling/calendars.repository';

import { mockDbService } from './mocks';

describe('CalendarsRepository', () => {
  let repository: CalendarsRepository;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CalendarsRepository,
        {
          provide: DbService,
          useValue: mockDbService,
        },
      ],
    }).compile();

    repository = module.get<CalendarsRepository>(CalendarsRepository);
  });

  describe('findCalendarByUserId', () => {
    it('should successfully find a calendar by user id', async () => {
      mockDbService.calendar.findUnique.mockResolvedValue(calendarFixture);

      const result = await repository.findCalendarByUserId(calendarFixture.userId);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(calendarFixture);
      expect(mockDbService.calendar.findUnique).toHaveBeenCalledWith({
        where: { userId: calendarFixture.userId },
      });
    });

    it('should return error when calendar does not exist', async () => {
      mockDbService.calendar.findUnique.mockResolvedValue(null);

      const result = await repository.findCalendarByUserId('non-existent-user-id');

      expect(result.isErr()).toBe(true);
    });

    it('should return db error when query fails', async () => {
      mockDbService.calendar.findUnique.mockRejectedValue(calendarDbError);

      const result = await repository.findCalendarByUserId(calendarFixture.userId);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr()).toEqual({
        code: 'FIND_CALENDAR_BY_USER_ID_DB_ERROR',
        isUnexpectedError: true,
        originalError: calendarDbError,
      });
    });
  });

  describe('findCalendarByUserIdAndProvider', () => {
    it('should successfully find a calendar by user id and provider', async () => {
      mockDbService.calendar.findFirst.mockResolvedValue(calendarFixture);

      const result = await repository.findCalendarByUserIdAndProvider(calendarFixture.userId, calendarFixture.provider);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(calendarFixture);
      expect(mockDbService.calendar.findFirst).toHaveBeenCalledWith({
        where: { userId: calendarFixture.userId, provider: calendarFixture.provider },
      });
    });

    it('should return not found error when calendar does not exist', async () => {
      mockDbService.calendar.findFirst.mockResolvedValue(null);

      const result = await repository.findCalendarByUserIdAndProvider(calendarFixture.userId, calendarFixture.provider);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_NOT_FOUND');
    });

    it('should return db error when query fails', async () => {
      mockDbService.calendar.findFirst.mockRejectedValue(calendarDbError);

      const result = await repository.findCalendarByUserIdAndProvider(calendarFixture.userId, calendarFixture.provider);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_DB_ERROR');
    });
  });

  describe('createCalendar', () => {
    it('should successfully create a calendar', async () => {
      mockDbService.calendar.create.mockResolvedValue(calendarFixture);

      const result = await repository.createCalendar(createCalendarData);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(calendarFixture);
      expect(mockDbService.calendar.create).toHaveBeenCalledWith({ data: createCalendarData });
    });

    it('should return db error when creation fails', async () => {
      mockDbService.calendar.create.mockRejectedValue(createCalendarError);

      const result = await repository.createCalendar(createCalendarData);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('CREATE_CALENDAR_DB_ERROR');
    });
  });

  describe('updateCalendarById', () => {
    it('should successfully update a calendar by id', async () => {
      mockDbService.calendar.update.mockResolvedValue(updatedCalendarFixture);

      const result = await repository.updateCalendarById(calendarFixture.id, updateCalendarData);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap().credentials.access_token).toBe('new-access-token');
      expect(mockDbService.calendar.update).toHaveBeenCalledWith({
        where: { id: calendarFixture.id },
        data: updateCalendarData,
      });
    });

    it('should return error when calendar does not exist', async () => {
      mockDbService.calendar.update.mockRejectedValue(recordNotFoundError);

      const result = await repository.updateCalendarById(calendarFixture.id, updateCalendarData);

      expect(result.isErr()).toBe(true);
    });

    it('should return db error when update fails', async () => {
      mockDbService.calendar.update.mockRejectedValue(updateCalendarError);

      const result = await repository.updateCalendarById(calendarFixture.id, updateCalendarData);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('UPDATE_CALENDAR_BY_ID_DB_ERROR');
    });
  });

  describe('updateCalendarByUserId', () => {
    it('should successfully update a calendar by user id', async () => {
      mockDbService.calendar.update.mockResolvedValue(updatedCalendarFixture);

      const result = await repository.updateCalendarByUserId(calendarFixture.userId, updateCalendarData);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap().credentials.access_token).toBe('new-access-token');
      expect(mockDbService.calendar.update).toHaveBeenCalledWith({
        where: { userId: calendarFixture.userId },
        data: updateCalendarData,
      });
    });

    it('should return error when calendar does not exist', async () => {
      mockDbService.calendar.update.mockRejectedValue(recordNotFoundError);

      const result = await repository.updateCalendarByUserId(calendarFixture.userId, updateCalendarData);

      expect(result.isErr()).toBe(true);
    });
  });
});
