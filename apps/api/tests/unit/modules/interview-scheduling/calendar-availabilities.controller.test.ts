import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { SentryService } from '@lib/global/sentry.service';
import { CalendarAvailabilitiesController } from '@modules/interview-scheduling/calendar-availabilities.controller';
import { CalendarAvailabilitiesService } from '@modules/interview-scheduling/calendar-availabilities.service';

import { Errors } from '@lib/errors';
import { JwtGuard } from '@lib/guards/jwt.guard';

// Mock fixtures
const mockUserId = 'user-123';
const mockEventTypeId = 'event-type-456';
const mockCalendarAvailabilityId = 'cal-avail-789';

const calendarAvailabilityFixture = {
  id: mockCalendarAvailabilityId,
  userId: mockUserId,
  eventTypeId: mockEventTypeId,
  days: [0, 1, 2, 3, 4],
  date: null,
  startTime: '09:00',
  endTime: '17:00',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const createCalendarAvailabilityDtoFixture = {
  days: [0, 1, 2, 3, 4],
  date: null,
  startTime: '09:00',
  endTime: '17:00',
};

const updateCalendarAvailabilityDtoFixture = {
  days: [1, 3, 5],
  date: null,
  startTime: '10:00',
  endTime: '16:00',
};

const availabilitySlotsFixture = {
  timezone: 'Europe/Berlin',
  availabilities: [
    {
      utcStart: '2025-05-12T09:00:00Z',
      utcEnd: '2025-05-12T09:30:00Z',
    },
    {
      utcStart: '2025-05-12T10:00:00Z',
      utcEnd: '2025-05-12T10:30:00Z',
    },
  ],
};

describe('CalendarAvailabilitiesController', () => {
  let calendarAvailabilitiesController: CalendarAvailabilitiesController;
  let calendarAvailabilitiesService: CalendarAvailabilitiesService;
  let sentryService: SentryService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CalendarAvailabilitiesController],
      providers: [
        {
          provide: CalendarAvailabilitiesService,
          useValue: {
            createCalendarAvailability: jest.fn(),
            getCalendarAvailability: jest.fn(),
            getCalendarAvailabilityByUserAndEventTypeId: jest.fn(),
            updateCalendarAvailability: jest.fn(),
            deleteCalendarAvailability: jest.fn(),
            getAvailabilitySlots: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {
            logAndCaptureError: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    calendarAvailabilitiesController = module.get<CalendarAvailabilitiesController>(CalendarAvailabilitiesController);
    calendarAvailabilitiesService = module.get<CalendarAvailabilitiesService>(CalendarAvailabilitiesService);
    sentryService = module.get<SentryService>(SentryService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createCalendarAvailability', () => {
    it('should successfully create a calendar availability', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'createCalendarAvailability').mockResolvedValue(ok(calendarAvailabilityFixture));

      const result = await calendarAvailabilitiesController.createCalendarAvailability()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
        body: createCalendarAvailabilityDtoFixture,
      });

      expect(result.status).toBe(HttpStatus.CREATED);
      expect(result.body).toEqual(calendarAvailabilityFixture);
      expect(calendarAvailabilitiesService.createCalendarAvailability).toHaveBeenCalledWith(mockUserId, mockEventTypeId, createCalendarAvailabilityDtoFixture);
    });

    it('should handle unexpected errors and log to Sentry', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'createCalendarAvailability').mockResolvedValue(
        Errors.createError('CREATE_CALENDAR_AVAILABILITY_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Database connection error'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.createCalendarAvailability()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
        body: createCalendarAvailabilityDtoFixture,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('getCalendarAvailability', () => {
    it('should successfully get a calendar availability by id', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getCalendarAvailability').mockResolvedValue(ok(calendarAvailabilityFixture));

      const result = await calendarAvailabilitiesController.getCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(calendarAvailabilityFixture);
      expect(calendarAvailabilitiesService.getCalendarAvailability).toHaveBeenCalledWith(mockCalendarAvailabilityId);
    });

    it('should return 404 when calendar availability is not found', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getCalendarAvailability').mockResolvedValue(Errors.createError('FIND_CALENDAR_AVAILABILITY_BY_ID_NOT_FOUND'));
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getCalendarAvailability()({
        params: { id: 'non-existent-id' },
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Calendar availability not found' });
      expect(logSpy).not.toHaveBeenCalled();
    });

    it('should handle unexpected errors and log to Sentry', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getCalendarAvailability').mockResolvedValue(
        Errors.createError('FIND_CALENDAR_AVAILABILITY_BY_ID_DB_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Database error when fetching availability'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('getCalendarAvailabilityByUserAndEventTypeId', () => {
    it('should successfully get calendar availability by user and event type', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getCalendarAvailabilityByUserAndEventTypeId').mockResolvedValue(ok([calendarAvailabilityFixture]));

      const result = await calendarAvailabilitiesController.getCalendarAvailabilityByUserAndEventTypeId()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual([calendarAvailabilityFixture]);
      expect(calendarAvailabilitiesService.getCalendarAvailabilityByUserAndEventTypeId).toHaveBeenCalledWith(mockUserId, mockEventTypeId);
    });

    it('should handle unexpected errors and log to Sentry', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getCalendarAvailabilityByUserAndEventTypeId').mockResolvedValue(
        Errors.createError('FIND_CALENDAR_AVAILABILITIES_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Database error when fetching availabilities'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getCalendarAvailabilityByUserAndEventTypeId()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('updateCalendarAvailability', () => {
    it('should successfully update a calendar availability', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'updateCalendarAvailability').mockResolvedValue(ok(calendarAvailabilityFixture));

      const result = await calendarAvailabilitiesController.updateCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
        body: updateCalendarAvailabilityDtoFixture,
      });

      expect(result.status).toBe(HttpStatus.NO_CONTENT);
      expect(calendarAvailabilitiesService.updateCalendarAvailability).toHaveBeenCalledWith(mockCalendarAvailabilityId, updateCalendarAvailabilityDtoFixture);
    });

    it('should handle unexpected errors and log to Sentry', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'updateCalendarAvailability').mockResolvedValue(
        Errors.createError('UPDATE_CALENDAR_AVAILABILITY_BY_ID_DB_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Database error when updating availability'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.updateCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
        body: updateCalendarAvailabilityDtoFixture,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('deleteCalendarAvailability', () => {
    it('should successfully delete a calendar availability', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'deleteCalendarAvailability').mockResolvedValue(ok(calendarAvailabilityFixture));

      const result = await calendarAvailabilitiesController.deleteCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
      });

      expect(result.status).toBe(HttpStatus.NO_CONTENT);
      expect(calendarAvailabilitiesService.deleteCalendarAvailability).toHaveBeenCalledWith(mockCalendarAvailabilityId);
    });

    it('should handle unexpected errors and log to Sentry', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'deleteCalendarAvailability').mockResolvedValue(
        Errors.createError('DELETE_CALENDAR_AVAILABILITY_BY_ID_DB_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Database error when deleting availability'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.deleteCalendarAvailability()({
        params: { id: mockCalendarAvailabilityId },
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Internal server error' });
      expect(logSpy).toHaveBeenCalled();
    });
  });

  describe('getAvailabilitySlots', () => {
    const mockQuery = {
      utcStartTimeString: '2025-05-12T00:00:00Z',
      utcEndTimeString: '2025-05-18T23:59:59Z',
    };

    it('should successfully get availability slots', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getAvailabilitySlots').mockResolvedValue(ok(availabilitySlotsFixture));

      const result = await calendarAvailabilitiesController.getAvailabilitySlots()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
        query: mockQuery,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(availabilitySlotsFixture);
      expect(calendarAvailabilitiesService.getAvailabilitySlots).toHaveBeenCalledWith(
        mockUserId,
        mockEventTypeId,
        mockQuery.utcStartTimeString,
        mockQuery.utcEndTimeString
      );
    });

    it('should return 404 when user is not found', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getAvailabilitySlots').mockResolvedValue(Errors.createError('FIND_USER_BY_ID_NOT_FOUND'));
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getAvailabilitySlots()({
        params: { userId: 'non-existent-user', eventTypeId: mockEventTypeId },
        query: mockQuery,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'User not found' });
      expect(logSpy).not.toHaveBeenCalled();
    });

    it('should return 404 when calendar is not connected', async () => {
      jest
        .spyOn(calendarAvailabilitiesService, 'getAvailabilitySlots')
        .mockResolvedValue(Errors.createError('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED'));
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getAvailabilitySlots()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
        query: mockQuery,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Calendar not connected' });
      expect(logSpy).not.toHaveBeenCalled();
    });

    it('should return 404 when event type is not found', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getAvailabilitySlots').mockResolvedValue(Errors.createError('FIND_CALENDAR_EVENT_TYPE_BY_ID_NOT_FOUND'));
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getAvailabilitySlots()({
        params: { userId: mockUserId, eventTypeId: 'non-existent-event-type' },
        query: mockQuery,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Event type not found' });
      expect(logSpy).not.toHaveBeenCalled();
    });

    it('should handle unexpected errors when fetching busy slots', async () => {
      jest.spyOn(calendarAvailabilitiesService, 'getAvailabilitySlots').mockResolvedValue(
        Errors.createError('GET_BUSY_SLOTS_WITH_TIMEZONE_ERROR', {
          isUnexpectedError: true,
          originalError: new Error('Error fetching busy slots'),
        })
      );
      const logSpy = jest.spyOn(sentryService, 'logAndCaptureError');

      const result = await calendarAvailabilitiesController.getAvailabilitySlots()({
        params: { userId: mockUserId, eventTypeId: mockEventTypeId },
        query: mockQuery,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'Error fetching busy slots' });
      expect(logSpy).toHaveBeenCalled();
    });
  });
});
