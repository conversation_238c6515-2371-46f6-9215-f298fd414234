import { Test, TestingModule } from '@nestjs/testing';
import axios from 'axios';

import { ConfigService } from '@config/config.service';
import { VideoCallService } from '@modules/interview-scheduling/video-call.service';

import { mockConfigService, roomName, mockDailyCoSuccessResponse, mockDailyCoEmptyResponse, mockApiError } from './mocks';

jest.mock('axios');

describe('VideoCallService', () => {
  let service: VideoCallService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        VideoCallService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<VideoCallService>(VideoCallService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getVideoCallProviderUrl', () => {
    it('should successfully create a Daily.co room and return URL', async () => {
      (axios.post as jest.Mock).mockResolvedValueOnce(mockDailyCoSuccessResponse);

      const result = await service.getVideoCallProviderUrl('daily.co', roomName);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toBe(mockDailyCoSuccessResponse.data.url);
    });

    it('should return error when Daily.co API call fails', async () => {
      (axios.post as jest.Mock).mockRejectedValueOnce(mockApiError);

      const result = await service.getVideoCallProviderUrl('daily.co', roomName);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_VIDEO_CALL_URL_API_ERROR');
      expect(result._unsafeUnwrapErr().isUnexpectedError).toBe(true);
    });

    it('should return error when Daily.co API response is missing URL', async () => {
      (axios.post as jest.Mock).mockResolvedValueOnce(mockDailyCoEmptyResponse);

      const result = await service.getVideoCallProviderUrl('daily.co', roomName);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr()).toEqual({
        code: 'GET_VIDEO_CALL_URL_API_ERROR',
        isUnexpectedError: true,
        originalError: new Error('Daily.co API response missing URL'),
      });
    });

    it('should return error for invalid provider', async () => {
      const result = await service.getVideoCallProviderUrl('zoom', roomName);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toBe('GET_VIDEO_CALL_URL_INVALID_PROVIDER');
    });
  });
});
