// Mock repositories and services
export const mockCalendarsRepository = {
  findCalendarByUserId: jest.fn(),
  findCalendarByUserIdAndProvider: jest.fn(),
  createCalendar: jest.fn(),
  updateCalendarByUserId: jest.fn(),
  updateCalendarById: jest.fn(),
};

export const mockCommonUsersRepository = {
  findById: jest.fn(),
  findUserByIdNeverthrow: jest.fn(),
};

export const mockSentryService = {
  logAndCaptureError: jest.fn(),
};

export const mockConfigService = {
  get: jest.fn(),
};

export const mockDbService = {
  calendar: {
    findUnique: jest.fn(),
    findFirst: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  calendarAvailability: {
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
};

export const mockCalendarAvailabilitiesRepository = {
  findCalendarAvailabilitiesByUserAndEventTypeId: jest.fn(),
  findCalendarAvailabilityById: jest.fn(),
  createCalendarAvailabilityByUserAndEventTypeId: jest.fn(),
  updateCalendarAvailabilityById: jest.fn(),
  deleteCalendarAvailabilityById: jest.fn(),
};

export const mockCalendarsService = {
  getBusySlots: jest.fn(),
};

export const mockCalendarEventTypesRepository = {
  findCalendarEventTypeById: jest.fn(),
};

// Room name for video call tests
export const roomName = 'test-room';

// Mock API responses
export const mockDailyCoSuccessResponse = {
  data: {
    url: 'https://domain.daily.co/test-room',
  },
};

export const mockDailyCoEmptyResponse = {
  data: {},
};

export const mockVideoCallService = {
  getVideoCallProviderUrl: jest.fn(),
};

// Mock API error
export const mockApiError = new Error('API Error');
