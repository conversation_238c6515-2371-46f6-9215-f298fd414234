import { RateCalculator } from '@modules/missions/helpers/rate';

describe('RateCalculator', () => {
  describe('getMarginFromMarkup', () => {
    it('should correctly convert markup to margin', () => {
      expect(RateCalculator.getMarginFromMarkup(0.428571429)).toBeCloseTo(0.3, 5);
      expect(RateCalculator.getMarginFromMarkup(0.5)).toBeCloseTo(0.333333, 5);
      expect(RateCalculator.getMarginFromMarkup(1)).toBeCloseTo(0.5, 5);
      expect(RateCalculator.getMarginFromMarkup(0)).toBeCloseTo(0, 5);
    });
  });

  describe('getMarkupFromMargin', () => {
    it('should correctly convert margin to markup', () => {
      expect(RateCalculator.getMarkupFromMargin(0.3)).toBeCloseTo(0.428571429, 5);
      expect(RateCalculator.getMarkupFromMargin(0.333333)).toBeCloseTo(0.5, 5);
      expect(RateCalculator.getMarkupFromMargin(0.5)).toBeCloseTo(1, 5);
      expect(RateCalculator.getMarkupFromMargin(0)).toBeCloseTo(0, 5);
    });
  });

  describe('getClientRateFromBuilderRateAndMarkup', () => {
    it('should correctly calculate client rate from builder rate and markup', () => {
      expect(RateCalculator.getClientRateFromBuilderRateAndMarkup(87.5, 0.428571429)).toBeCloseTo(125, 2);
      expect(RateCalculator.getClientRateFromBuilderRateAndMarkup(100, 0.5)).toBeCloseTo(150, 2);
      expect(RateCalculator.getClientRateFromBuilderRateAndMarkup(100, 0)).toBeCloseTo(100, 2);
    });
  });

  describe('getClientRateFromBuilderRateAndMargin', () => {
    it('should correctly calculate client rate from builder rate and margin', () => {
      expect(RateCalculator.getClientRateFromBuilderRateAndMargin(87.5, 0.3)).toBeCloseTo(125, 2);
      expect(RateCalculator.getClientRateFromBuilderRateAndMargin(100, 0.333333)).toBeCloseTo(150, 2);
      expect(RateCalculator.getClientRateFromBuilderRateAndMargin(100, 0)).toBeCloseTo(100, 2);
    });
  });

  describe('getBuilderRateFromClientRateAndMarkup', () => {
    it('should correctly calculate builder rate from client rate and markup', () => {
      expect(RateCalculator.getBuilderRateFromClientRateAndMarkup(125, 0.428571429)).toBeCloseTo(87.5, 2);
      expect(RateCalculator.getBuilderRateFromClientRateAndMarkup(150, 0.5)).toBeCloseTo(100, 2);
      expect(RateCalculator.getBuilderRateFromClientRateAndMarkup(100, 0)).toBeCloseTo(100, 2);
    });
  });

  describe('getBuilderRateFromClientRateAndMargin', () => {
    it('should correctly calculate builder rate from client rate and margin', () => {
      expect(RateCalculator.getBuilderRateFromClientRateAndMargin(125, 0.3)).toBeCloseTo(87.5, 2);
      expect(RateCalculator.getBuilderRateFromClientRateAndMargin(150, 0.333333)).toBeCloseTo(100, 2);
      expect(RateCalculator.getBuilderRateFromClientRateAndMargin(100, 0)).toBeCloseTo(100, 2);
    });
  });

  describe('getMarkupFromPercentage', () => {
    it('should correctly convert percentage string to markup decimal', () => {
      expect(RateCalculator.getMarkupFromPercentage('30')).toBe(0.3);
      expect(RateCalculator.getMarkupFromPercentage('50')).toBe(0.5);
      expect(RateCalculator.getMarkupFromPercentage('0')).toBe(0);
    });

    it('should return undefined when input is invalid', () => {
      expect(RateCalculator.getMarkupFromPercentage('-10')).toBe(undefined);
      expect(RateCalculator.getMarkupFromPercentage('110')).toBe(undefined);
    });
  });
});
