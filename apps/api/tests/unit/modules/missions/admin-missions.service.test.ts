import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { CommonTalentSkillsRepository } from '@common/missions/talent-skills.repository';
import { CommonTalentIndustriesRepository } from '@common/talent-industries/talent-industries.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { talentIndustriesFixture, tfsOwnersFixture } from '@fixtures/modules/missions/admin-missions.service.fixtures';
import { roleCategoriesFixture, talentSkillsFixture } from '@fixtures/modules/missions/common.fixtures';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';
import { AdminMissionsService } from '@modules/missions/admin-missions.service';
import { MissionsRepository } from '@modules/missions/missions.repository';

import { Errors } from '@lib/errors';

describe('AdminMissionsService', () => {
  let service: AdminMissionsService;
  let commonUsersRepository: CommonUsersRepository;
  let commonTalentIndustriesRepository: CommonTalentIndustriesRepository;
  let commonTalentSkillsRepository: CommonTalentSkillsRepository;
  let missionsRepository: MissionsRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminMissionsService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: PlatformServiceClient,
          useValue: {
            signInWithPassword: jest.fn(),
            getTfsOwners: jest.fn(),
          },
        },
        {
          provide: CommonUsersRepository,
          useValue: {
            findUsersByEmails: jest.fn(),
          },
        },
        {
          provide: CommonTalentIndustriesRepository,
          useValue: {
            findAllTalentIndustries: jest.fn(),
          },
        },
        {
          provide: CommonTalentSkillsRepository,
          useValue: {
            findAllTalentSkills: jest.fn(),
          },
        },
        {
          provide: MissionsRepository,
          useValue: {
            findAllRoleCategories: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminMissionsService>(AdminMissionsService);
    commonUsersRepository = module.get<CommonUsersRepository>(CommonUsersRepository);
    commonTalentIndustriesRepository = module.get<CommonTalentIndustriesRepository>(CommonTalentIndustriesRepository);
    commonTalentSkillsRepository = module.get<CommonTalentSkillsRepository>(CommonTalentSkillsRepository);
    missionsRepository = module.get<MissionsRepository>(MissionsRepository);

    jest.spyOn(Logger, 'error').mockImplementation(jest.fn());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getAllMissionIndustries', () => {
    it('should return all mission industries as expected', async () => {
      jest.spyOn(commonTalentIndustriesRepository, 'findAllTalentIndustries').mockResolvedValue(ok(talentIndustriesFixture));

      const result = await service.getAllMissionIndustries();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const expectedResult = talentIndustriesFixture.map((t) => ({ id: t.id, name: t.name }));

        expect(result.value).toEqual(expectedResult);
      }
    });

    it('should return an error, since the db call failed', async () => {
      jest.spyOn(commonTalentIndustriesRepository, 'findAllTalentIndustries').mockResolvedValue(Errors.createError('FIND_ALL_TALENT_INDUSTRIES_DB_ERROR'));

      const result = await service.getAllMissionIndustries();

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code).toEqual('FIND_ALL_TALENT_INDUSTRIES_DB_ERROR');
      }
    });
  });

  describe('getTfsOwners', () => {
    it('should return all tfs owners as expected', async () => {
      jest.spyOn(commonUsersRepository, 'findUsersByEmails').mockResolvedValue(ok(tfsOwnersFixture));

      const result = await service.getTfsOwners();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const expectedResult = tfsOwnersFixture.map((t) => ({
          id: t.id,
          firstName: t.firstName!,
          lastName: t.lastName!,
          profilePictureURL: t.pictureURL,
        }));

        expect(result.value).toEqual(expectedResult);
      }
    });

    it('should return an error, since the db call failed', async () => {
      jest.spyOn(commonUsersRepository, 'findUsersByEmails').mockResolvedValue(Errors.createError('FIND_USERS_BY_EMAILS_DB_ERROR'));

      const result = await service.getTfsOwners();

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code).toEqual('FIND_USERS_BY_EMAILS_DB_ERROR');
      }
    });
  });

  describe('getRoleCategories', () => {
    it('should return all role categories', async () => {
      jest.spyOn(missionsRepository, 'findAllRoleCategories').mockResolvedValue(ok(roleCategoriesFixture));

      const result = await service.getRoleCategories();

      expect(result.isOk() && result.value).toEqual(roleCategoriesFixture);
      expect(missionsRepository.findAllRoleCategories).toHaveBeenCalled();
    });

    it('should return empty array when error occurs', async () => {
      jest.spyOn(missionsRepository, 'findAllRoleCategories').mockResolvedValue(Errors.createError('FIND_ALL_ROLE_CATEGORIES_DB_ERROR'));

      const result = await service.getRoleCategories();

      expect(result.isErr()).toBe(true);
    });
  });

  describe('getRoleSkills', () => {
    it('should return all role skills', async () => {
      jest.spyOn(commonTalentSkillsRepository, 'findAllTalentSkills').mockResolvedValue(ok(talentSkillsFixture));

      const result = await service.getRoleSkills();

      expect(result.isOk() && result.value).toEqual(talentSkillsFixture);
      expect(commonTalentSkillsRepository.findAllTalentSkills).toHaveBeenCalled();
    });

    it('should return the required error code when db error occurs', async () => {
      jest.spyOn(commonTalentSkillsRepository, 'findAllTalentSkills').mockResolvedValue(Errors.createError('FIND_ALL_TALENT_SKILLS_DB_ERROR'));

      const result = await service.getRoleSkills();

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('FIND_ALL_TALENT_SKILLS_DB_ERROR');
    });
  });
});
