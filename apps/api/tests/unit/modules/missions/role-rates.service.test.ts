import { Cache } from '@nestjs/cache-manager';
import { Test, TestingModule } from '@nestjs/testing';
import { GetRateGuidaceSchemaDto, QueryRateGuidaceSchemaDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { RateCalculator } from '@modules/missions/helpers/rate';
import { RoleRatesRepository } from '@modules/missions/role-rates.repository';
import { RoleRatesService } from '@modules/missions/role-rates.service';

import { Errors } from '@lib/errors';

describe('RoleRatesService', () => {
  let service: RoleRatesService;
  let cache: Cache;
  let roleRatesRepository: RoleRatesRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleRatesService,
        {
          provide: Cache,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
          },
        },
        {
          provide: RoleRatesRepository,
          useValue: {
            findHourlyRatesForRoleCategory: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<RoleRatesService>(RoleRatesService);
    cache = module.get<Cache>(Cache);
    roleRatesRepository = module.get<RoleRatesRepository>(RoleRatesRepository);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getRoleRateGuidance', () => {
    const categoryId = 'category-123';
    const markupPercentage = '30';
    const query: QueryRateGuidaceSchemaDto = { categoryId, markupPercentage };
    const roleRateRange = { min: 100, max: 200 };
    const expectedGuidance: GetRateGuidaceSchemaDto = { lowerBound: 143, upperBound: 286 };

    it('should return error when categoryId is missing', async () => {
      const result = await service.getRoleRateGuidance({ categoryId: '', markupPercentage });
      expect(result.isErr()).toBe(true);
    });

    it('should return error when markupPercentage is missing', async () => {
      const result = await service.getRoleRateGuidance({ categoryId, markupPercentage: '' });
      expect(result.isErr()).toBe(true);
    });

    it('should return error when getRoleRateRange returns error', async () => {
      const getRoleRateRangeSpy = jest.spyOn(service, 'getRoleRateRange').mockResolvedValue(Errors.createError('GET_ROLE_RATE_RANGE_NO_RATES'));

      const result = await service.getRoleRateGuidance(query);

      expect(result.isErr()).toBe(true);
      expect(getRoleRateRangeSpy).toHaveBeenCalledWith(categoryId);
    });

    it('should return null when calculated bounds are invalid', async () => {
      const getRoleRateRangeSpy = jest.spyOn(service, 'getRoleRateRange').mockResolvedValue(ok({ min: 0, max: 0 }));

      const result = await service.getRoleRateGuidance(query);

      expect(result.isErr()).toBe(true);
      expect(getRoleRateRangeSpy).toHaveBeenCalled();
    });

    it('should calculate and return rate guidance correctly', async () => {
      const getRoleRateRangeSpy = jest.spyOn(service, 'getRoleRateRange').mockResolvedValue(ok(roleRateRange));
      const getMarkupFromPercentageSpy = jest.spyOn(RateCalculator, 'getMarkupFromPercentage').mockReturnValue(0.3);
      const getClientRateFromBuilderRateAndMarkupSpy = jest
        .spyOn(RateCalculator, 'getClientRateFromBuilderRateAndMarkup')
        .mockReturnValueOnce(142.85714285714286)
        .mockReturnValueOnce(285.7142857142857);

      const result = await service.getRoleRateGuidance(query);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedGuidance);
        expect(getMarkupFromPercentageSpy).toHaveBeenCalledWith(markupPercentage);
        expect(getClientRateFromBuilderRateAndMarkupSpy).toHaveBeenCalledWith(roleRateRange.min, 0.3);
        expect(getClientRateFromBuilderRateAndMarkupSpy).toHaveBeenCalledWith(roleRateRange.max, 0.3);
        expect(getRoleRateRangeSpy).toHaveBeenCalled();
      }
    });
  });

  describe('getRoleRateRange', () => {
    const categoryId = 'category-123';
    const cacheKey = `rate-guidance:${categoryId}`;
    const cachedRange = { min: 100, max: 200 };
    const rates = [50, 75, 100, 125, 150, 175, 200, 225, 250];

    const statistics = {
      total: rates.length,
      percentiles: [50, 75, 100, 125, 150, 175, 200, 225, 250, 250, 250],
    };

    const expectedRange = { min: 100, max: 250 };

    it('should return cached range when available', async () => {
      const cacheGetSpy = jest.spyOn(cache, 'get').mockResolvedValue(cachedRange);

      const result = await service.getRoleRateRange(categoryId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(cachedRange);
        expect(cacheGetSpy).toHaveBeenCalledWith(cacheKey);
        expect(roleRatesRepository.findHourlyRatesForRoleCategory).not.toHaveBeenCalled();
      }
    });

    it('should return null when not enough rates are found', async () => {
      const cacheGetSpy = jest.spyOn(cache, 'get').mockResolvedValue(null);
      const roleRatesRepositorySpy = jest.spyOn(roleRatesRepository, 'findHourlyRatesForRoleCategory').mockResolvedValue(ok([50, 75]));

      const result = await service.getRoleRateRange(categoryId);

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(cacheGetSpy).toHaveBeenCalledWith(cacheKey);
        expect(roleRatesRepositorySpy).toHaveBeenCalled();
      }
    });

    it('should calculate, cache, and return rate range when not cached', async () => {
      const cacheGetSpy = jest.spyOn(cache, 'get').mockResolvedValue(null);
      const roleRatesRepositorySpy = jest.spyOn(roleRatesRepository, 'findHourlyRatesForRoleCategory').mockResolvedValue(ok(rates));

      const calculateStatisticsSpy = jest.spyOn(service, 'calculateStatistics').mockReturnValue(ok(statistics));

      const result = await service.getRoleRateRange(categoryId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedRange);
        expect(cacheGetSpy).toHaveBeenCalledWith(cacheKey);
        expect(roleRatesRepositorySpy).toHaveBeenCalled();
        expect(calculateStatisticsSpy).toHaveBeenCalledWith(rates);
        expect(cache.set).toHaveBeenCalledWith(cacheKey, expectedRange, expect.any(Number));
      }
    });
  });

  describe('calculatePercentile', () => {
    it('should return 0 when array is empty', () => {
      const result = jest.fn(() => service.calculatePercentile([], 50))();
      expect(result.isOk() && result.value).toBe(0);
    });

    it('should return error when percentile is out of range', () => {
      const firstPercentileResult = service.calculatePercentile([1, 2, 3], -10);
      expect(firstPercentileResult.isErr()).toBe(true);

      const secondPercentileResult = service.calculatePercentile([1, 2, 3], 110);
      expect(secondPercentileResult.isErr()).toBe(true);
    });

    it('should return the only value when array has one element', () => {
      const result = jest.fn(() => service.calculatePercentile([42], 50))();
      expect(result.isOk() && result.value).toBe(42);
    });

    it('should calculate percentile correctly for exact index', () => {
      const result = jest.fn(() => service.calculatePercentile([10, 20, 30, 40, 50], 50))();
      expect(result.isOk() && result.value).toBe(30);
    });

    it('should calculate percentile correctly for interpolated index', () => {
      const result = jest.fn(() => service.calculatePercentile([10, 20, 30, 40, 50], 25))();
      expect(result.isOk() && result.value).toBe(20);
    });

    it('should calculate percentile correctly for interpolated value', () => {
      const result = jest.fn(() => service.calculatePercentile([10, 20, 30, 40, 50], 75))();
      expect(result.isOk() && result.value).toBe(40);
    });
  });

  describe('calculateStatistics', () => {
    it('should return default statistics when array is empty', () => {
      const result = jest.fn(() => service.calculateStatistics([]))();
      expect(result.isOk() && result.value).toEqual({ total: 0, percentiles: new Array<number>(11).fill(0) });
    });

    it('should filter out non-positive rates', () => {
      const calculatePercentileSpy = jest.spyOn(service, 'calculatePercentile').mockReturnValue(ok(10));

      const result = jest.fn(() => service.calculateStatistics([10, -5, 0, 20]))();

      expect(result.isOk() && result.value.total).toBe(2);
      expect(calculatePercentileSpy).toHaveBeenCalledTimes(11);
    });

    it('should calculate statistics correctly', () => {
      const rates = [50, 75, 100, 125, 150, 175, 200];
      const calculatePercentileSpy = jest
        .spyOn(service, 'calculatePercentile')
        .mockReturnValueOnce(ok(50))
        .mockReturnValueOnce(ok(65))
        .mockReturnValueOnce(ok(80))
        .mockReturnValueOnce(ok(95))
        .mockReturnValueOnce(ok(110))
        .mockReturnValueOnce(ok(125))
        .mockReturnValueOnce(ok(140))
        .mockReturnValueOnce(ok(155))
        .mockReturnValueOnce(ok(170))
        .mockReturnValueOnce(ok(185))
        .mockReturnValueOnce(ok(200));

      const result = jest.fn(() => service.calculateStatistics(rates))();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        const value = result.value;

        expect(value.total).toBe(7);
        expect(value.percentiles).toEqual([50, 65, 80, 95, 110, 125, 140, 155, 170, 185, 200]);
        expect(calculatePercentileSpy).toHaveBeenCalledTimes(11);
      }
    });
  });
});
