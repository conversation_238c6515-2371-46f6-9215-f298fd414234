import { Prisma } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { GenerateObjectResult } from 'ai';
import { ok } from 'neverthrow';

import { CommonCompaniesService } from '@common/companies/companies.service';
import { CommonUsersService } from '@common/users/users.service';
import {
  clientRequestHelpPrompt,
  clientRequestTypePrompt,
  clientSolutionPrompt,
  clientTimelinePrompt,
  companyDescriptionPrompt,
  companyIndustriesPrompt,
  companyNamePrompt,
  rolesPrompt,
} from '@fixtures/modules/missions/helpers/missions-prefill';
import { userIdFixture } from '@fixtures/modules/missions/missions-prefill.repository.fixtures';
import {
  clientRegistrationFixture,
  companyEnrichmentFixture,
  llmResponseFixture,
  llmRolesResponseFixture,
  missionPrefillFixture,
  prefillRoleSkillsFixture,
  roleCategoryFixture,
  solutionFixture,
} from '@fixtures/modules/missions/missions-prefill.service.fixtures';
import { OpenAIClient } from '@lib/clients/open-ai.client';
import { MissionsPrefillRepository } from '@modules/missions/missions-prefill.repository';
import { MissionsPrefillService, PrefillLLMResponse, PrefillRoleLLMResponse, requestTypeLabel } from '@modules/missions/missions-prefill.service';
import { MissionsService } from '@modules/missions/missions.service';

import { Errors } from '@lib/errors';

describe('MissionsPrefillService', () => {
  let missionsPrefillService: MissionsPrefillService;
  let missionsPrefillRepository: MissionsPrefillRepository;
  let commonCompaniesService: CommonCompaniesService;
  let usersService: CommonUsersService;
  let missionsService: MissionsService;
  let openAiClient: OpenAIClient;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissionsPrefillService,
        {
          provide: MissionsPrefillRepository,
          useValue: {
            upsertMissionPrefill: jest.fn(),
            findMissionPrefillByUserId: jest.fn(),
            getClientRequestedSolution: jest.fn(),
            getMostCommonSkillsForSpecialization: jest.fn(),
          },
        },
        {
          provide: MissionsService,
          useValue: {
            getRoleCategories: jest.fn(),
          },
        },
        {
          provide: CommonUsersService,
          useValue: {
            getUserRegistrationData: jest.fn(),
          },
        },
        {
          provide: CommonCompaniesService,
          useValue: {
            getCompanyEnrichemntName: jest.fn(),
            getCompanyEnrichmentIndustries: jest.fn(),
            getCompanyEnrichmentDescription: jest.fn(),
            getCompanyEnrichmentTimezone: jest.fn(),
            getCompanyEnrichment: jest.fn(),
          },
        },
        {
          provide: OpenAIClient,
          useValue: {
            generateObject: jest.fn(),
            generateArray: jest.fn(),
          },
        },
      ],
    }).compile();

    missionsPrefillService = module.get<MissionsPrefillService>(MissionsPrefillService);
    missionsPrefillRepository = module.get<MissionsPrefillRepository>(MissionsPrefillRepository);
    usersService = module.get<CommonUsersService>(CommonUsersService);
    openAiClient = module.get<OpenAIClient>(OpenAIClient);
    commonCompaniesService = module.get<CommonCompaniesService>(CommonCompaniesService);
    missionsService = module.get<MissionsService>(MissionsService);

    jest.spyOn(Logger, 'error').mockImplementation(jest.fn());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generateMissionPrefill', () => {
    it('should generate and return mission prefill', async () => {
      jest.spyOn(missionsService, 'getRoleCategories').mockResolvedValue(ok([roleCategoryFixture]));

      const userSpy = jest.spyOn(usersService, 'getUserRegistrationData').mockResolvedValue(ok(clientRegistrationFixture as any));
      const companyEnrichemntSpy = jest.spyOn(commonCompaniesService, 'getCompanyEnrichment').mockResolvedValue(ok(companyEnrichmentFixture));
      const solutionSpy = jest.spyOn(missionsPrefillRepository, 'getClientRequestedSolution').mockResolvedValue(ok(solutionFixture));

      jest.spyOn(missionsPrefillRepository, 'getMostCommonSkillsForSpecialization').mockResolvedValue(ok(prefillRoleSkillsFixture));
      jest.spyOn(missionsService, 'getRoleCategories').mockResolvedValue(ok([roleCategoryFixture]));

      const generateSpy = jest.spyOn(openAiClient, 'generateObject').mockResolvedValue(ok(llmResponseFixture as GenerateObjectResult<PrefillLLMResponse>));
      const generateArraySpy = jest
        .spyOn(openAiClient, 'generateArray')
        .mockResolvedValue(ok(llmRolesResponseFixture as GenerateObjectResult<PrefillRoleLLMResponse[]>));

      const upsertSpy = jest.spyOn(missionsPrefillService, 'upsertMissionPrefill').mockResolvedValue(ok(missionPrefillFixture));
      const result = await missionsPrefillService.generateMissionPrefill(userIdFixture);

      expect(userSpy).toHaveBeenCalled();
      expect(companyEnrichemntSpy).toHaveBeenCalled();
      expect(solutionSpy).toHaveBeenCalled();

      expect(generateSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt:
            expect.stringContaining(companyNamePrompt(companyEnrichmentFixture?.structured?.name)) &&
            expect.stringContaining(companyIndustriesPrompt(companyEnrichmentFixture.structured?.industries.join(', '))) &&
            expect.stringContaining(companyDescriptionPrompt((companyEnrichmentFixture.clearbit?.data as Prisma.JsonObject).description as string)) &&
            expect.stringContaining(clientRequestTypePrompt(requestTypeLabel[clientRegistrationFixture?.requestType ?? 'BUILD_PRODUCT'])) &&
            expect.stringContaining(clientRequestHelpPrompt(clientRegistrationFixture?.requestHelp)) &&
            expect.stringContaining(clientSolutionPrompt(solutionFixture?.title)) &&
            expect.stringContaining(clientTimelinePrompt(clientRegistrationFixture?.hiringTimeline)),
        })
      );

      expect(generateArraySpy).toHaveBeenCalledWith(
        expect.objectContaining({
          prompt: expect.stringContaining(rolesPrompt([roleCategoryFixture])),
        })
      );

      expect(upsertSpy).toHaveBeenCalled();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(missionPrefillFixture);
      }
    });

    it('should return null if prefill data generation stopped due to error', async () => {
      jest
        .spyOn(usersService, 'getUserRegistrationData')
        .mockResolvedValue(Errors.createError('GET_USER_REGISTRATION_DATA_USER_REGISTRATION_DATA_NOT_FOUND' as const, { isUnexpectedError: true }));

      jest
        .spyOn(openAiClient, 'generateObject')
        .mockResolvedValue(ok({ ...llmResponseFixture, finishReason: 'error' } as GenerateObjectResult<PrefillLLMResponse>));

      const result = await missionsPrefillService.generateMissionPrefill('1');

      expect(result.isErr()).toBe(true);
    });
  });

  describe('getMissionPrefill', () => {
    it('should return mission prefill', async () => {
      jest.spyOn(missionsPrefillRepository, 'findMissionPrefillByUserId').mockResolvedValue(ok(missionPrefillFixture));
      const result = await missionsPrefillService.getMissionPrefillByUserId('22');

      expect(result.isOk() && result.value).toEqual(missionPrefillFixture);
    });
  });

  describe('upsertMissionPrefill', () => {
    it('should update and return mission prefill', async () => {
      jest.spyOn(missionsPrefillRepository, 'upsertMissionPrefill').mockResolvedValue(ok(missionPrefillFixture));

      const result = await missionsPrefillService.upsertMissionPrefill('22', { missionName: 'Test Mission' });

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(missionPrefillFixture);
      }
    });
  });
});
