import { HttpStatus } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfirmMissionDto, DraftMissionDto, GetRateGuidaceSchemaDto, PublishMissionDto, RequestRoleRemovalSchemaDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CommonHubspotService } from '@common/hubspot/hubspot.service';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { categoryIdFixture, missionFixture, requestRoleFixture } from '@fixtures/modules/missions/common.fixtures';
import {
  confirmedMissionFixture,
  hubspotDealFixture,
  publishMissionFixture,
  updatedMissionFixture,
} from '@fixtures/modules/missions/missions.controller.fixtures';
import { SentryService } from '@lib/global/sentry.service';
import { MissionsController } from '@modules/missions/missions.controller';
import { MissionsService } from '@modules/missions/missions.service';
import { RoleRatesService } from '@modules/missions/role-rates.service';

import { Errors } from '@lib/errors';
import { AccountJwtGuard, AuthenticatedAccountRequest } from '@lib/guards/account-jwt.guard';
import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

const mockReq = {
  user: { id: 'user‑1' },
  account: { id: 'account‑1' },
} as AuthenticatedAccountRequest;

describe('MissionsController', () => {
  let missionsController: MissionsController;
  let missionsService: MissionsService;
  let roleRatesService: RoleRatesService;
  let hubspotService: CommonHubspotService;
  let sentryService: SentryService;
  let rbacService: jest.Mocked<CommonRBACService>;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [MissionsController],
      providers: [
        {
          provide: MissionsService,
          useValue: {
            getMissionById: jest.fn(),
            getRoleCategories: jest.fn(),
            getTalentSkills: jest.fn(),
            createMission: jest.fn(),
            updateMission: jest.fn(),
            deleteMission: jest.fn(),
            confirmMission: jest.fn(),
            publishMission: jest.fn(),
            requestNewRole: jest.fn(),
            requestRoleRemoval: jest.fn(),
            approveRoleRequest: jest.fn(),
            rejectRoleRequest: jest.fn(),
            createRoleDeals: jest.fn(),
            processAssetsChanges: jest.fn(),
          },
        },
        {
          provide: RoleRatesService,
          useValue: {
            getRoleRateGuidance: jest.fn(),
          },
        },
        {
          provide: CommonHubspotService,
          useValue: {
            getDealById: jest.fn(),
          },
        },
        {
          provide: CommonUsersRepository,
          useValue: {
            findUserById: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {
            logAndCaptureError: jest.fn(),
          },
        },
        {
          provide: CommonRBACService,
          useValue: {
            checkClientPermission: jest.fn().mockResolvedValue(ok()),
            grantRole: jest.fn().mockResolvedValue(ok()),
          },
        },
      ],
    })
      .overrideGuard(AccountJwtGuard)
      .useValue({ canActivate: () => true })
      .overrideGuard(AdminJwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    missionsController = module.get<MissionsController>(MissionsController);
    missionsService = module.get<MissionsService>(MissionsService);
    roleRatesService = module.get<RoleRatesService>(RoleRatesService);
    hubspotService = module.get<CommonHubspotService>(CommonHubspotService);
    sentryService = module.get<SentryService>(SentryService);
    rbacService = module.get(CommonRBACService);
  });

  describe('getMissionById', () => {
    it('should return mission details when mission exists', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.getMissionById(mockReq)({ params: { id: missionFixture.mid } });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual({
        companyStory: missionFixture.companyStory,
        description: missionFixture.description,
        logoURL: missionFixture.logoURL,
        mid: missionFixture.mid,
        overlapMinutes: missionFixture.overlapMinutes,
        plannedStart: missionFixture.plannedStart,
        roles: [
          {
            availability: null,
            budget: null,
            categoryId: 'category-1',
            customQuestions: [],
            headline: 'Role headline',
            id: 'role-id',
            isFullTimeRetainer: false,
            locations: [],
            markup: null,
            preferredSkills: [],
            requiredSkills: [],
            status: 'Open',
            assignedUser: {
              firstName: 'John',
              id: 'user-id',
              lastName: 'Doe',
              pictureURL: 'http://example.com/picture.png',
            },
          },
        ],
        status: missionFixture.status,
        timezone: missionFixture.timezone,
        title: missionFixture.title,
        videoURL: missionFixture.videoURL,
      });
    });

    it('should return 403 when user does not have access to the mission', async () => {
      jest.spyOn(rbacService, 'checkClientPermission').mockResolvedValueOnce(Errors.createError('CHECK_CLIENT_PERMISSION_UNAUTHORIZED'));

      const result = await missionsController.getMissionById(mockReq)({ params: { id: missionFixture.mid } });

      expect(result.status).toBe(HttpStatus.FORBIDDEN);
    });

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValueOnce(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.getMissionById(mockReq)({ params: { id: 'non-existent' } });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });
  });

  describe('getRoleCategories', () => {
    it('should return role categories', async () => {
      const mockCategories = [{ id: '1', title: 'category1' }];
      jest.spyOn(missionsService, 'getRoleCategories').mockResolvedValue(ok(mockCategories));

      const result = await missionsController.getRoleCategories()({});

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(mockCategories);
    });
  });

  describe('getTalentSkills', () => {
    it('should return talent skills', async () => {
      const mockSkills = [{ id: '1', name: 'skill1' }];
      jest.spyOn(missionsService, 'getTalentSkills').mockResolvedValue(ok(mockSkills));

      const result = await missionsController.getTalentSkills()({});

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(mockSkills);
    });
  });

  describe('getHubspotDeal', () => {
    it('should return deal details when deal exists', async () => {
      jest.spyOn(hubspotService, 'getDealById').mockResolvedValue(ok(hubspotDealFixture));

      const result = await missionsController.getHubspotDeal()({ params: { dealId: '1' } });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual({
        id: hubspotDealFixture.id,
        name: hubspotDealFixture.name,
        createdAt: hubspotDealFixture.createdAt,
      });
    });

    it('should return 500 when deal does not exist', async () => {
      jest.spyOn(hubspotService, 'getDealById').mockResolvedValue(Errors.createError('GET_DEAL_BY_ID_API_ERROR'));

      const result = await missionsController.getHubspotDeal()({ params: { dealId: 'non-existent' } });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('createMission', () => {
    const newMissionData: DraftMissionDto = {
      title: 'Test Mission',
      videoURL: null,
      logoURL: null,
      roles: [],
    };

    it('should create a mission successfully', async () => {
      const createMissionMock = jest.spyOn(missionsService, 'createMission').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.createMission(mockReq)({ body: newMissionData });

      expect(result.status).toBe(HttpStatus.CREATED);
      expect(result.body).toEqual({ mid: missionFixture.mid });
      expect(createMissionMock).toHaveBeenCalled();
    });

    it('should return 500 when a mission creation error is thrown', async () => {
      const createMissionMock = jest.spyOn(missionsService, 'createMission').mockResolvedValue(Errors.createError('CREATE_MISSION_DB_ERROR'));

      const result = await missionsController.createMission(mockReq)({ body: newMissionData });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(result.body).toEqual({ message: 'An error occurred. Please contact support.' });
      expect(createMissionMock).toHaveBeenCalled();
    });

    it('should call the function for granting the role with the correct parameters after creating a mission', async () => {
      const createMissionMock = jest.spyOn(missionsService, 'createMission').mockResolvedValue(ok(missionFixture));
      const grantRoleSpy = jest.spyOn(rbacService, 'grantRole').mockResolvedValue(ok());

      await missionsController.createMission(mockReq)({ body: newMissionData });

      expect(grantRoleSpy).toHaveBeenCalledWith(mockReq.user.id, mockReq.account.id, { role: 'missionadmin', missionId: missionFixture.mid });
      expect(createMissionMock).toHaveBeenCalled();
    });

    it('should delete the mission if granting the role fails and return 500', async () => {
      const deleteMissionMock = jest.spyOn(missionsService, 'deleteMission').mockResolvedValue(ok(missionFixture));
      const createMissionMock = jest.spyOn(missionsService, 'createMission').mockResolvedValue(ok(missionFixture));
      const grantRoleMock = jest.spyOn(rbacService, 'grantRole').mockResolvedValue(Errors.createError('GRANT_ROLE_API_ERROR'));

      const result = await missionsController.createMission(mockReq)({ body: newMissionData });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(deleteMissionMock).toHaveBeenCalledWith(missionFixture.mid, true);
      expect(createMissionMock).toHaveBeenCalled();
      expect(grantRoleMock).toHaveBeenCalled();
    });

    it('should call sentry if deleting the mission fails with an unexpected error', async () => {
      const sentryLogAndCaptureMock = jest.spyOn(sentryService, 'logAndCaptureError').mockImplementation();
      const createMissionMock = jest.spyOn(missionsService, 'createMission').mockResolvedValue(ok(missionFixture));
      const grantRoleMock = jest.spyOn(rbacService, 'grantRole').mockResolvedValue(Errors.createError('GRANT_ROLE_API_ERROR'));
      const deleteMissionMock = jest
        .spyOn(missionsService, 'deleteMission')
        .mockResolvedValue(Errors.createError('DELETE_MISSION_DB_ERROR', { isUnexpectedError: true }));

      const result = await missionsController.createMission(mockReq)({ body: newMissionData });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      expect(deleteMissionMock).toHaveBeenCalledWith(missionFixture.mid, true);
      expect(createMissionMock).toHaveBeenCalled();
      expect(grantRoleMock).toHaveBeenCalled();
      expect(sentryLogAndCaptureMock).toHaveBeenCalled();
    });
  });

  describe('updateMission', () => {
    const updateMissionData: DraftMissionDto = {
      title: 'Title Updated',
      videoURL: null,
      logoURL: null,
      roles: [],
    };

    it('should update mission successfully', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'updateMission').mockResolvedValue(ok(updatedMissionFixture));

      const result = await missionsController.updateMission(mockReq)({
        params: { id: missionFixture.mid },
        body: updateMissionData,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
    });

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.updateMission(mockReq)({
        params: { id: 'non-existent' },
        body: updateMissionData,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should return 400 when an error is thrown', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'updateMission').mockResolvedValue(Errors.createError('UPDATE_MISSION_DB_ERROR'));

      const result = await missionsController.updateMission(mockReq)({
        params: { id: missionFixture.mid },
        body: updateMissionData,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });

    it('should return 422 when mission is not in editable status', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok({ ...missionFixture, status: 'Running' }));

      const result = await missionsController.updateMission(mockReq)({
        params: { id: missionFixture.mid },
        body: updateMissionData,
      });

      expect(result.status).toBe(HttpStatus.UNPROCESSABLE_ENTITY);
      expect(result.body).toEqual({ message: 'Mission is not in editable status' });
    });
  });

  describe('confirmMission', () => {
    const confirmMissionData: ConfirmMissionDto = {
      title: 'Mission Confirmed',
      description: '<p>This is project description</p>',
      companyStory: '<p>This is company description</p>',
      plannedStart: 'Immediately',
      timeOverlap: '2h',
      timezone: 'Pacific/Niue',
      videoURL: null,
      logoURL: null,
      roles: [],
    };

    it('should confirm mission successfully', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'confirmMission').mockResolvedValue(ok(confirmedMissionFixture));

      const result = await missionsController.confirmMission(mockReq)({
        params: { id: missionFixture.mid },
        body: confirmMissionData,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
    });

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.confirmMission(mockReq)({
        params: { id: 'non-existent' },
        body: confirmMissionData,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should return 400 when an error is thrown', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'confirmMission').mockResolvedValue(Errors.createError('UPDATE_MISSION_DB_ERROR'));

      const result = await missionsController.confirmMission(mockReq)({
        params: { id: missionFixture.mid },
        body: confirmMissionData,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('publishMission', () => {
    const mockAccountReq = { ...mockReq, user: { id: 'user-id' } } as AuthenticatedAccountRequest;
    const publishMissionData: PublishMissionDto = {
      hubspotDealId: '1',
    };

    it('should publish mission successfully', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(confirmedMissionFixture));
      jest.spyOn(missionsService, 'publishMission').mockResolvedValue(ok(publishMissionFixture));
      jest.spyOn(missionsService, 'createRoleDeals').mockResolvedValue(ok());

      const result = await missionsController.publishMission(mockAccountReq)({
        params: { id: missionFixture.mid },
        body: publishMissionData,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
    });

    it('should return 400 when mission status is not Formation', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.publishMission(mockAccountReq)({
        params: { id: missionFixture.mid },
        body: publishMissionData,
      });

      expect(result.status).toBe(HttpStatus.BAD_REQUEST);
      expect(result.body).toEqual({ message: 'Mission is not in formation status' });
    });

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.publishMission(mockAccountReq)({
        params: { id: 'non-existent' },
        body: publishMissionData,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should return 500 when an error is thrown', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(confirmedMissionFixture));
      jest.spyOn(missionsService, 'publishMission').mockResolvedValue(Errors.createError('UPDATE_MISSION_DB_ERROR'));

      const result = await missionsController.publishMission(mockAccountReq)({
        params: { id: missionFixture.mid },
        body: publishMissionData,
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('requestRoleRemoval', () => {
    const roleRemovalRequestData: RequestRoleRemovalSchemaDto = {
      reason: 'Budget constraints',
      comment: 'We need to reduce the budget for this project',
    };

    it('should successfully request role removal', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      const requestRoleRemovalSpy = jest.spyOn(missionsService, 'requestRoleRemoval').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.requestRoleRemoval(mockReq)({
        params: { id: missionFixture.mid, roleId: 'role-1' },
        body: roleRemovalRequestData,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toBeUndefined();
      expect(requestRoleRemovalSpy).toHaveBeenCalledWith(missionFixture.mid, 'role-1', roleRemovalRequestData);
    });

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.requestRoleRemoval(mockReq)({
        params: { id: 'non-existent', roleId: 'role-1' },
        body: roleRemovalRequestData,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });
  });

  describe('getRoleRateGuidance', () => {
    it('should return role rate guidance when data exists', async () => {
      const rateGuidance: GetRateGuidaceSchemaDto = { lowerBound: 100, upperBound: 200 };
      jest.spyOn(roleRatesService, 'getRoleRateGuidance').mockResolvedValue(ok(rateGuidance));

      const result = await missionsController.getRoleRateGuidance()({ query: { categoryId: categoryIdFixture } });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(rateGuidance);
    });

    it('should return 404 when role rate guidance does not exist', async () => {
      jest.spyOn(roleRatesService, 'getRoleRateGuidance').mockResolvedValue(Errors.createError('GET_ROLE_RATE_RANGE_NO_RATES'));

      const result = await missionsController.getRoleRateGuidance()({ query: { categoryId: 'non-existent' } });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
    });
  });

  describe('requestNewRole', () => {
    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.requestNewRole(mockReq)({
        params: { id: 'non-existent' },
        body: requestRoleFixture,
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should return 422 when mission is in editable status', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(
        ok({
          ...missionFixture,
          status: 'Spec',
        })
      );

      const result = await missionsController.requestNewRole(mockReq)({
        params: { id: missionFixture.mid },
        body: requestRoleFixture,
      });

      expect(result.status).toBe(HttpStatus.UNPROCESSABLE_ENTITY);
      expect(result.body).toEqual({ message: 'Mission is in editable status' });
    });

    it('should successfully request new role', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(
        ok({
          ...missionFixture,
          status: 'Running',
        })
      );
      jest.spyOn(missionsService, 'requestNewRole').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.requestNewRole(mockReq)({
        params: { id: missionFixture.mid },
        body: requestRoleFixture,
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
      expect(missionsService.requestNewRole).toHaveBeenCalledWith(missionFixture.mid, requestRoleFixture);
    });
  });

  describe('approveRoleRequest', () => {
    const mockAccountReq = { ...mockReq, user: { id: 'user-id' } } as AuthenticatedAccountRequest;

    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.approveRoleRequest(mockAccountReq)({
        params: { id: 'non-existent', roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should successfully approve role request', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'approveRoleRequest').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.approveRoleRequest(mockAccountReq)({
        params: { id: missionFixture.mid, roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
      expect(missionsService.approveRoleRequest).toHaveBeenCalledWith(missionFixture.mid, 'role-1');
    });

    it('should return 500 when an error occurs', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'approveRoleRequest').mockResolvedValue(Errors.createError('UPDATE_MISSION_ROLE_DB_ERROR'));

      const result = await missionsController.approveRoleRequest(mockAccountReq)({
        params: { id: missionFixture.mid, roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('rejectRoleRequest', () => {
    it('should return 404 when mission does not exist', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(Errors.createError('FIND_MISSION_BY_ID_NOT_FOUND'));

      const result = await missionsController.rejectRoleRequest(mockReq)({
        params: { id: 'non-existent', roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.NOT_FOUND);
      expect(result.body).toEqual({ message: 'Not Found' });
    });

    it('should successfully reject role request', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'rejectRoleRequest').mockResolvedValue(ok(missionFixture));

      const result = await missionsController.rejectRoleRequest(mockReq)({
        params: { id: missionFixture.mid, roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(undefined);
      expect(missionsService.rejectRoleRequest).toHaveBeenCalledWith(missionFixture.mid, 'role-1');
    });

    it('should return 500 when an error occurs', async () => {
      jest.spyOn(missionsService, 'getMissionById').mockResolvedValue(ok(missionFixture));
      jest.spyOn(missionsService, 'rejectRoleRequest').mockResolvedValue(Errors.createError('UPDATE_MISSION_ROLE_DB_ERROR'));

      const result = await missionsController.rejectRoleRequest(mockReq)({
        params: { id: missionFixture.mid, roleId: 'role-1' },
      });

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });
});
