import { Test, TestingModule } from '@nestjs/testing';

import {
  skillId1Fixture,
  skillId2Fixture,
  skillId3Fixture,
  skillId4Fixture,
  skillId5Fixture,
  skillList1Fixture,
  skillList2Fixture,
  skillList3Fixture,
  skillList4Fixture,
  categoryIdFixture,
  userIdFixture,
  skillList5Fixture,
} from '@fixtures/modules/missions/missions-prefill.repository.fixtures';
import { DbService } from '@lib/global/db.service';
import { MissionsPrefillRepository } from '@modules/missions/missions-prefill.repository';

const mockDbService = {
  missionPrefill: {
    upsert: jest.fn(),
    findUnique: jest.fn(),
  },
  user: {
    findMany: jest.fn(),
  },
};

describe('MissionsPrefillRepository', () => {
  let repository: MissionsPrefillRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissionsPrefillRepository,
        {
          provide: DbService,
          useValue: mockDbService,
        },
      ],
    }).compile();

    repository = module.get<MissionsPrefillRepository>(MissionsPrefillRepository);
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('upsertMissionPrefill', () => {
    it('Should successfully create a mission prefill', async () => {
      const missionPrefillData = {
        userId: '6741bb926441991d7c625133',
      };

      const expectedMissionPrefill = {
        userId: missionPrefillData.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDbService.missionPrefill.upsert.mockResolvedValue(expectedMissionPrefill);

      const result = await repository.upsertMissionPrefill(missionPrefillData.userId);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedMissionPrefill);
        expect(mockDbService.missionPrefill.upsert).toHaveBeenCalledTimes(1);
      }
    });

    it('Should throw an error, because the creation failed', async () => {
      const error = new Error('Database error');
      mockDbService.missionPrefill.upsert.mockRejectedValue(error);

      const result = await repository.upsertMissionPrefill('invalidId');

      expect(result.isErr()).toBe(true);

      if (result.isErr()) {
        expect(result.error.code).toBe('UPSERT_MISSION_PREFILL_DB_ERROR');
      }
    });
  });

  describe('findMissionPrefillByUserId', () => {
    it('Should successfully find a mission prefill', async () => {
      const missionPrefillData = {
        userId: userIdFixture,
      };

      const expectedMissionPrefill = {
        userId: missionPrefillData.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDbService.missionPrefill.upsert.mockResolvedValue(expectedMissionPrefill);
      await repository.upsertMissionPrefill(missionPrefillData.userId);
      mockDbService.missionPrefill.findUnique.mockResolvedValue(expectedMissionPrefill);

      const get = await repository.findMissionPrefillByUserId(missionPrefillData.userId);

      expect(get.isOk() && get.value).toEqual(expectedMissionPrefill);
    });
  });

  describe('getMostCommonSkillsForSpecialization', () => {
    it('Should return lists of skills, sorted by count and divided to "required" and "preferred"', async () => {
      const expectedResult = [
        {
          talentProfile: {
            talentSkills: {
              mainTalentSkills: [...skillList1Fixture, ...skillList2Fixture, ...skillList3Fixture, ...skillList4Fixture, ...skillList5Fixture],
            },
          },
        },
      ];

      const expectedSkills = {
        requiredSkills: [skillId1Fixture, skillId2Fixture, skillId3Fixture],
        preferredSkills: [skillId4Fixture, skillId5Fixture],
      };

      mockDbService.user.findMany.mockResolvedValue(expectedResult);

      const getResult = await repository.getMostCommonSkillsForSpecialization(categoryIdFixture);

      expect(getResult.isOk()).toBe(true);

      if (getResult.isOk()) {
        expect(getResult.value).toEqual(expectedSkills);
      }
    });
  });
});
