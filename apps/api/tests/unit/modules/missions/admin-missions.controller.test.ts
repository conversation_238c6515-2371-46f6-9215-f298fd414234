import { HttpStatus, Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { ConfigService } from '@config/config.service';
import { talentIndustriesFixture, tfsOwnersFixture } from '@fixtures/modules/missions/admin-missions.service.fixtures';
import { SentryService } from '@lib/global/sentry.service';
import { AdminMissionsController } from '@modules/missions/admin-missions.controller';
import { AdminMissionsService } from '@modules/missions/admin-missions.service';

import { Errors } from '@lib/errors';
import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

describe('AdminMissionsController', () => {
  let adminMissionsController: AdminMissionsController;
  let adminMissionsService: AdminMissionsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminMissionsController,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: AdminMissionsService,
          useValue: {
            getAllMissionIndustries: jest.fn(),
            getTfsOwners: jest.fn(),
          },
        },
        {
          provide: SentryService,
          useValue: {
            logAndCaptureError: jest.fn(),
          },
        },
      ],
    })
      .overrideGuard(AdminJwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    adminMissionsController = module.get<AdminMissionsController>(AdminMissionsController);
    adminMissionsService = module.get<AdminMissionsService>(AdminMissionsService);

    jest.spyOn(Logger, 'error').mockImplementation(jest.fn());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getMissionIndustries', () => {
    it('should return ok & mission industries as expected', async () => {
      const missionIndustries = talentIndustriesFixture.map((t) => ({ id: t.id, name: t.name }));

      jest.spyOn(adminMissionsService, 'getAllMissionIndustries').mockResolvedValue(ok(missionIndustries));

      const result = await adminMissionsController.getMissionIndustries()();

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(missionIndustries);
    });

    it('should return internal server error, since the db call failed', async () => {
      jest.spyOn(adminMissionsService, 'getAllMissionIndustries').mockResolvedValue(Errors.createError('FIND_ALL_TALENT_INDUSTRIES_DB_ERROR'));

      const result = await adminMissionsController.getMissionIndustries()();

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });

  describe('getTfsOwners', () => {
    it('should return ok & tfs owners as expected', async () => {
      const tfsOwners = tfsOwnersFixture.map((t) => ({
        id: t.id,
        firstName: t.firstName!,
        lastName: t.lastName!,
        profilePictureURL: t.pictureURL,
      }));

      jest.spyOn(adminMissionsService, 'getTfsOwners').mockResolvedValue(ok(tfsOwners));

      const result = await adminMissionsController.getTfsOwners()();

      expect(result.status).toBe(HttpStatus.OK);
      expect(result.body).toEqual(tfsOwners);
    });

    it('should return internal server error, since the db call failed', async () => {
      jest.spyOn(adminMissionsService, 'getTfsOwners').mockResolvedValue(Errors.createError('FIND_USERS_BY_EMAILS_DB_ERROR'));

      const result = await adminMissionsController.getTfsOwners()();

      expect(result.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });
});
