import { Prisma } from '@a_team/prisma';
import { Test, TestingModule } from '@nestjs/testing';

import { missionFixture, roleCategoriesFixture, talentSkillsFixture } from '@fixtures/modules/missions/common.fixtures';
import { missionIdFixture, roleIdFixture } from '@fixtures/modules/missions/missions.repository.fixtures';
import { DbService } from '@lib/global/db.service';
import { MissionsRepository } from '@modules/missions/missions.repository';

const mockDbService = {
  mission: {
    create: jest.fn(),
    update: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  roleCategory: {
    findMany: jest.fn(),
  },
  talentCategory: {
    findMany: jest.fn(),
  },
};

describe('MissionsRepository', () => {
  let repository: MissionsRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MissionsRepository,
        {
          provide: DbService,
          useValue: mockDbService,
        },
      ],
    }).compile();

    repository = module.get<MissionsRepository>(MissionsRepository);
  });

  beforeEach(() => {
    jest.resetAllMocks();
  });

  describe('findMissionById', () => {
    it('should successfully find a mission by id', async () => {
      mockDbService.mission.findUnique.mockResolvedValue(missionFixture);

      const result = await repository.findMissionById(missionIdFixture);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(missionFixture);
        expect(mockDbService.mission.findUnique).toHaveBeenCalledWith({
          where: { mid: missionIdFixture },
        });
      }
    });

    it('should return null when mission is not found', async () => {
      mockDbService.mission.findUnique.mockResolvedValue(null);

      const result = await repository.findMissionById(missionIdFixture);

      expect(result.isErr()).toBe(true);
    });
  });

  describe('findAllRoleCategories', () => {
    it('should successfully return all role categories', async () => {
      mockDbService.roleCategory.findMany.mockResolvedValue(roleCategoriesFixture);

      const result = await repository.findAllRoleCategories();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(roleCategoriesFixture);
        expect(mockDbService.roleCategory.findMany).toHaveBeenCalledWith({
          select: { id: true, title: true },
        });
      }
    });
  });

  describe('findAllTalentSkills', () => {
    it('should successfully return all talent skills', async () => {
      mockDbService.talentCategory.findMany.mockResolvedValue(talentSkillsFixture);

      const result = await repository.findAllTalentSkills();

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(talentSkillsFixture);
        expect(mockDbService.talentCategory.findMany).toHaveBeenCalledWith({
          where: {
            nodeType: 'skill',
            parentTalentCategoryIds: {
              isEmpty: false,
            },
          },
          select: { id: true, name: true },
        });
      }
    });
  });

  describe('createMission', () => {
    it('Should successfully create a mission because all of the data is present', async () => {
      const missionData: Prisma.MissionCreateInput = {
        mid: missionIdFixture,
        title: 'Test title mission',
        description: 'Test description',
        status: 'Created',
      };

      const expectedMission = {
        ...missionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDbService.mission.create.mockResolvedValue(expectedMission);

      const result = await repository.createMission(missionData);

      expect(result.isOk()).toBe(true);
      expect(result._unsafeUnwrap()).toEqual(expectedMission);
      expect(mockDbService.mission.create).toHaveBeenCalledTimes(1);
    });

    it('Should throw an error, because the creation failed', async () => {
      const missionData: Prisma.MissionCreateInput = {
        mid: missionIdFixture,
        title: 'Test title mission',
        description: 'Test description',
        status: 'Created',
      };

      const error = new Error('Database error');
      mockDbService.mission.create.mockRejectedValue(error);

      const result = await repository.createMission(missionData);

      expect(result.isErr()).toBe(true);
      expect(result._unsafeUnwrapErr().code).toEqual('CREATE_MISSION_DB_ERROR');
    });
  });

  describe('updateMission', () => {
    it('should successfully update a mission', async () => {
      const updateData: Prisma.MissionUpdateInput = {
        title: 'Updated Title',
        status: 'Published',
      };

      const expectedMission = {
        mid: missionIdFixture,
        ...updateData,
        updatedAt: new Date(),
      };

      mockDbService.mission.update.mockResolvedValue(expectedMission);

      const result = await repository.updateMission(missionIdFixture, updateData);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedMission);
        expect(mockDbService.mission.update).toHaveBeenCalledTimes(1);
      }
    });

    it('should return an error when update fails', async () => {
      const updateData: Prisma.MissionUpdateInput = {
        title: 'Updated Title',
      };

      const error = new Error('Database update error');
      mockDbService.mission.update.mockRejectedValue(error);

      const result = await repository.updateMission(missionIdFixture, updateData);

      expect(result.isErr()).toBe(true);
    });
  });

  describe('updateMissionRole', () => {
    const missionId = missionIdFixture;
    const roleId = roleIdFixture;

    it('should successfully update a mission role', async () => {
      const roleUpdateData: Prisma.MissionRoleUpdateInput = {
        status: 'Canceled',
      };

      const expectedMission = {
        mid: missionId,
        title: 'Test Mission',
        roles: [
          {
            id: roleId,
            status: 'Canceled',
          },
        ],
      };

      mockDbService.mission.update.mockResolvedValue(expectedMission);

      const result = await repository.updateMissionRole(missionId, roleId, roleUpdateData);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedMission);
        expect(mockDbService.mission.update).toHaveBeenCalledTimes(1);
        expect(mockDbService.mission.update).toHaveBeenCalledWith({
          where: { mid: missionId },
          data: {
            roles: {
              updateMany: {
                where: { id: roleId },
                data: roleUpdateData,
              },
            },
          },
        });
      }
    });

    it('should return an error when updating mission role fails', async () => {
      const roleUpdateData: Prisma.MissionRoleUpdateInput = {
        status: 'Canceled',
      };

      const error = new Error('Database update error');
      mockDbService.mission.update.mockRejectedValue(error);

      const result = await repository.updateMissionRole(missionId, roleId, roleUpdateData);

      expect(result.isErr()).toBe(true);
    });
  });

  describe('addRoleToMission', () => {
    it('should successfully add a role to a mission', async () => {
      const missionId = missionIdFixture;
      const roleData: Prisma.MissionRoleCreateInput = {
        id: roleIdFixture,
        headline: 'New Role',
        categoryId: 'category-123',
        status: 'Open',
      };

      const expectedMission = {
        mid: missionId,
        roles: [roleData],
      };

      mockDbService.mission.update.mockResolvedValue(expectedMission);

      const result = await repository.addRoleToMission(missionId, roleData);

      expect(result.isOk()).toBe(true);

      if (result.isOk()) {
        expect(result.value).toEqual(expectedMission);
        expect(mockDbService.mission.update).toHaveBeenCalledWith({
          where: { mid: missionId },
          data: {
            roles: {
              push: roleData,
            },
          },
        });
      }
    });
  });
});
