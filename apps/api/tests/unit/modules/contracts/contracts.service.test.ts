import { Contract, ContractParty, ContractStatus, ContractType, User } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ok } from 'neverthrow';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonBillingAccountsRepository } from '@common/billing-accounts/billing-accounts.repository';
import { CommonCompaniesRepository } from '@common/companies/companies.repository';
import { CommonHubspotService } from '@common/hubspot/hubspot.service';
import { CommonMissionsRepository, MissionWithAccountInfo } from '@common/missions/missions.repository';
import { CommonSlackService } from '@common/slack/slack.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { masterServicesPandadocWebhookDataFixture } from '@fixtures/modules/contracts/contracts.fixtures';
import { PandadocApiClient } from '@lib/clients/pandadoc-api.client';
import { DbService } from '@lib/global/db.service';
import { SentryService } from '@lib/global/sentry.service';
import { ContractsRepository } from '@modules/contracts/contracts.repository';
import { ContractsService } from '@modules/contracts/contracts.service';

import { Errors } from '@lib/errors';
import { JwtGuard } from '@lib/guards/jwt.guard';

describe('ContractsService', () => {
  let contractsService: ContractsService;
  let contractsRepository: ContractsRepository;
  let hubspotService: CommonHubspotService;
  let pandadocApiClient: PandadocApiClient;
  let usersRepository: CommonUsersRepository;
  let missionsRepository: CommonMissionsRepository;
  let billingAccountsRepository: CommonBillingAccountsRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        ContractsService,
        {
          provide: CommonHubspotService,
          useValue: {
            getDealById: jest.fn(),
            getCompanyById: jest.fn(),
            updateDealById: jest.fn(),
          },
        },
        {
          provide: ContractsRepository,
          useValue: {
            findById: jest.fn(),
            findMany: jest.fn(),
            findByPandadocId: jest.fn(),
            create: jest.fn(),
            checkIfDealHasBillingForm: jest.fn(),
            findContractByAccountAndType: jest.fn(),
          },
        },
        {
          provide: CommonUsersRepository,
          useValue: {
            findUserById: jest.fn(),
            findUserByIdNeverthrow: jest.fn(),
            updateUserById: jest.fn(),
          },
        },
        {
          provide: CommonMissionsRepository,
          useValue: {
            findMissionById: jest.fn(),
            findMissionWithAccountInfoById: jest.fn(),
          },
        },
        {
          provide: CommonBillingAccountsRepository,
          useValue: {
            getBillingAccountByAccountId: jest.fn(),
          },
        },
        ConfigService,
        CommonSlackService,
        CommonHubspotService,
        CommonCompaniesRepository,
        CommonAccountsRepository,
        PandadocApiClient,
        DbService,
        SentryService,
      ],
    })
      .overrideGuard(JwtGuard)
      .useValue({ canActivate: () => true })
      .compile();

    contractsService = module.get(ContractsService);
    contractsRepository = module.get(ContractsRepository);
    hubspotService = module.get(CommonHubspotService);
    pandadocApiClient = module.get(PandadocApiClient);
    usersRepository = module.get(CommonUsersRepository);
    missionsRepository = module.get(CommonMissionsRepository);
    billingAccountsRepository = module.get(CommonBillingAccountsRepository);
  });

  beforeAll(() => {
    jest.spyOn(Logger, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('getContracts', () => {
    it('Should get contracts from the db', async () => {
      jest.spyOn(usersRepository, 'findUserById').mockResolvedValue(null);
      const findManySpy = jest.spyOn(contractsRepository, 'findMany').mockResolvedValue({ contracts: [], count: 0 });

      await contractsService.getContracts({ page: 0, pageSize: 10 }, 'test', '1');

      expect(findManySpy).toHaveBeenCalledWith('test', 0, 10);
    });
  });

  describe('handlePandadocWebhook', () => {
    it('Should throw error if it is not possible to find account id', async () => {
      jest.spyOn(contractsRepository, 'findByPandadocId').mockResolvedValue(null);
      jest.spyOn(contractsService as any, 'getAccountFromHubspotCompanyId').mockResolvedValue(null);

      await expect(contractsService.handlePandadocWebhook(masterServicesPandadocWebhookDataFixture)).rejects.toThrow(Error);
    });

    it('Should handle webhook and create a contract', async () => {
      jest.spyOn(contractsRepository, 'findByPandadocId').mockResolvedValue(null);
      jest.spyOn(contractsService as any, 'getAccountFromHubspotCompanyId').mockResolvedValue('606334c3e1840a0012601d62');
      const createSpy = jest.spyOn(contractsRepository, 'create').mockResolvedValue({ sid: '606334c3e1840a0012601d64' } as Contract);
      const processPandadocDocumentRecipientsSpy = jest.spyOn(contractsService as any, 'processPandadocDocumentRecipients').mockImplementation();

      await contractsService.handlePandadocWebhook(masterServicesPandadocWebhookDataFixture);

      expect(createSpy).toHaveBeenCalled();
      expect(processPandadocDocumentRecipientsSpy).toHaveBeenCalled();
    });
  });

  describe('getContract', () => {
    const mockContract: Contract = {
      sid: '123',
      documentTitle: 'Test Contract',
      type: ContractType.ServiceOrder,
      status: ContractStatus.Created,
      downloadURL: 'http://test.com/contract.pdf',
      parties: [],
      pandadocMetadata: {
        id: 'panda-123',
      },
    } as any as Contract;

    it('Should throw error if contract not found', async () => {
      jest.spyOn(contractsRepository, 'findById').mockResolvedValue(null);

      await expect(contractsService.getContract('123', 'user-123')).rejects.toThrow('No contract with id 123');
    });

    it('Should return contract without signing URL if parties exist', async () => {
      const contractWithParties = {
        ...mockContract,
        parties: [{ user: '1' } as any as ContractParty],
      };

      jest.spyOn(contractsRepository, 'findById').mockResolvedValue(contractWithParties);
      jest.spyOn(contractsService as any, 'getContractPdfUrl').mockReturnValue('http://test.com/contract.pdf');

      const result = await contractsService.getContract('123', 'user-123');

      expect(result).toEqual({
        id: '123',
        documentTitle: 'Test Contract',
        type: ContractType.ServiceOrder,
        downloadURL: 'http://test.com/contract.pdf',
        signingURL: undefined,
      });
    });
  });

  describe('handleHubspotDealWebhook', () => {
    it('Should update deal if hubspot info is correct', async () => {
      jest.spyOn(usersRepository, 'findUserById').mockResolvedValue({} as User);
      jest.spyOn(missionsRepository, 'findMissionWithAccountInfoById').mockResolvedValue(
        ok({
          accountId: 'testaccountid',
          roles: [{ id: 'testroleid', user: 'testuser', availability: { date: new Date('2025-01-25') } }],
        } as MissionWithAccountInfo)
      );
      jest.spyOn(billingAccountsRepository, 'getBillingAccountByAccountId').mockResolvedValue(null);
      jest.spyOn(contractsRepository, 'findContractByAccountAndType').mockResolvedValue(null);
      const updateDealByIdSpy = jest.spyOn(hubspotService, 'updateDealById').mockImplementation();

      await contractsService.handleHubspotDealWebhook({
        hasBillingForm: true,
        id: 123,
        roleId: 'testroleid',
        missionId: 'testmissionid',
      });

      expect(updateDealByIdSpy).toHaveBeenCalledWith(
        '123',
        expect.objectContaining({
          has_billing_form: false,
          role_start_date: '2025-01-25',
        })
      );
    });

    it('Should return null if hubspot update is not successful', async () => {
      jest.spyOn(usersRepository, 'findUserById').mockResolvedValue({} as User);
      jest
        .spyOn(missionsRepository, 'findMissionWithAccountInfoById')
        .mockResolvedValue(ok({ accountId: 'testaccountid', roles: [{ id: 'testroleid', user: 'testuser' }] } as MissionWithAccountInfo));
      jest.spyOn(billingAccountsRepository, 'getBillingAccountByAccountId').mockResolvedValue(null);
      jest.spyOn(contractsRepository, 'findContractByAccountAndType').mockResolvedValue(null);
      jest.spyOn(hubspotService, 'updateDealById').mockRejectedValue(new Error());
      jest.spyOn(Logger, 'error').mockImplementation(jest.fn());

      await expect(
        contractsService.handleHubspotDealWebhook({
          hasBillingForm: true,
          id: 123,
          roleId: 'testroleid',
          missionId: 'testmissionid',
        })
      ).rejects.toThrow(Error);
    });
  });

  describe('getPandadocPdf', () => {
    it('Should call api and return buffered data', async () => {
      const apiSpy = jest.spyOn((pandadocApiClient as any).client, 'get').mockResolvedValue({ data: 'data' });

      const result = await contractsService.getPandadocPdf('testid');

      expect(result.toString()).toBe('data');
      expect(apiSpy).toHaveBeenCalled();
    });
  });

  describe('agreeToTermsOfService', () => {
    it('Should agree to terms of service', async () => {
      jest.spyOn(usersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok({ status: 'Active' } as User));
      const updateUserByIdSpy = jest.spyOn(usersRepository, 'updateUserById').mockResolvedValue(ok({} as User));

      await contractsService.agreeToTermsOfService('testid', '127.0.0.1');

      expect(updateUserByIdSpy).toHaveBeenCalledWith('testid', {
        acceptTOS: {
          ip: '127.0.0.1',
          signedAt: expect.any(Date),
          version: 'v2',
        },
        acceptTOSHistory: [],
      });
    });

    it('Should return error if user is not active', async () => {
      jest.spyOn(usersRepository, 'findUserByIdNeverthrow').mockResolvedValue(ok({ status: 'Rejected' } as User));

      const result = await contractsService.agreeToTermsOfService('testid', '127.0.0.1');

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('AGREE_TO_TERMS_OF_SERVICE_USER_NOT_ACTIVE');
    });

    it('Should return error if user not found', async () => {
      jest.spyOn(usersRepository, 'findUserByIdNeverthrow').mockResolvedValue(Errors.createError('FIND_USER_BY_ID_NOT_FOUND'));

      const result = await contractsService.agreeToTermsOfService('testid', '127.0.0.1');

      expect(result.isErr()).toBe(true);
      expect(result.isErr() && result.error.code).toBe('FIND_USER_BY_ID_NOT_FOUND');
    });
  });
});
