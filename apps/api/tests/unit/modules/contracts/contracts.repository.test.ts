import { ContractStatus, ContractType, Prisma } from '@a_team/prisma';
import { Test, TestingModule } from '@nestjs/testing';

import { DbService } from '@lib/global/db.service';
import { ContractsRepository } from '@modules/contracts/contracts.repository';

const mockDbService = {
  contract: {
    create: jest.fn(),
    update: jest.fn(),
    findUnique: jest.fn(),
    findMany: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
};

const contractDataFixture: Prisma.ContractCreateInput = {
  type: ContractType.ServiceOrder,
  status: ContractStatus.Created,
  downloadURL: 'https://cdn.a.team/contracts/standard/v2/ATeams-Builder-Agreement.pdf',
};

describe('ContractsRepository', () => {
  let repository: ContractsRepository;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContractsRepository,
        {
          provide: DbService,
          useValue: mockDbService,
        },
      ],
    }).compile();

    repository = module.get<ContractsRepository>(ContractsRepository);

    jest.clearAllMocks();
  });

  describe('create', () => {
    it('Should successfully create a contract because all mandatory fields are present', async () => {
      const expectedContract = {
        ...contractDataFixture,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockDbService.contract.create.mockResolvedValue(expectedContract);

      const result = await repository.create(contractDataFixture);

      expect(result).toEqual(expectedContract);
      expect(mockDbService.contract.create).toHaveBeenCalledTimes(1);
    });

    it('Should throw an error, because the status field is not present', async () => {
      const contractData: Prisma.ContractCreateInput = {
        type: ContractType.ServiceOrder,
      } as Prisma.ContractCreateInput;

      const error = new Error('Database error');
      mockDbService.contract.create.mockRejectedValue(error);

      await expect(repository.create(contractData)).rejects.toThrow(error);
    });
  });
});
