{"compilerOptions": {"module": "commonjs", "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2021", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "strict": true, "skipLibCheck": true, "esModuleInterop": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "moduleResolution": "node", "isolatedModules": true, "types": ["jest", "node"], "paths": {"@config/*": ["src/config/*"], "@lib/*": ["src/lib/*"], "@modules/*": ["src/modules/*"], "@common/*": ["src/common/*"], "@fixtures/*": ["tests/fixtures/*"], "@tests/*": ["tests/*"]}}, "exclude": ["node_modules", "dist"]}