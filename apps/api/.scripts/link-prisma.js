const fs = require('fs');
const path = require('path');

const getLinuxDistribution = () => {
  try {
    const osRelease = fs.readFileSync('/etc/os-release', 'utf-8');
    const match = osRelease.match(/^ID=(.+)$/m);
    return match ? match[1].replace(/"/g, '') : 'unknown';
  } catch (error) {
    console.error('Failed to detect Linux distribution:', error.message);
    return 'unknown';
  }
};

const getPlatformPackage = () => {
  switch (process.platform) {
    case 'darwin':
      return '@a_team/prisma-macos';
    case 'win32':
      return '@a_team/prisma-win';
    default:
      const linuxDistro = getLinuxDistribution();

      if (linuxDistro === 'debian') {
        return '@a_team/prisma-linux-debian';
      }

      return '@a_team/prisma-linux';
  }
};

const platformPackage = getPlatformPackage();
const nodeModulesPath = path.join(__dirname, '..', 'node_modules');
const sourcePath = path.join(nodeModulesPath, platformPackage);
const targetPath = path.join(nodeModulesPath, '@a_team/prisma');

if (fs.existsSync(sourcePath)) {
  if (fs.existsSync(targetPath)) {
    fs.rmSync(targetPath, { recursive: true, force: true });
  }

  fs.renameSync(sourcePath, targetPath);
  console.log(`Renamed ${platformPackage} to @a_team/prisma`);
} else {
  console.error(`Platform-specific package ${platformPackage} not found`);
  process.exit(1);
}
