{"name": "api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,tests}/**/*.ts\"", "test": "jest", "postinstall": "node .scripts/link-prisma.js"}, "optionalDependencies": {"@a_team/prisma-linux": "npm:@a_team/prisma@3.13.2-linux", "@a_team/prisma-linux-debian": "npm:@a_team/prisma@3.13.2-linux-debian", "@a_team/prisma-macos": "npm:@a_team/prisma@3.13.2-macos", "@a_team/prisma-win": "npm:@a_team/prisma@3.13.2-win"}, "dependencies": {"@a_team/logger": "0.1.0", "@ai-sdk/openai": "1.0.4", "@hubspot/api-client": "12.0.1", "@nestjs/cache-manager": "2.3.0", "@nestjs/common": "10.0.0", "@nestjs/config": "3.3.0", "@nestjs/core": "10.0.0", "@nestjs/platform-express": "10.0.0", "@nestjs/swagger": "8.0.1", "@opentelemetry/api": "1.9.0", "@opentelemetry/auto-instrumentations-node": "0.55.0", "@opentelemetry/exporter-metrics-otlp-proto": "0.57.0", "@opentelemetry/exporter-trace-otlp-proto": "0.57.0", "@opentelemetry/resources": "1.30.0", "@opentelemetry/sdk-metrics": "1.30.0", "@opentelemetry/sdk-node": "0.57.0", "@opentelemetry/semantic-conventions": "1.28.0", "@packages/contracts": "workspace:*", "@sendgrid/mail": "8.1.4", "@sentry/node": "8.50.0", "@slack/web-api": "7.7.0", "@ts-rest/nest": "3.51.0", "@ts-rest/open-api": "3.51.0", "@uploadcare/rest-client": "6.14.3", "ai": "4.0.3", "axios": "1.7.7", "cache-manager": "5.7.6", "date-fns": "4.1.0", "date-fns-tz": "3.2.0", "express": "4.21.1", "google-auth-library": "9.15.1", "googleapis": "148.0.0", "jsonwebtoken": "9.0.2", "neverthrow": "8.2.0", "reflect-metadata": "0.1.12", "rxjs": "7.8.1", "zod": "3.23.8"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.2.2", "@nestjs/testing": "^10.4.6", "@packages/eslint-config": "workspace:*", "@swc/jest": "^0.2.37", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.17.0", "@types/supertest": "^6.0.2", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.2", "supertest": "^7.0.0", "timezone-mock": "1.3.6", "ts-jest": "^29.2.5", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0"}}