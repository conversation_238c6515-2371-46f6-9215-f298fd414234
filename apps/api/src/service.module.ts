import { CacheModule } from '@nestjs/cache-manager';
import { Module } from '@nestjs/common';

import { ServiceController } from './service.controller';
import { CommonClientReviewsModule } from '@common/client-reviews/client-reviews.module';
import { ConfigModule } from '@config/config.module';
import { GlobalModule } from '@lib/global/global.module';
import { AccountsModule } from '@modules/accounts/accounts.module';
import { AuthModule } from '@modules/auth/auth.module';
import { ContractsModule } from '@modules/contracts/contracts.module';
import { InterviewSchedulingModule } from '@modules/interview-scheduling/interview-scheduling.module';
import { MissionsModule } from '@modules/missions/missions.module';
import { ProposalsModule } from '@modules/proposals/proposals.module';

import { ServiceLogger } from './logger';

const getAppModules = () => [
  ConfigModule,
  MissionsModule,
  ProposalsModule,
  AuthModule,
  AccountsModule,
  CommonClientReviewsModule,
  ContractsModule,
  InterviewSchedulingModule,
];

@Module({
  imports: [GlobalModule, CacheModule.register({ isGlobal: true }), ...getAppModules()],
  controllers: [ServiceController],
  providers: [ServiceLogger],
})
export class ServiceModule {}
