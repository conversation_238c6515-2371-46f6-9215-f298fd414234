import { PrismaInstrumentation } from '@a_team/prisma';
import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { SwaggerModule } from '@nestjs/swagger';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPMetricExporter } from '@opentelemetry/exporter-metrics-otlp-proto';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-proto';
import { Resource } from '@opentelemetry/resources';
import { PeriodicExportingMetricReader } from '@opentelemetry/sdk-metrics';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { apiContract } from '@packages/contracts';
import { generateOpenApi } from '@ts-rest/open-api';

import { ServiceModule } from './service.module';
import { ConfigService } from '@config/config.service';

import { ServiceLogger } from './logger';

const setupTelemetry = () => {
  const sdk = new NodeSDK({
    resource: new Resource({
      [ATTR_SERVICE_NAME]: 'core-platform-api',
    }),
    traceExporter: new OTLPTraceExporter(),
    metricReader: new PeriodicExportingMetricReader({
      exporter: new OTLPMetricExporter(),
    }),
    instrumentations: [getNodeAutoInstrumentations(), new PrismaInstrumentation()],
  });

  return sdk;
};

const bootstrap = async () => {
  const app = await NestFactory.create(ServiceModule, {
    bufferLogs: true,
  });

  const configService = app.get<ConfigService>(ConfigService);

  const PORT = configService.get('PORT');
  const ENV = configService.get('ENV');

  const WEB_BASE_URL = configService.get('WEB_BASE_URL');
  const ADMIN_WEB_BASE_URL = configService.get('ADMIN_WEB_BASE_URL');

  app.enableCors({ origin: [WEB_BASE_URL, ADMIN_WEB_BASE_URL] });

  if (ENV !== 'local') {
    const sdk = setupTelemetry();
    sdk.start();
  }

  if (ENV === 'local') {
    const config = generateOpenApi(
      apiContract,
      {
        info: {
          title: 'Core platform api service',
          version: '1.0.0',
        },
      },
      {
        setOperationId: true,
        jsonQuery: true,
      }
    );

    SwaggerModule.setup('docs', app, config);
  }

  if (ENV !== 'local') {
    app.useLogger(app.get(ServiceLogger));
  }

  await app.listen(PORT);
  Logger.log(`Service started at ${PORT}!`);
};

// eslint-disable-next-line no-console
bootstrap().catch((e) => console.error(e));
