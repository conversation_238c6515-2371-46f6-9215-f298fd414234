import { Controller, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON><PERSON><PERSON>, TsRestHandler } from '@ts-rest/nest';

import { AuthService } from './auth.service';
import { SentryService } from '@lib/global/sentry.service';

import { AuthenticatedRequest, JwtGuard } from '@lib/guards/jwt.guard';

@Controller()
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly sentryService: SentryService
  ) {}

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.auth.getMe)
  getMe(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.auth.getMe, async () => {
      const result = await this.authService.getMe(req.user.id);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/auth/auth.controller/getMe] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'User not found' },
            };
          case 'FIND_ACTIVE_USER_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      const user = result.value;

      return {
        status: HttpStatus.OK,
        body: user,
      };
    });
  }

  @TsRestHandler(apiContract.auth.signInWithPassword)
  signInWithPassword(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.auth.signInWithPassword, async ({ body }) => {
      const result = await this.authService.signInWithPassword(body);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/auth/auth.controller/signInWithPassword] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'User not found' },
            };
          case 'FIND_ACTIVE_USER_BY_ID_DB_ERROR':
          case 'SIGN_IN_WITH_PASSWORD_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
