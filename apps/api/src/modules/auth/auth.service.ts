import { User } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { AuthenticatedUser, SignInWithPasswordSchemaDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CommonUsersRepository } from '@common/users/users.repository';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';

@Injectable()
export class AuthService {
  private transformToAuthenticatedUser(dbUser: User): AuthenticatedUser {
    const { id, email, firstName, lastName, isAdmin } = dbUser;

    return {
      id,
      email,
      isAdmin: isAdmin ?? false,
      firstName,
      lastName,
      fullName: firstName ? `${firstName}${lastName ? ' ' + lastName : ''}` : null,
    };
  }

  constructor(
    private readonly userRepository: CommonUsersRepository,
    private readonly platformClient: PlatformServiceClient
  ) {}

  getMe = async (userId: string) => {
    const dbUserResult = await this.userRepository.findActiveUserById(userId);

    if (dbUserResult.isErr()) {
      return dbUserResult;
    }

    return ok(this.transformToAuthenticatedUser(dbUserResult.value));
  };

  signInWithPassword = async (credentials: SignInWithPasswordSchemaDto) => {
    const { email: signInEmail, password } = credentials;

    const signInResponseResult = await this.platformClient.signInWithPassword(signInEmail, password);

    if (signInResponseResult.isErr()) {
      return signInResponseResult;
    }

    const signInResponse = signInResponseResult.value;
    const { userId, token, refreshToken } = signInResponse;

    const dbUserResult = await this.userRepository.findActiveUserById(userId);

    if (dbUserResult.isErr()) {
      return dbUserResult;
    }

    return ok({
      token,
      refreshToken,
      user: this.transformToAuthenticatedUser(dbUserResult.value),
    });
  };
}
