import { Module } from '@nestjs/common';

import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientsModule } from '@lib/clients/clients.module';

@Module({
  imports: [CommonUsersModule, ClientsModule],
  controllers: [AuthController],
  providers: [AuthService],
})
export class AuthModule {}
