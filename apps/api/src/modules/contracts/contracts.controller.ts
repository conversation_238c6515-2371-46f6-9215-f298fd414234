import { Controller, Get, HttpStatus, Logger, Param, Req, Request, Response, UnauthorizedException, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON>and<PERSON>, TsRestHandler } from '@ts-rest/nest';
import { Response as Res } from 'express';

import { ContractsService } from './contracts.service';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { SentryService } from '@lib/global/sentry.service';

import { AccountJwtGuard, AuthenticatedAccountRequest } from '@lib/guards/account-jwt.guard';
import { ApiKeyAuthGuard } from '@lib/guards/api-key.guard';
import { JwtGuard } from '@lib/guards/jwt.guard';
import { PandadocWebhookGuard } from '@lib/guards/pandadoc-webhook.guard';

@Controller()
export class ContractsController {
  constructor(
    private readonly contractsService: ContractsService,
    private readonly rbacService: CommonRBACService,
    private readonly sentryService: SentryService
  ) {}

  @UseGuards(PandadocWebhookGuard)
  @TsRestHandler(apiContract.contracts.pandadocWebhookHandler)
  createContractWebhook() {
    return tsRestHandler(apiContract.contracts.pandadocWebhookHandler, async ({ body }) => {
      const contract = await this.contractsService.handlePandadocWebhook(body[0]);

      return { status: HttpStatus.CREATED, body: { id: contract.sid } };
    });
  }

  @UseGuards(AccountJwtGuard)
  @TsRestHandler(apiContract.contracts.getContracts)
  getContracts(@Request() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.getContracts, async ({ query }) => {
      const data = await this.contractsService.getContracts(query, req.account.id, req.user.id);

      return { status: HttpStatus.OK, body: data };
    });
  }

  @UseGuards(AccountJwtGuard)
  @TsRestHandler(apiContract.contracts.createCustomContracts)
  createCustomContracts(@Request() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.createCustomContracts, async ({ body }) => {
      await this.contractsService.createCustomContracts(req.account.id, body);

      return { status: HttpStatus.CREATED, body: { message: 'Created' } };
    });
  }

  @UseGuards(AccountJwtGuard)
  @TsRestHandler(apiContract.contracts.getContract)
  getContract(@Req() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.getContract, async ({ params }) => {
      const contractId = params.id;

      const result = await this.rbacService.checkClientPermission(req.user.id, req.account.id, `contract:${contractId}`, 'read');

      if (result.isErr()) {
        // TODO: Remove throw after contracts are migrated to neverthrow
        throw new UnauthorizedException('Unauthorized');
      }

      const contract = await this.contractsService.getContract(contractId, req.user.id);

      return { status: HttpStatus.OK, body: contract };
    });
  }

  @Get(apiContract.contracts.getPandadocPdf.path)
  async getPandadocPdf(@Response() res: Res, @Param('pandadocId') pandadocId: string) {
    const data = await this.contractsService.getPandadocPdf(pandadocId);

    res.setHeader('Content-Disposition', 'inline; filename="contract.pdf"');
    res.setHeader('Content-Type', 'application/pdf');

    return res.send(data);
  }

  @UseGuards(ApiKeyAuthGuard)
  @TsRestHandler(apiContract.contracts.hubspotDealWebhook)
  hubspotDealWebhook() {
    return tsRestHandler(apiContract.contracts.hubspotDealWebhook, async ({ body }) => {
      try {
        const response = await this.contractsService.handleHubspotDealWebhook(body);

        if (!response) {
          return { status: HttpStatus.NOT_FOUND, body: { message: 'Mission or role not found' } };
        }

        return { status: HttpStatus.OK, body: response };
      } catch (error) {
        Logger.error(`[modules/contracts/contracts.controller/hubspotDealWebhook] - Error handling webhook`, error);
        return { status: HttpStatus.BAD_REQUEST, body: { message: 'Error handling webhook' } };
      }
    });
  }

  @UseGuards(AccountJwtGuard)
  @TsRestHandler(apiContract.contracts.updateContract)
  updateContract(@Req() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.updateContract, async ({ params, body }) => {
      const contractId = params.id;

      const result = await this.rbacService.checkClientPermission(req.user.id, req.account.id, `contract:${contractId}`, 'write');

      if (result.isErr()) {
        // TODO: Remove throw after contracts are migrated to neverthrow
        throw new UnauthorizedException('Unauthorized');
      }

      const contract = await this.contractsService.updateContract(contractId, body);

      if (!contract) {
        return { status: HttpStatus.NOT_FOUND, body: { message: 'Contract not found' } };
      }

      return { status: HttpStatus.OK, body: contract };
    });
  }

  @UseGuards(AccountJwtGuard)
  @TsRestHandler(apiContract.contracts.deleteContract)
  deleteContract(@Req() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.deleteContract, async ({ params }) => {
      const contractId = params.id;

      const result = await this.rbacService.checkClientPermission(req.user.id, req.account.id, `contract:${contractId}`, 'write');

      if (result.isErr()) {
        // TODO: Remove throw after contracts are migrated to neverthrow
        throw new UnauthorizedException('Unauthorized');
      }

      const contract = await this.contractsService.deleteContract(contractId);

      return { status: HttpStatus.OK, body: { id: contract.sid } };
    });
  }

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.contracts.agreeToTermsOfService)
  agreeToTermsOfService(@Req() req: AuthenticatedAccountRequest) {
    return tsRestHandler(apiContract.contracts.agreeToTermsOfService, async () => {
      const result = await this.contractsService.agreeToTermsOfService(req.user.id, req.ip!);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/admin-accounts.controller/inviteMissionCollaborator] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'AGREE_TO_TERMS_OF_SERVICE_USER_NOT_ACTIVE':
            return { status: HttpStatus.BAD_REQUEST, body: { message: 'User not active' } };
          case 'FIND_USER_BY_ID_DB_ERROR':
          case 'FIND_USER_BY_ID_NOT_FOUND':
          case 'UPDATE_USER_BY_ID_DB_ERROR':
            return { status: HttpStatus.INTERNAL_SERVER_ERROR, body: { message: 'Internal server error' } };
        }
      }

      return { status: HttpStatus.OK, body: { message: 'Terms of service accepted' } };
    });
  }
}
