import { Contract, ContractPartyType, ContractType, Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';

import { DbService } from '@lib/global/db.service';

export type UpdateContractProperties = {
  documentTitle: string | null;
  type: ContractType;
  signedAt: Date | null;
};

export type PagedContracts = { count: number; contracts: Contract[] };

@Injectable()
export class ContractsRepository {
  constructor(private readonly prisma: DbService) {}

  /**
   * Repository function to fetch page of contracts for contracts table. Contracts are fetched only if:
   * - they are not deleted,
   * - they are not builder agreement,
   * - they have matching account id (either one of the parties or the contract),
   * - if contract is from pandadoc then it is only shown if it is distributed.
   *
   * @param  {string} accountId - Id of the account
   * @param  {number} page - Page number
   * @param  {number} pageSize - Size of the page
   * @returns Promise - Result delegated back {@link PagedContracts}
   */
  findMany = async (accountId: string, page: number, pageSize: number): Promise<PagedContracts> => {
    const where: Prisma.ContractWhereInput = {
      deletedAt: { isSet: false },
      NOT: { type: ContractType.MissionAgreement },
      AND: [
        {
          OR: [
            {
              parties: {
                some: {
                  accountId,
                },
              },
            },
            { accountId },
          ],
        },
        {
          OR: [
            {
              pandadocMetadata: { isSet: false },
            },
            { pandadocMetadata: { is: { isDocumentSent: true } } },
          ],
        },
      ],
    };

    const contracts = await this.prisma.contract.findMany({
      where,
      skip: page * pageSize,
      take: pageSize,
      orderBy: { createdAt: 'desc' },
    });

    const count = await this.prisma.contract.count({
      where,
    });

    return {
      count,
      contracts,
    };
  };

  findById = async (contractId: string): Promise<Contract | null> => {
    return await this.prisma.contract.findFirst({ where: { sid: contractId } });
  };

  findByPandadocId = async (pandadocId: string): Promise<Contract | null> => {
    return await this.prisma.contract.findFirst({
      where: {
        pandadocMetadata: {
          is: { id: pandadocId },
        },
      },
    });
  };

  create = async (data: Prisma.ContractUncheckedCreateInput): Promise<Contract> => {
    return await this.prisma.contract.create({ data });
  };

  updateById = async (contractId: string, data: Prisma.ContractUpdateInput): Promise<Contract> => {
    return await this.prisma.contract.update({
      where: { sid: contractId },
      data,
    });
  };

  updateContractParty = async (contractId: string, userId: string, partyData: Prisma.ContractPartyUpdateInput): Promise<Contract> => {
    return this.prisma.contract.update({
      where: { sid: contractId },
      data: {
        parties: {
          updateMany: {
            data: partyData,
            where: {
              user: userId,
            },
          },
        },
      },
    });
  };

  updateContractProperties = async (contractId: string, { documentTitle, type, signedAt }: UpdateContractProperties): Promise<Contract> => {
    const update: Prisma.ContractUpdateInput = {
      documentTitle,
      type,
    };

    if (signedAt) {
      update.parties = {
        updateMany: {
          data: { signedAt },
          where: {
            type: ContractPartyType.BillingCustomer,
          },
        },
      };
    }

    return this.prisma.contract.update({
      where: { sid: contractId },
      data: update,
    });
  };

  findContractByAccountAndType = async (accountId: string, type: ContractType): Promise<Contract | null> => {
    return await this.prisma.contract.findFirst({
      where: { accountId, type },
    });
  };
}
