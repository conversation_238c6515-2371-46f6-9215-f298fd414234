import { Module } from '@nestjs/common';

import { ContractsController } from './contracts.controller';
import { ContractsRepository } from './contracts.repository';
import { ContractsService } from './contracts.service';
import { CommonAccountsModule } from '@common/accounts/accounts.module';
import { CommonBillingAccountsModule } from '@common/billing-accounts/billing-accounts.module';
import { CommonCompaniesModule } from '@common/companies/companies.module';
import { CommonHubspotModule } from '@common/hubspot/hubspot.module';
import { CommonMissionsModule } from '@common/missions/missions.module';
import { CommonRBACModule } from '@common/rbac/rbac.module';
import { CommonSlackModule } from '@common/slack/slack.module';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientsModule } from '@lib/clients/clients.module';
import { GuardsModule } from '@lib/guards/guards.module';

@Module({
  imports: [
    GuardsModule,
    CommonUsersModule,
    CommonRBACModule,
    CommonSlackModule,
    CommonHubspotModule,
    CommonMissionsModule,
    CommonCompaniesModule,
    CommonAccountsModule,
    CommonBillingAccountsModule,
    ClientsModule,
  ],
  controllers: [ContractsController],
  providers: [ContractsService, ContractsRepository],
})
export class ContractsModule {}
