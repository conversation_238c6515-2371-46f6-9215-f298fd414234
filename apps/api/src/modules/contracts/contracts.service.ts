import { BillingPaymentDue, Company, Contract, ContractPartyType, ContractStatus, ContractType, Prisma, User, UserStatus } from '@a_team/prisma';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
  CreateCustomContractsBodyDto,
  GetContractResponseDto,
  GetContractsQueryDto,
  GetContractsResponseDto,
  HubspotDealWebhookBody,
  HubspotDealWebhookResponse,
  PandadocContractData,
  PandadocDocumentWebhookPayload,
  PandadocFieldSchema,
  PandadocLinkedObjectSchema,
  PandadocRecipientSchema,
  UpdateContractBodyDto,
} from '@packages/contracts';
import { format } from 'date-fns';

import { ContractsRepository } from './contracts.repository';
import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonBillingAccountsRepository } from '@common/billing-accounts/billing-accounts.repository';
import { CommonCompaniesRepository, CompanyWithAccounts } from '@common/companies/companies.repository';
import { CommonHubspotService } from '@common/hubspot/hubspot.service';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonSlackService } from '@common/slack/slack.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { PandadocApiClient } from '@lib/clients/pandadoc-api.client';
import { SentryService } from '@lib/global/sentry.service';

import { Errors } from '@lib/errors';

type MissionAndAccountInfo = {
  accountId: string;
  missionId: string;
  roleId?: string;
};

type AgreeToTermsOfServiceErrorCodes = 'AGREE_TO_TERMS_OF_SERVICE_USER_NOT_ACTIVE';

@Injectable()
export class ContractsService {
  private sendContractSignedMessage = async (contract: Contract, user: User) => {
    const contractUrl = `${this.configService.get('WEB_BASE_URL')}/contracts/${contract.sid}`;

    await this.slackService.sendMessage({
      channel: 'teamformations',
      text: [`<mailto:${user.email}|${user.firstName} ${user.lastName}> just signed <${contractUrl}|contract>`, `Type: ${contract.type}`].join('\n'),
    });
  };

  private sendContractAccountNotFoundMessage = async (pandadocId: string, contractName: string, message: string) => {
    await this.slackService.sendMessage({
      channel: 'teamformations',
      text: [`Failed to find matching account for contract: <https://app.pandadoc.com/a/#/documents/${pandadocId}|${contractName}>`, `Error: ${message}`].join(
        '\n'
      ),
    });
  };

  private extractHubspotIds = (linkedObjects: PandadocLinkedObjectSchema[]) => {
    const hubspotData: {
      companyId?: string;
      contactId?: string;
      dealId?: string;
    } = {};

    for (const linkedObject of linkedObjects) {
      switch (linkedObject.entity_type) {
        case 'company':
          hubspotData.companyId = linkedObject.entity_id;
          break;
        case 'contact':
          hubspotData.contactId = linkedObject.entity_id;
          break;
        case 'deal':
          hubspotData.dealId = linkedObject.entity_id;
          break;
      }
    }

    return hubspotData;
  };

  /**
   * Helper function to get ContractPartyType from pandadoc webhook.
   * It tries to match type to pandadoc role.
   * If pandadoc role is not one of the defines ones, "companyUser"s are considered BillingCustomers,
   * and regular "user"s are considered as admins, that is ATeam parties.
   *
   * @param   {User} user - User from the db
   * @param   {string} pandadocRole - Pandadoc role assigned to this user (our defined roles on Pandadoc are ATeam and Client)
   * @returns {ContractPartyType} - Contract party type (ATeam or Client)
   */
  private getUserPartyType = (user: User, pandadocRole?: string): ContractPartyType => {
    switch (pandadocRole) {
      case 'Client':
        return ContractPartyType.BillingCustomer;
      case 'ATeam':
        return ContractPartyType.ATeam;
      default:
        return user.type === 'companyUser' ? ContractPartyType.BillingCustomer : ContractPartyType.ATeam;
    }
  };

  /**
   * Helper function used to process document recipients, update signature status and contract status.
   * It loops through recipients and looks if they signed or not and updates the contract in the db.
   * Finally, it checks if all parties signed the contract and updates its status to Completed if they did.
   *
   * @param   {string} contractId - Id of the contract in the db
   * @param   {PandadocRecipientSchema[]} recipients - Array of recipients data
   */
  private processPandadocDocumentRecipients = async (contractId: string, recipients: PandadocRecipientSchema[]) => {
    const contract = await this.contractsRepository.findById(contractId);

    if (!contract) {
      return;
    }

    for (const recipient of recipients) {
      if (recipient.recipient_type === 'signer') {
        const user = await this.commonUsersRepository.findUserByEmail(recipient.email);

        if (!user) {
          Logger.error(`[modules/contracts/contracts.service/processPandadocDocumentRecipients] - User with email ${recipient.email} does not exist`);
          continue;
        }

        const existingParty = contract.parties.find((party) => party.user === user.id);

        if (existingParty) {
          await this.contractsRepository.updateContractParty(contract.sid, user.id, { signedAt: recipient.has_completed ? new Date() : null });

          if (!existingParty.signedAt && recipient.has_completed) {
            await this.sendContractSignedMessage(contract, user);
          }
          continue;
        }

        await this.contractsRepository.updateById(contract.sid, {
          parties: {
            push: {
              type: this.getUserPartyType(user, recipient.roles?.[0]),
              user: user.id,
              signedAt: recipient.has_completed ? new Date() : null,
            },
          },
        });
      }
    }

    if (contract.status !== ContractStatus.Created) {
      return;
    }

    const allRecipientsSigned = recipients.every((recipient) => recipient.has_completed || recipient.recipient_type !== 'signer');

    if (allRecipientsSigned) {
      await this.contractsRepository.updateById(contractId, { status: ContractStatus.Completed });
      if (contract.type === ContractType.ServiceOrder) {
        if (contract.missionId && contract.role) {
          await this.commonMissionsRepository.updateMissionRole(contract.missionId, contract.role, {
            status: 'Active',
          });
        } else {
          Logger.error(`[modules/contracts/contracts.service/processPandadocDocumentRecipients] - No mission or role attached to contract ${contract.sid}`);
          this.sentryService.captureError(new Error(`No mission or role attached to contract ${contract.sid}`));
        }
      }
    }
  };

  /**
   * Helper function to process pandadoc document fields.
   * It finds the purchase order number and billing form data from the fields and updates the
   * mission and billing account accordingly.
   *
   * @param   {string} contractId - Id of the contract in the db
   * @param   {PandadocFieldSchema[]} fields - Array of fields data
   */
  private processPandadocDocumentFields = async (contractId: string, fields: PandadocFieldSchema[]) => {
    try {
      const contract = await this.contractsRepository.findById(contractId);

      if (!contract?.accountId) {
        return;
      }

      let purchaseOrderNumber: string | null = null;
      const billingFormData: Record<string, string | null> = {};

      for (const field of fields) {
        const { field_id: fieldId, value } = field;

        if (fieldId === 'purchaseOrderNumber') {
          purchaseOrderNumber = value as string | null;
        } else {
          billingFormData[fieldId] = value as string | null;
        }
      }

      if (!billingFormData.billingEntityName) {
        return;
      }

      const accountResult = await this.commonAccountsRepository.findAccountById(contract.accountId);
      const account = accountResult.isOk() ? accountResult.value : undefined;

      const billingAccount = await this.commonBillingAccountsRepository.getBillingAccountByAccountId(contract.accountId);

      const billingFormDbData: Prisma.BillingAccountCreateInput = {
        billingInfo: {
          name: billingFormData.billingEntityName,
          contactEmail: billingFormData.billingEmail,
          contactName: billingFormData.billingUserName,
          tin: billingFormData.billingFederalID,
        },
      };

      if (account?.clientCompany) {
        billingFormDbData.clientCompanyModel = {
          connect: { id: account.clientCompany },
        };
      }

      if (billingFormData.billingPaymentTerms) {
        billingFormDbData.paymentTerms = {
          due: billingFormData.billingPaymentTerms as BillingPaymentDue,
        };
      }

      if (billingAccount) {
        await this.commonBillingAccountsRepository.updateBillingAccount(billingAccount.id, billingFormDbData);
      } else {
        const newBillingAccount = await this.commonBillingAccountsRepository.createBillingAccount(billingFormDbData);
        await this.commonAccountsRepository.updateAccountById(contract.accountId, {
          billingAccountModel: { connect: { id: newBillingAccount.id } },
        });
      }

      if (purchaseOrderNumber && contract.missionId) {
        await this.commonMissionsRepository.updateMission(contract.missionId, {
          invoicing: {
            purchaseOrderNumber,
          },
        });
      }
    } catch (error) {
      this.sentryService.logAndCaptureError(
        `[modules/contracts/contracts.service/processPandadocDocumentFields] - Error processing pandadoc document fields for contract ${contractId}`,
        error as Error
      );
    }
  };

  /**
   * Helper function to get contract's pdf url. If contract is made with pandadoc, pdf is gotten
   * using our proxy url `/v1/contracts/pdf/:id/raw`. Else, contract is gotten by pdf url saved
   * in the db.
   *
   * @param  {Contract} contract - Contract data
   * @returns string - Contract pdf url
   */
  private getContractPdfUrl = (contract: Contract): string => {
    if (contract.pandadocMetadata) {
      return `${this.configService.get('API_BASE_URL')}/v1/contracts/pdf/${contract.pandadocMetadata.id}/raw`;
    }

    return contract.downloadURL;
  };

  private getAccountFromHubspotCompanyId = async (companyId: string | undefined) => {
    if (!companyId) {
      return undefined;
    }

    const hubspotCompanyResult = await this.hubspotService.getCompanyById(companyId);

    if (hubspotCompanyResult.isErr()) {
      return undefined;
    }

    const hubspotCompany = hubspotCompanyResult.value;
    const { website, platformId } = hubspotCompany;

    let company: CompanyWithAccounts | null = null;

    if (platformId) {
      company = await this.commonCompaniesRepository.findCompanyWithAccountsById(platformId);
    }

    if (!company && website) {
      company = await this.commonCompaniesRepository.findCompanyWithAccountsByWebsite(website);
    }

    if (!company?.accounts) {
      return undefined;
    }

    const lastAccount = company.accounts[company.accounts.length - 1];
    return lastAccount.id;
  };

  private getMissionAndAccountDataFromDealId = async (dealId: string | undefined): Promise<MissionAndAccountInfo | undefined> => {
    if (!dealId) {
      return undefined;
    }

    const hubspotDealResult = await this.hubspotService.getDealById(dealId);

    if (hubspotDealResult.isErr()) {
      return undefined;
    }

    const hubspotDeal = hubspotDealResult.value;
    const missionId = hubspotDeal.missionId;

    if (!missionId) {
      return undefined;
    }

    const missionResult = await this.commonMissionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return;
    }

    const mission = missionResult.value;

    if (!mission) {
      return undefined;
    }

    const role = mission.roles.find((r) => r.id === hubspotDeal.roleId);

    return {
      accountId: mission.accountId!,
      missionId: mission.mid,
      roleId: role?.id,
    };
  };

  private getTypeForPandadocContract = (data: PandadocContractData): ContractType => {
    const documentName = data.name.toLowerCase();
    const hubspotData = this.extractHubspotIds(data.linked_objects ?? []);

    if (hubspotData.companyId || documentName.includes('msa') || documentName.includes('master service')) {
      return ContractType.MasterServicesAgreement;
    }

    if (documentName.includes('scope of work') || documentName.includes('sow')) {
      return ContractType.ScopeOfWork;
    }

    if (documentName.includes('service order') || hubspotData.dealId) {
      return ContractType.ServiceOrder;
    }

    return ContractType.ClientContract;
  };

  constructor(
    private readonly contractsRepository: ContractsRepository,
    private readonly configService: ConfigService,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly commonAccountsRepository: CommonAccountsRepository,
    private readonly commonBillingAccountsRepository: CommonBillingAccountsRepository,
    private readonly slackService: CommonSlackService,
    private readonly hubspotService: CommonHubspotService,
    private readonly commonMissionsRepository: CommonMissionsRepository,
    private readonly commonCompaniesRepository: CommonCompaniesRepository,
    private readonly pandadocApiClient: PandadocApiClient,
    private readonly sentryService: SentryService
  ) {}

  /**
   * Service function used for handling the gotten pandadoc webhook. The contract is either updated or created.
   * If a contract is made from a hubspot deal it will be attached for a mission/role (usually Service Order and Scope of Work contracts).
   * If a contract is made from the hubspot company page it will be attached for a company (usually MSA contracts).
   *
   * Throws an error if the account can't be found for the specific contract.
   *
   * @param  {PandadocDocumentWebhookPayload} {data} - The pandadoc webhook data
   */
  handlePandadocWebhook = async ({ data }: PandadocDocumentWebhookPayload) => {
    const { id: pandadocId, sent_by, recipients = [], fields = [] } = data;

    const isDocumentSent = !!sent_by;
    const existingContract = await this.contractsRepository.findByPandadocId(pandadocId);

    if (!existingContract) {
      const hubspotData = this.extractHubspotIds(data.linked_objects ?? []);

      const dealId = hubspotData.dealId;
      const companyId = hubspotData.companyId;

      let accountId: string | undefined = undefined;
      let missionId: string | undefined = undefined;
      let roleId: string | undefined = undefined;

      const dealMissionAndAccountData = await this.getMissionAndAccountDataFromDealId(dealId);

      if (dealMissionAndAccountData) {
        accountId = dealMissionAndAccountData.accountId;
        missionId = dealMissionAndAccountData.missionId;
        roleId = dealMissionAndAccountData.roleId;
      } else {
        accountId = await this.getAccountFromHubspotCompanyId(companyId);
      }

      if (!accountId) {
        const message = `Couldn't find corresponding account and/or mission id for pandadoc contract ${data.id}`;

        await this.sendContractAccountNotFoundMessage(data.id, data.name, message);
        throw new Error(message);
      }

      const contract = await this.contractsRepository.create({
        custom: false,
        pandadocMetadata: {
          id: pandadocId,
          hubspotDealId: dealId,
          hubspotCompanyId: companyId,
          isDocumentSent,
          recipients: recipients.map((r) => ({
            id: r.id,
            email: r.email,
            role: r.role,
          })),
        },
        downloadURL: '',
        documentTitle: data.name,
        type: this.getTypeForPandadocContract(data),
        status: ContractStatus.Created,
        missionId,
        accountId,
        role: roleId,
      });

      await this.processPandadocDocumentRecipients(contract.sid, recipients);
      await this.processPandadocDocumentFields(contract.sid, fields);
      return contract;
    }

    if (existingContract.pandadocMetadata && isDocumentSent !== existingContract.pandadocMetadata?.isDocumentSent) {
      await this.contractsRepository.updateById(existingContract.sid, {
        pandadocMetadata: {
          ...existingContract.pandadocMetadata,
          isDocumentSent,
        },
      });
    }

    await this.processPandadocDocumentRecipients(existingContract.sid, recipients);
    return existingContract;
  };

  getContracts = async (query: GetContractsQueryDto, accountId: string, userId: string): Promise<GetContractsResponseDto> => {
    const user = await this.commonUsersRepository.findUserById(userId);

    const { contracts, count } = await this.contractsRepository.findMany(accountId, query.page, query.pageSize);

    const resultData = contracts.map((contract) => {
      const clientParty = contract.parties.find((p) => p.type === ContractPartyType.BillingCustomer);
      const signedAt = clientParty?.signedAt;
      const recipients = contract.pandadocMetadata?.recipients ?? [];
      const isDocumentSent = contract.pandadocMetadata?.isDocumentSent ?? false;

      return {
        id: contract.sid,
        status: contract.status,
        type: contract.type,
        documentTitle: contract.documentTitle ?? undefined,
        isSigned: !!signedAt || contract.status === ContractStatus.Completed,
        signedAt: signedAt ?? undefined,
        isDocumentSent,
        userCanSign: user ? recipients.some((r) => r.email === user.email) : false,
      };
    });

    return { data: resultData, count };
  };

  getContract = async (id: string, userId: string): Promise<GetContractResponseDto> => {
    const contract = await this.contractsRepository.findById(id);

    if (!contract) {
      throw new HttpException(`No contract with id ${id}`, HttpStatus.NOT_FOUND);
    }

    let signingURL: string | undefined;

    if (contract.pandadocMetadata) {
      const user = await this.commonUsersRepository.findUserById(userId);
      const userEmail = user?.email;

      if (userEmail) {
        signingURL = await this.pandadocApiClient.getPandadocSigningUrl(contract.pandadocMetadata.id, userEmail);
      }
    }

    return {
      id: contract.sid,
      downloadURL: this.getContractPdfUrl(contract),
      signingURL,
      documentTitle: contract.documentTitle,
      type: contract.type,
    };
  };

  createCustomContracts = async (accountId: string, data: CreateCustomContractsBodyDto) => {
    for (const contractData of data) {
      await this.contractsRepository.create({
        downloadURL: contractData.url,
        documentTitle: contractData.fileName,
        createdAt: new Date(),
        type: contractData.type,
        status: ContractStatus.Completed,
        custom: true,
        accountId,
      });
    }
  };

  getPandadocPdf = async (pandadocId: string) => {
    return this.pandadocApiClient.getPandadocDocumentPdf(pandadocId);
  };

  handleHubspotDealWebhook = async (data: HubspotDealWebhookBody): Promise<HubspotDealWebhookResponse | null> => {
    const missionResult = await this.commonMissionsRepository.findMissionWithAccountInfoById(data.missionId);

    if (missionResult.isErr()) {
      // TODO: refactor this to use a proper neverthrow error handling
      return null;
    }

    const mission = missionResult.value;

    if (!mission) {
      const error = new Error(`Missing mission for mission id ${data.missionId}`);
      this.sentryService.logAndCaptureError(`[modules/contracts/contracts.service/handleHubspotDealWebhook] - ${error.message}`, error);
      return null;
    }

    if (!mission.accountId) {
      const error = new Error(`Missing account id for mission id ${data.missionId}`);
      this.sentryService.logAndCaptureError(`[modules/contracts/contracts.service/handleHubspotDealWebhook] - ${error.message}`, error);
      return null;
    }

    const role = mission.roles.find((r) => r.id === data.roleId);

    if (!role) {
      const error = new Error(`Missing role for role id ${data.roleId}`);
      this.sentryService.logAndCaptureError(`[modules/contracts/contracts.service/handleHubspotDealWebhook] - ${error.message}`, error);
      return null;
    }

    let company: Company | null = null;
    if (mission.accountModel?.companyId) {
      company = await this.commonCompaniesRepository.findCompanyById(mission.accountModel.companyId);
    }

    let msaSigningDate = '';
    const msaContract = await this.contractsRepository.findContractByAccountAndType(mission.accountId, ContractType.MasterServicesAgreement);
    if (msaContract) {
      const signingDate = msaContract.parties.find((p) => p.type === ContractPartyType.BillingCustomer)?.signedAt;
      msaSigningDate = signingDate ? format(signingDate, 'yyyy-MM-dd') : '-';
    }

    let builder: User | null = null;
    if (role.user) {
      builder = await this.commonUsersRepository.findUserById(role.user);
    }

    const billingAccount = await this.commonBillingAccountsRepository.getBillingAccountByAccountId(mission.accountId);

    const rate = role.isFullTimeRetainer ? role.monthlyRate : role.hourlyRate;
    const clientRate = rate ? `${rate}$ per ${role.isFullTimeRetainer ? 'month' : 'hour'}` : '';
    const availability = role?.availability;

    const billingPaymentTerms = billingAccount?.paymentTerms?.due;
    const billingPaymentTermsDays = billingPaymentTerms ? billingPaymentTerms.substring(3) : null;

    await this.hubspotService.updateDealById(String(data.id), {
      has_billing_form: !!billingAccount,
      role_builder_name: builder ? `${builder.firstName} ${builder.lastName}` : null,
      client_rate: clientRate,
      role_description: role.description ?? '',
      role_schedule: role.workingHours?.name ?? '',
      role_start_date: availability?.date ? format(availability.date, 'yyyy-MM-dd') : '-',
      contract_company_name: company?.name ?? null,
      msa_signing_date: msaSigningDate,
      billing_user_name: billingAccount?.billingInfo?.contactName ?? null,
      billing_email: billingAccount?.billingInfo?.contactEmail ?? null,
      billing_entity_name: billingAccount?.billingInfo?.name ?? null,
      billing_federal_id: billingAccount?.billingInfo?.tin ?? null,
      billing_payment_terms: billingPaymentTerms ?? null,
      billing_payment_terms_days: billingPaymentTermsDays ?? null,
      billing_period: mission?.billingPeriod,
      po_number: mission?.invoicing?.purchaseOrderNumber ?? null,
      is_monthly_role: role.isFullTimeRetainer ?? false,
    });

    return { dealId: data.id };
  };

  updateContract = async (contractId: string, data: UpdateContractBodyDto) => {
    const contract = await this.contractsRepository.findById(contractId);

    if (!contract) {
      return null;
    }

    await this.contractsRepository.updateContractProperties(contractId, {
      documentTitle: data.documentTitle ?? contract.documentTitle,
      type: data.type ?? contract.type,
      signedAt: data.signedAt ?? null,
    });

    return { id: contract.sid };
  };

  deleteContract = async (contractId: string) => {
    return this.contractsRepository.updateById(contractId, {
      deletedAt: new Date(),
    });
  };

  agreeToTermsOfService = async (userId: string, ipAddress: string) => {
    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(userId);

    if (userResult.isErr()) {
      return userResult;
    }

    const user = userResult.value;

    if (user.status !== UserStatus.Active) {
      return Errors.createError<AgreeToTermsOfServiceErrorCodes>('AGREE_TO_TERMS_OF_SERVICE_USER_NOT_ACTIVE');
    }

    const newAcceptTOSHistory = user.acceptTOSHistory ?? [];
    if (user.acceptTOS) {
      newAcceptTOSHistory.push(user.acceptTOS);
    }

    const updateResult = await this.commonUsersRepository.updateUserById(userId, {
      acceptTOS: {
        signedAt: new Date(),
        version: 'v2',
        ip: ipAddress,
      },
      acceptTOSHistory: newAcceptTOSHistory,
    });

    return updateResult;
  };
}
