import { Controller, HttpStatus, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRestHandler } from '@ts-rest/nest';
import { TsRestHandler } from '@ts-rest/nest';

import { AccountsService } from './accounts.service';
import { SentryService } from '@lib/global/sentry.service';

import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

@Controller()
@UseGuards(AdminJwtGuard)
export class AdminAccountsController {
  constructor(
    private readonly accountsService: AccountsService,
    private readonly sentryService: SentryService
  ) {}

  @TsRestHandler(apiContract.adminAccounts.getAccountTeamAdvisorByAccountId)
  getAccountTeamAdvisorByAccountId(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminAccounts.getAccountTeamAdvisorByAccountId, async ({ params }) => {
      const result = await this.accountsService.getAccountTeamAdvisor(params.accountId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/admin-accounts.controller/getAccountTeamAdvisor] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Team advisor not found' },
            };
          case 'FIND_ACTIVE_USER_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      const teamAdvisor = result.value;

      return {
        status: HttpStatus.OK,
        body: teamAdvisor,
      };
    });
  }

  @TsRestHandler(apiContract.adminAccounts.getAccountCollaboratorsByAccountId)
  getAccountCollaboratorsByAccountId(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminAccounts.getAccountCollaboratorsByAccountId, async ({ params }) => {
      const result = await this.accountsService.getAccountCollaborators(params.accountId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/admin-accounts.controller/getAccountCollaborators] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACCOUNT_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Account not found' },
            };
          case 'FIND_ACCOUNT_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      const collaborators = result.value;

      return {
        status: HttpStatus.OK,
        body: collaborators,
      };
    });
  }

  @TsRestHandler(apiContract.adminAccounts.inviteMissionCollaborator)
  inviteMissionCollaborator(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminAccounts.inviteMissionCollaborator, async ({ params, body, headers }) => {
      const result = await this.accountsService.inviteMissionCollaborator(params.missionId, body, headers.authorization!);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/admin-accounts.controller/inviteMissionCollaborator] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'INVITE_MISSION_COLLABORATOR_NO_MISSION_SPEC_ID':
          case 'INVITE_MISSION_COLLABORATOR_API_ERROR':
          case 'FIND_MISSION_BY_ID_DB_ERROR':
          case 'FIND_MISSION_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: { message: 'Mission collaborator invited' },
      };
    });
  }
}
