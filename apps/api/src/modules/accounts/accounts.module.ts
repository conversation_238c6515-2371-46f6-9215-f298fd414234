import { Module } from '@nestjs/common';

import { AccountsController } from './accounts.controller';
import { AccountsRepository } from './accounts.repository';
import { AccountsService } from './accounts.service';
import { AdminAccountsController } from './admin-accounts.controller';
import { CommonAccountsModule } from '@common/accounts/accounts.module';
import { CommonMissionsModule } from '@common/missions/missions.module';
import { CommonRBACModule } from '@common/rbac/rbac.module';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientsModule } from '@lib/clients/clients.module';
import { GuardsModule } from '@lib/guards/guards.module';

@Module({
  imports: [GuardsModule, CommonAccountsModule, CommonUsersModule, CommonRBACModule, ClientsModule, CommonMissionsModule],
  controllers: [AccountsController, AdminAccountsController],
  providers: [AccountsService, AccountsRepository],
})
export class AccountsModule {}
