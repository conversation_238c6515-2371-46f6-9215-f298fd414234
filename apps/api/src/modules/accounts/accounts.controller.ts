import { <PERSON>, HttpStatus, UseGuards, Req } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRestHandler } from '@ts-rest/nest';
import { TsRestHandler } from '@ts-rest/nest';

import { AccountsService } from './accounts.service';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { SentryService } from '@lib/global/sentry.service';

import { AuthenticatedRequest, JwtGuard } from '@lib/guards/jwt.guard';

@Controller()
export class AccountsController {
  constructor(
    private readonly accountsService: AccountsService,
    private readonly sentryService: SentryService,
    private readonly rbacService: CommonRBACService
  ) {}

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.accounts.getAccountTeamAdvisor)
  getAccountTeamAdvisor(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.accounts.getAccountTeamAdvisor, async ({ params }) => {
      await this.rbacService.checkClientPermission(req.user.id, params.accountId, 'workspace', 'read');

      const result = await this.accountsService.getAccountTeamAdvisor(params.accountId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/accounts.controller/getAccountTeamAdvisor] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Team advisor not found' },
            };
          case 'FIND_ACTIVE_USER_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      const teamAdvisor = result.value;

      return {
        status: HttpStatus.OK,
        body: teamAdvisor,
      };
    });
  }

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.accounts.getAccountCollaborators)
  getAccountCollaborators(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.accounts.getAccountCollaborators, async ({ params }) => {
      await this.rbacService.checkClientPermission(req.user.id, params.accountId, 'workspace', 'read');

      const result = await this.accountsService.getAccountCollaborators(params.accountId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/accounts/accounts.controller/getAccountCollaborators] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ACCOUNT_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Account not found' },
            };
          case 'FIND_ACCOUNT_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      const collaborators = result.value;

      return {
        status: HttpStatus.OK,
        body: collaborators,
      };
    });
  }
}
