import { Injectable } from '@nestjs/common';
import { AccountAccessLevel, Collaborator, InviteMissionCollaboratorBody } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonAccountsService } from '@common/accounts/accounts.service';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { ClientAppV1Client } from '@lib/clients/client-app-v1.client';
import { SentryService } from '@lib/global/sentry.service';

import { Errors } from '@lib/errors';

type InviteMissionCollaboratorErrorCodes = 'INVITE_MISSION_COLLABORATOR_NO_MISSION_SPEC_ID';

@Injectable()
export class AccountsService extends CommonAccountsService {
  constructor(
    configService: ConfigService,
    sentryService: SentryService,
    commonUsersRepository: CommonUsersRepository,
    commonAccountsRepository: CommonAccountsRepository,
    private readonly clientAppV1Client: ClientAppV1Client,
    private readonly commonMissionsRepository: CommonMissionsRepository
  ) {
    super(configService, sentryService, commonUsersRepository, commonAccountsRepository);
  }

  async getAccountCollaborators(accountId: string) {
    const accountResult = await this.commonAccountsRepository.findAccountById(accountId);

    if (accountResult.isErr()) {
      return accountResult;
    }

    const account = accountResult.value;

    const collaborators: Collaborator[] = [];

    for (const member of account.members) {
      const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(member.user);

      if (userResult.isErr()) {
        const error = userResult.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[accounts/accounts.service/getAccountCollaborators] - ${error.code}`, error.originalError);
        }

        continue;
      }

      const user = userResult.value;

      collaborators.push({
        id: user.id,
        email: user.email,
        accessLevel: member.accessLevel as AccountAccessLevel,
        username: user.username!,
        firstName: user.firstName!,
        lastName: user.lastName ?? '',
        pictureURL: user.pictureURL,
      });
    }

    return ok(collaborators);
  }

  async inviteMissionCollaborator(missionId: string, body: InviteMissionCollaboratorBody, token: string) {
    const missionResult = await this.commonMissionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    // TODO: Make sure this works after missionSpecs are deprecated
    if (!mission.missionSpecId) {
      return Errors.createError<InviteMissionCollaboratorErrorCodes>('INVITE_MISSION_COLLABORATOR_NO_MISSION_SPEC_ID', {
        originalError: new Error('Mission has no mission spec id'),
        isUnexpectedError: true,
      });
    }

    const { fullName, email, role, skipEmail } = body;

    const [firstName] = fullName.split(' ');
    const lastName = fullName.slice(firstName.length + 1);

    const result = await this.clientAppV1Client.inviteMissionCollaborator(
      {
        email,
        firstName,
        lastName,
        missionSpecId: mission.missionSpecId,
        role,
        skipEmail,
      },
      token
    );

    if (result.isErr()) {
      return result;
    }

    return ok(result.value);
  }
}
