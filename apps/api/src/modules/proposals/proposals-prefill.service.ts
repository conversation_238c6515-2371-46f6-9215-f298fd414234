import { Experience, User } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok } from 'neverthrow';
import { z } from 'zod';

import { ProposalsPromptsService } from './proposals-prompts.service';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonRoleCategoriesRepository } from '@common/missions/role-categories.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { OpenAIClient } from '@lib/clients/open-ai.client';

import { Errors } from '@lib/errors';

type PromptBuilderData = {
  aboutMe?: string;
  yearsExperience?: number;
  jobExperiences: Partial<Experience>[];
  projectExperiences: Partial<Experience>[];
};

type PromptRoleData = {
  roleName: string;
  roleDescription: string;
};

type SectionPromptData = {
  builderProfile: string;
  builderFirstName: string;
  roleName: string;
  roleDescription: string;
};

type CandidateGeneratedSections = 'experience-section' | 'requirements-section';

type GetMissionRoleErrorCodes = 'GET_MISSION_ROLE_NO_ROLE_FOUND';

type GetGeneratedSectionForRoleErrorCodes = 'GET_GENERATED_SECTION_FOR_ROLE_NO_ROLE_FOUND';

type GetGeneratedSectionForCandidateErrorCodes = 'GET_GENERATED_SECTION_FOR_CANDIDATE_NO_BUILDER_FOUND';

@Injectable()
export class ProposalsPrefillService {
  private getBuilderDataForPrompt = async (builder: User) => {
    const experiencesResult = await this.commonUsersRepository.findUserExperiences(builder.id);

    if (experiencesResult.isErr()) {
      return experiencesResult;
    }

    const experiences = experiencesResult.value;
    const jobExperiences = experiences.filter((e) => e.kind === 'job');
    const projectExperiences = experiences.filter((e) => e.kind === 'project');

    const builderData: PromptBuilderData = {
      aboutMe: builder.aboutMe ?? undefined,
      yearsExperience: builder.yearsExperience ?? undefined,
      jobExperiences: jobExperiences.map((e) => ({ jobRole: e.jobRole, description: e.description, companyName: e.companyName })),
      projectExperiences: projectExperiences.map((e) => ({
        projectRole: e.jobRole,
        projectTitle: e.title,
        description: e.description,
        companyName: e.companyName,
      })),
    };

    return ok(builderData);
  };

  private getGeneratedText = async (prompt: string) => {
    const result = await this.openaiClient.generateObject({
      prompt,
      schema: z.object({ result: z.string() }),
    });

    if (result.isErr()) {
      return result;
    }

    return ok(result.value.object.result);
  };

  private getGeneratedSection = async (section: CandidateGeneratedSections, builder: User, builderPromptData: PromptBuilderData, roleData: PromptRoleData) => {
    const sectionPromptData: SectionPromptData = {
      builderProfile: JSON.stringify(builderPromptData),
      roleDescription: roleData.roleDescription,
      roleName: roleData.roleName,
      builderFirstName: builder.firstName!,
    };

    const sectionPrompt = await this.promptService.getPrompt({ promptName: section, variables: sectionPromptData });
    const sectionResult = await this.getGeneratedText(sectionPrompt);

    if (sectionResult.isErr()) {
      return sectionResult;
    }

    return ok(sectionResult.value);
  };

  private getGeneratedExperienceSectionTitle = async (sectionText: string, builderName: string) => {
    const prompt = await this.promptService.getPrompt({ promptName: 'experience-section-title', variables: { sectionText, builderName } });
    const result = await this.openaiClient.generateObject({
      prompt,
      schema: z.object({ result: z.string() }),
    });

    if (result.isErr()) {
      return result;
    }

    return ok(result.value.object.result);
  };

  private getSectionTitle = async (section: CandidateGeneratedSections, builderName: string, sectionText: string) => {
    switch (section) {
      case 'requirements-section':
        return ok(`How does ${builderName} fit your requirements?`);
      case 'experience-section':
        return this.getGeneratedExperienceSectionTitle(sectionText, builderName);
    }
  };

  private getMissionRole = async (missionId: string, roleId: string) => {
    const missionResult = await this.missionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const role = mission.roles.find((r) => r.id === roleId);

    if (!role) {
      return Errors.createError<GetMissionRoleErrorCodes>('GET_MISSION_ROLE_NO_ROLE_FOUND');
    }

    return ok(role);
  };

  constructor(
    private readonly missionsRepository: CommonMissionsRepository,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly commonRoleCategoriesRepository: CommonRoleCategoriesRepository,
    private readonly openaiClient: OpenAIClient,
    private readonly promptService: ProposalsPromptsService
  ) {}

  getGeneratedSectionForRole = async (missionId: string, roleId: string) => {
    const missionResult = await this.missionsRepository.findMissionWithAccountInfoById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const role = mission.roles.find((r) => r.id === roleId);

    if (!role) {
      return Errors.createError<GetGeneratedSectionForRoleErrorCodes>('GET_GENERATED_SECTION_FOR_ROLE_NO_ROLE_FOUND');
    }

    const roleCategoryResult = await this.commonRoleCategoriesRepository.findRoleCategoryById(role.categoryId);

    if (roleCategoryResult.isErr()) {
      return roleCategoryResult;
    }

    const roleCategory = roleCategoryResult.value;

    const roleData = {
      roleName: roleCategory.title,
      companyName: mission.accountModel.workspace?.name ?? '-',
    };

    const prompt = await this.promptService.getPrompt({ promptName: 'about-builders-section', variables: roleData });

    const openaiResult = await this.openaiClient.generateObject({
      prompt,
      schema: z.object({ result: z.string() }),
    });

    if (openaiResult.isErr()) {
      return openaiResult;
    }

    return ok({
      section: openaiResult.value.object.result,
    });
  };

  getGeneratedSectionForCandidate = async (missionId: string, roleId: string, userId: string, section: CandidateGeneratedSections) => {
    const roleResult = await this.getMissionRole(missionId, roleId);

    if (roleResult.isErr()) {
      return roleResult;
    }

    const role = roleResult.value;

    const builder = await this.commonUsersRepository.findUserById(userId);
    if (!builder) {
      return Errors.createError<GetGeneratedSectionForCandidateErrorCodes>('GET_GENERATED_SECTION_FOR_CANDIDATE_NO_BUILDER_FOUND');
    }

    const builderPromptDataResult = await this.getBuilderDataForPrompt(builder);

    if (builderPromptDataResult.isErr()) {
      return builderPromptDataResult;
    }

    const builderPromptData = builderPromptDataResult.value;

    const roleCategoryResult = await this.commonRoleCategoriesRepository.findRoleCategoryById(role.categoryId);

    if (roleCategoryResult.isErr()) {
      return roleCategoryResult;
    }

    const roleCategory = roleCategoryResult.value;

    const roleData = {
      roleDescription: role.description ?? '-',
      roleName: roleCategory.title,
    };

    const sectionResult = await this.getGeneratedSection(section, builder, builderPromptData, roleData);

    if (sectionResult.isErr()) {
      return sectionResult;
    }

    const sectionTitleResult = await this.getSectionTitle(section, builder.firstName!, sectionResult.value);

    if (sectionTitleResult.isErr()) {
      return sectionTitleResult;
    }

    return ok({
      section: sectionResult.value,
      title: sectionTitleResult.value,
    });
  };
}
