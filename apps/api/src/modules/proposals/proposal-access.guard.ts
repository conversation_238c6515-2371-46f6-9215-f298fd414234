import { <PERSON><PERSON> } from '@nestjs/cache-manager';
import { ExecutionContext, Injectable, Logger } from '@nestjs/common';

import { ProposalsRepository } from './proposals.repository';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';

import { JwtGuard, AuthenticatedUser } from '@lib/guards/jwt.guard';

export type ProposalAccessRequest = Request & { user?: AuthenticatedUser };

/**
 * Guard that checks if the user has access to the proposal.
 *
 * If the proposal is public, the user can access it.
 * If the proposal is not public, the user can access it if they are an admin or
 * if they have access to the mission.
 */
@Injectable()
export class ProposalAccessGuard extends JwtGuard {
  constructor(
    configService: ConfigService,
    cacheManager: Cache,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly proposalsRepository: ProposalsRepository,
    private readonly rbacService: CommonRBACService
  ) {
    super(configService, commonUsersRepository, cacheManager);
  }

  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<{ params: { proposalId: string } }>();
    const proposalId = request.params.proposalId;

    if (!proposalId) {
      Logger.error('[modules/proposals/proposal-access.guard/canActivate] - Proposal ID is required');
      return false;
    }

    const proposalResult = await this.proposalsRepository.findById(proposalId);

    if (proposalResult.isErr()) {
      Logger.error('[modules/proposals/proposal-access.guard/canActivate] - Proposal not found');
      return false;
    }

    const proposal = proposalResult.value;

    const isProposalPublic = proposal.publicUntil && new Date() <= new Date(proposal.publicUntil);

    const isAuthenticated = await super.canActivate(context);

    if (!isAuthenticated) {
      if (isProposalPublic) {
        return true;
      }

      return false;
    }

    const newRequest = context.switchToHttp().getRequest<{ user: { id: string } }>();
    const user = await this.commonUsersRepository.findUserById(newRequest.user.id);

    if (!user) {
      Logger.error('[modules/proposals/proposal-access.guard/canActivate] - User not found');
      return false;
    }

    if (user.isAdmin || isProposalPublic) {
      return true;
    }

    const result = await this.rbacService.checkClientPermission(user.id, '', `mission:${proposal.missionId}`, 'read');

    if (result.isErr()) {
      Logger.error('[modules/proposals/proposal-access.guard/canActivate] - User does not have access to mission');
      return false;
    }

    return true;
  }
}
