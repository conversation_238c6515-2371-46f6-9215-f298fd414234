import { Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindProposalByIdErrorCodes = 'FIND_PROPOSAL_BY_ID_DB_ERROR' | 'FIND_PROPOSAL_BY_ID_NOT_FOUND';
type UpdateProposalByIdErrorCodes = 'UPDATE_PROPOSAL_BY_ID_DB_ERROR' | 'UPDATE_PROPOSAL_BY_ID_NOT_FOUND';
type FindCandidateByIdErrorCodes = 'FIND_CANDIDATE_BY_ID_DB_ERROR' | 'FIND_CANDIDATE_BY_ID_NOT_FOUND';
type UpdateCandidateErrorCodes = 'UPDATE_CANDIDATE_DB_ERROR' | 'UPDATE_CANDIDATE_NOT_FOUND';
type CreateProposalErrorCodes = 'CREATE_PROPOSAL_DB_ERROR';

@Injectable()
export class ProposalsRepository {
  constructor(private readonly prisma: DbService) {}

  findById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.proposal.findFirst({
        where: {
          id,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindProposalByIdErrorCodes>('FIND_PROPOSAL_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const proposal = result.value;

    if (!proposal) {
      return Errors.createError<FindProposalByIdErrorCodes>('FIND_PROPOSAL_BY_ID_NOT_FOUND');
    }

    return ok(proposal);
  };

  updateById = async (contractId: string, data: Prisma.ProposalUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.proposal.update({
        where: { id: contractId },
        data,
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<UpdateProposalByIdErrorCodes>('UPDATE_PROPOSAL_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const proposal = result.value;

    if (!proposal) {
      return Errors.createError<UpdateProposalByIdErrorCodes>('UPDATE_PROPOSAL_BY_ID_NOT_FOUND');
    }

    return ok(proposal);
  };

  findCandidateById = async (proposalId: string, userId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.proposal.findFirst({
        where: {
          id: proposalId,
          candidates: {
            some: {
              userId,
            },
          },
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindCandidateByIdErrorCodes>('FIND_CANDIDATE_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const proposal = result.value;
    const candidate = proposal?.candidates[0];

    if (!candidate) {
      return Errors.createError<FindCandidateByIdErrorCodes>('FIND_CANDIDATE_BY_ID_NOT_FOUND');
    }

    return ok(candidate);
  };

  updateCandidate = async (proposalId: string, userId: string, data: Prisma.ProposalCandidateUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.proposal.update({
        where: { id: proposalId },
        data: {
          candidates: {
            updateMany: {
              data,
              where: {
                userId,
              },
            },
          },
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<UpdateCandidateErrorCodes>('UPDATE_CANDIDATE_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const proposal = result.value;

    if (!proposal) {
      return Errors.createError<UpdateCandidateErrorCodes>('UPDATE_CANDIDATE_NOT_FOUND');
    }

    return ok(proposal);
  };

  createProposal = async (data: Prisma.ProposalCreateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.proposal.create({ data }), (e) => new Error(e as string));

    if (result.isErr()) {
      return Errors.createError<CreateProposalErrorCodes>('CREATE_PROPOSAL_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
