import { Module } from '@nestjs/common';

import { AdminProposalsController } from './admin-proposals.controller';
import { AdminProposalsService } from './admin-proposals.service';
import { ProposalsPrefillService } from './proposals-prefill.service';
import { ProposalsPromptsService } from './proposals-prompts.service';
import { ProposalsController } from './proposals.controller';
import { ProposalsRepository } from './proposals.repository';
import { ProposalsService } from './proposals.service';
import { CommonAccountsModule } from '@common/accounts/accounts.module';
import { CommonClientInterviewsModule } from '@common/client-interviews/client-interviews.module';
import { CommonClientReviewsModule } from '@common/client-reviews/client-reviews.module';
import { CommonCompaniesModule } from '@common/companies/companies.module';
import { CommonFeatureFlagsModule } from '@common/feature-flags/feature-flags.module';
import { CommonLinkedinRecommendationsModule } from '@common/linkedin-recommendations/linkedin-recommendations.module';
import { CommonMissionsModule } from '@common/missions/missions.module';
import { CommonRBACModule } from '@common/rbac/rbac.module';
import { CommonSlackModule } from '@common/slack/slack.module';
import { CommonTalentIndustriesModule } from '@common/talent-industries/talent-industries.module';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientsModule } from '@lib/clients/clients.module';
import { GuardsModule } from '@lib/guards/guards.module';
import { MissionsModule } from '@modules/missions/missions.module';

import { ProposalAccessGuard } from './proposal-access.guard';

@Module({
  imports: [
    GuardsModule,
    MissionsModule,
    CommonUsersModule,
    ClientsModule,
    CommonCompaniesModule,
    CommonClientReviewsModule,
    CommonLinkedinRecommendationsModule,
    CommonFeatureFlagsModule,
    CommonRBACModule,
    CommonSlackModule,
    CommonAccountsModule,
    CommonClientInterviewsModule,
    CommonTalentIndustriesModule,
    CommonMissionsModule,
  ],
  controllers: [ProposalsController, AdminProposalsController],
  providers: [ProposalsService, ProposalsRepository, AdminProposalsService, ProposalsPrefillService, ProposalsPromptsService, ProposalAccessGuard],
})
export class ProposalsModule {}
