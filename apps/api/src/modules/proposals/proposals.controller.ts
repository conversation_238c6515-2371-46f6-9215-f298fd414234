import { Controller, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { apiContract, ProposalAdminReviewStatus } from '@packages/contracts';
import { tsRestHandler } from '@ts-rest/nest';
import { TsRestHandler } from '@ts-rest/nest';

import { ProposalsService } from './proposals.service';
import { SentryService } from '@lib/global/sentry.service';

import { ProposalAccessGuard, ProposalAccessRequest } from './proposal-access.guard';

@Controller()
export class ProposalsController {
  constructor(
    private readonly proposalsService: ProposalsService,
    private readonly sentryService: SentryService
  ) {}

  @UseGuards(ProposalAccessGuard)
  @TsRestHandler(apiContract.proposals.getProposal)
  getProposal(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.proposals.getProposal, async ({ params }) => {
      const result = await this.proposalsService.getProposalById(params.proposalId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/proposals/proposals.controller/getProposal] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_MISSION_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Mission not found' },
            };
          case 'FIND_PROPOSAL_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Proposal not found' },
            };
          case 'FIND_PROPOSAL_BY_ID_DB_ERROR':
          case 'GET_PROPOSAL_BY_ID_TEAM_ADVISOR_NOT_FOUND':
          case 'FIND_MISSION_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      const { proposal, roles, sharedByUser, teamAdvisorUser, missionId, accountId } = result.value;
      const sharedBy = sharedByUser
        ? {
            id: sharedByUser.id,
            firstName: sharedByUser.firstName!,
            lastName: sharedByUser.lastName!,
          }
        : null;
      const status = proposal.adminReview?.status ?? 'pending';
      const teamAdvisor = {
        id: teamAdvisorUser.id,
        firstName: teamAdvisorUser.firstName!,
        lastName: teamAdvisorUser.lastName!,
        pictureURL: teamAdvisorUser.pictureURL ?? undefined,
      };

      return {
        status: HttpStatus.OK,
        body: {
          id: proposal.id,
          missionId,
          accountId,
          status: status as ProposalAdminReviewStatus,
          roles,
          publicUntil: proposal.publicUntil,
          sharedBy,
          teamAdvisor,
        },
      };
    });
  }

  @UseGuards(ProposalAccessGuard)
  @TsRestHandler(apiContract.proposals.getProposalBuilders)
  getProposalBuilders(@Req() req: ProposalAccessRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.proposals.getProposalBuilders, async ({ params, query }) => {
      const result = await this.proposalsService.getProposalBuilders(params.proposalId, query.builderIds, req.user?.id);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/proposals/proposals.controller/getProposalBuilders] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_PROPOSAL_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Proposal not found' },
            };
          case 'FIND_MISSION_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Mission not found' },
            };
          case 'GET_PROPOSAL_BUILDERS_BUILDER_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Builder not found' },
            };
          case 'GET_PROPOSAL_BUILDERS_CANDIDATE_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Candidate not found' },
            };
          case 'FIND_PROPOSAL_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
          case 'FIND_MISSION_APPLICATION_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Mission application not found' },
            };
          case 'GET_CLIENT_REVIEWS_COUNT_DB_ERROR':
          case 'FIND_MISSION_APPLICATION_BY_ID_DB_ERROR':
          case 'FIND_CLIENT_INTERVIEWS_FOR_PROPOSAL_AND_BUILDER_DB_ERROR':
          case 'GET_LINKEDIN_RECOMMENDATIONS_COUNT_DB_ERROR':
          case 'FIND_MISSION_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @UseGuards(ProposalAccessGuard)
  @TsRestHandler(apiContract.proposals.getBuilderRecommendations)
  getBuilderRecommendations(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.proposals.getBuilderRecommendations, async ({ params, query }) => {
      const result = await this.proposalsService.getBuilderRecommendations(params.userId, query);

      if (result.isErr()) {
        const error = result.error;
        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(
            `[modules/proposals/proposals.controller/getBuilderRecommendations] - Error getting builder recommendations - ${error.code}`,
            error.originalError
          );
        }

        switch (error.code) {
          case 'GET_CLIENT_REVIEWS_BY_USER_ID_DB_ERROR':
          case 'GET_LINKEDIN_RECOMMENDATIONS_BY_USER_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Failed to get builder recommendations' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
