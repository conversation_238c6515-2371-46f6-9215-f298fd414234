import { Proposal, ProposalReviewStatus } from '@a_team/prisma';
import { Injectable, Logger } from '@nestjs/common';
import {
  CreateProposalBodyDto,
  GetTeamAdvisorsResponseDto,
  MissionDataForProposal,
  ProposalRoleApplication,
  updateProposalCandidateSectionData,
} from '@packages/contracts';
import { ok, Result, ResultAsync } from 'neverthrow';

import { CommonAccountsService } from '@common/accounts/accounts.service';
import { CommonMissionApplicationsRepository } from '@common/missions/mission-applications.repository';
import { CommonMissionApplicationsService } from '@common/missions/mission-applications.service';
import { CommonMissionsRepository } from '@common/missions/missions.repository';
import { CommonRoleCategoriesRepository } from '@common/missions/role-categories.repository';
import { CommonTalentSkillsRepository } from '@common/missions/talent-skills.repository';
import { CommonSlackService } from '@common/slack/slack.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';
import { ProposalsRepository } from '@modules/proposals/proposals.repository';

import { Errors } from '@lib/errors';

type SendProposalStatusNotificationErrorCodes = 'SEND_PROPOSAL_STATUS_NOTIFICATION_NO_USER_EMAIL';

type UpdateProposalCandidateSectionErrorCodes = 'UPDATE_PROPOSAL_CANDIDATE_SECTION_NO_CARD_SECTIONS';

type GetOwnerAndTeamAdvisorSlackTagsErrorCodes =
  | 'GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_MISSION_ACCOUNT_ID'
  | 'GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_TFS_OWNER_FOUND'
  | 'GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_TA_OR_TFS_SLACK_TAG_FOUND';

type GetMissionRoleErrorCodes = 'GET_MISSION_ROLE_NO_ROLE_FOUND';

type GetMissionDataForProposalErrorCodes = 'GET_MISSION_DATA_FOR_PROPOSAL_NO_MISSION_ACCOUNT_ID';

@Injectable()
export class AdminProposalsService {
  private WEB_BASE_URL: string;

  private getOwnerAndTeamAdvisorSlackTags = async (missionId: string) => {
    const missionResult = await this.missionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    if (!mission?.accountId) {
      return Errors.createError<GetOwnerAndTeamAdvisorSlackTagsErrorCodes>('GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_MISSION_ACCOUNT_ID', {
        isUnexpectedError: true,
      });
    }

    const ownerId = mission.owner ?? mission.bdOwners[0];

    const [teamAdvisorResult, tfsOwnerResult] = await Promise.all([
      this.commonAccountsService.getAccountTeamAdvisor(mission.accountId),
      ownerId ? this.commonUsersRepository.findUserByIdNeverthrow(ownerId) : ResultAsync.fromSafePromise(Promise.resolve(null)),
    ]);

    if (teamAdvisorResult.isErr() || tfsOwnerResult.isErr()) {
      return Errors.createError<GetOwnerAndTeamAdvisorSlackTagsErrorCodes>('GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_TFS_OWNER_FOUND', {
        isUnexpectedError: true,
      });
    }

    const teamAdvisor = teamAdvisorResult.value;
    const tfsOwner = tfsOwnerResult.value;

    const [teamAdvisorSlackTagResult, tfsOwnerSlackTagResult] = await Promise.all([
      this.slackService.getUserSlackTagByEmail(teamAdvisor.email),
      tfsOwner ? this.slackService.getUserSlackTagByEmail(tfsOwner.email) : ok(undefined),
    ]);

    if (teamAdvisorSlackTagResult.isErr() || tfsOwnerSlackTagResult.isErr()) {
      return Errors.createError<GetOwnerAndTeamAdvisorSlackTagsErrorCodes>('GET_OWNER_AND_TEAM_ADVISOR_SLACK_TAGS_NO_TA_OR_TFS_SLACK_TAG_FOUND', {
        isUnexpectedError: true,
      });
    }

    return ok({ teamAdvisorSlackTag: teamAdvisorSlackTagResult.value, tfsOwnerSlackTag: tfsOwnerSlackTagResult.value });
  };

  private sendProposalStatusNotification = async (proposal: Proposal, userId: string, status: 'created' | 'approved' | 'rejected') => {
    const user = await this.commonUsersRepository.findUserById(userId);

    if (!user?.email) {
      return Errors.createError<SendProposalStatusNotificationErrorCodes>('SEND_PROPOSAL_STATUS_NOTIFICATION_NO_USER_EMAIL');
    }

    const slackUserTagResult = await this.slackService.getUserSlackTagByEmail(user.email);

    if (slackUserTagResult.isErr()) {
      return slackUserTagResult;
    }

    const slackTagsResult = await this.getOwnerAndTeamAdvisorSlackTags(proposal.missionId!);

    if (slackTagsResult.isErr()) {
      return slackTagsResult;
    }

    const { teamAdvisorSlackTag, tfsOwnerSlackTag } = slackTagsResult.value;

    const slackMessage = [
      `<${this.WEB_BASE_URL}/proposals/${proposal.id}|Proposal> has been ${status} by ${slackUserTagResult.value}`,
      `Team advisor: ${teamAdvisorSlackTag}`,
      tfsOwnerSlackTag ? `TFS owner: ${tfsOwnerSlackTag}` : undefined,
    ]
      .filter((v) => !!v)
      .join('\n');

    const sendMessageResult = await ResultAsync.fromPromise(
      this.slackService.sendMessage({
        channel: 'teamformations',
        text: slackMessage,
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (sendMessageResult.isErr()) {
      Logger.warn(`[modules/proposals/admin-proposals.service/sendProposalStatusNotification] -`, sendMessageResult.error);
    }

    return ok();
  };

  private getMissionRole = async (missionId: string, roleId: string) => {
    const missionResult = await this.missionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const role = mission.roles.find((r) => r.id === roleId);

    if (!role) {
      return Errors.createError<GetMissionRoleErrorCodes>('GET_MISSION_ROLE_NO_ROLE_FOUND');
    }

    return ok(role);
  };

  constructor(
    private readonly proposalsRepository: ProposalsRepository,
    private readonly missionsRepository: CommonMissionsRepository,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly slackService: CommonSlackService,
    private readonly commonAccountsService: CommonAccountsService,
    private readonly commonMissionApplicationsRepository: CommonMissionApplicationsRepository,
    private readonly commonTalentSkillsRepository: CommonTalentSkillsRepository,
    private readonly commonRoleCategoriesRepository: CommonRoleCategoriesRepository,
    private readonly commonMissionApplicationsService: CommonMissionApplicationsService,
    private readonly platformServiceClient: PlatformServiceClient,
    private readonly configService: ConfigService
  ) {
    this.WEB_BASE_URL = this.configService.get('WEB_BASE_URL');
  }

  updateProposalCandidateSection = async (proposalId: string, userId: string, data: updateProposalCandidateSectionData) => {
    const candidateResult = await this.proposalsRepository.findCandidateById(proposalId, userId);

    if (candidateResult.isErr()) {
      return candidateResult;
    }

    const candidate = candidateResult.value;

    if (!candidate?.cardSections) {
      return Errors.createError<UpdateProposalCandidateSectionErrorCodes>('UPDATE_PROPOSAL_CANDIDATE_SECTION_NO_CARD_SECTIONS');
    }

    const updateCandidateResult = await this.proposalsRepository.updateCandidate(proposalId, userId, {
      cardSections: {
        ...candidate.cardSections,
        [data.section]: {
          title: data.title,
          text: data.text,
        },
      },
    });

    if (updateCandidateResult.isErr()) {
      return updateCandidateResult;
    }

    return ok(updateCandidateResult.value);
  };

  rejectProposal = async (proposalId: string, userId: string) => {
    const proposalResult = await this.proposalsRepository.findById(proposalId);

    if (proposalResult.isErr()) {
      return proposalResult;
    }

    const updateResult = await this.proposalsRepository.updateById(proposalId, {
      adminReview: {
        status: ProposalReviewStatus.rejected,
        reviewedAt: new Date(),
        reviewedBy: userId,
      },
    });

    if (updateResult.isErr()) {
      return updateResult;
    }

    const updatedProposal = updateResult.value;

    const sendProposalStatusNotificationResult = await this.sendProposalStatusNotification(updatedProposal, userId, 'rejected');

    if (sendProposalStatusNotificationResult.isErr()) {
      return sendProposalStatusNotificationResult;
    }

    return ok(updatedProposal);
  };

  approveProposal = async (proposalId: string, userId: string) => {
    const proposalResult = await this.proposalsRepository.findById(proposalId);

    if (proposalResult.isErr()) {
      return proposalResult;
    }

    const updateResult = await this.proposalsRepository.updateById(proposalId, {
      adminReview: {
        status: ProposalReviewStatus.approved,
        reviewedAt: new Date(),
        reviewedBy: userId,
      },
      sharedAt: new Date(),
      sharedByModel: { connect: { id: userId } },
    });

    if (updateResult.isErr()) {
      return updateResult;
    }

    const updatedProposal = updateResult.value;

    const sendProposalStatusNotificationResult = await this.sendProposalStatusNotification(updatedProposal, userId, 'approved');

    if (sendProposalStatusNotificationResult.isErr()) {
      return sendProposalStatusNotificationResult;
    }

    // TODO: Send email to collaborators

    return ok(updatedProposal);
  };

  setProposalPublicUntil = async (proposalId: string, publicUntil: Date | null) => {
    return await this.proposalsRepository.updateById(proposalId, {
      publicUntil,
    });
  };

  getMissionDataForProposal = async (missionId: string) => {
    const missionResult = await this.missionsRepository.findMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const rolesData: MissionDataForProposal['roles'] = [];

    const talentSkillsResult = await this.commonTalentSkillsRepository.findAllTalentSkills();

    if (talentSkillsResult.isErr()) {
      return talentSkillsResult;
    }

    const talentSkills = talentSkillsResult.value;

    if (!mission.accountId) {
      return Errors.createError<GetMissionDataForProposalErrorCodes>('GET_MISSION_DATA_FOR_PROPOSAL_NO_MISSION_ACCOUNT_ID');
    }

    const teamAdvisorResult = await this.commonAccountsService.getAccountTeamAdvisor(mission.accountId);

    if (teamAdvisorResult.isErr()) {
      return teamAdvisorResult;
    }

    const teamAdvisor = teamAdvisorResult.value;

    for (const role of mission.roles) {
      const roleCategoryResult = await this.commonRoleCategoriesRepository.findRoleCategoryById(role.categoryId);

      if (roleCategoryResult.isErr()) {
        return roleCategoryResult;
      }

      const roleCategory = roleCategoryResult.value;

      const requiredSkills = role.requiredSkills.map((skill) => talentSkills.find((s) => s.id === skill.talentSkillId)!);
      const preferredSkills = role.preferredSkills.map((skill) => talentSkills.find((s) => s.id === skill.talentSkillId)!);

      rolesData.push({
        id: role.id,
        title: roleCategory.title,
        description: role.headline,
        builderMinHourlyRate: role.builderRateMin,
        builderMaxHourlyRate: role.builderRateMax,
        minHoursPerWeek: role.availability?.weeklyHoursAvailable,
        locations: role.locations ?? [],
        requiredSkills,
        preferredSkills,
      });
    }

    return ok<MissionDataForProposal>({
      id: mission.mid,
      title: mission.title,
      teamAdvisor: {
        id: teamAdvisor.id,
        name: `${teamAdvisor.firstName} ${teamAdvisor.lastName}`,
        pictureURL: teamAdvisor.pictureURL,
      },
      roles: rolesData,
    });
  };

  getRoleApplications = async (missionId: string, roleId: string) => {
    const roleResult = await this.getMissionRole(missionId, roleId);

    if (roleResult.isErr()) {
      return roleResult;
    }

    const role = roleResult.value;

    const applicationsResult = await this.commonMissionApplicationsRepository.findMissionApplicationsForRoleWithUser(role.id);

    if (applicationsResult.isErr()) {
      return applicationsResult;
    }

    const applications = applicationsResult.value;

    const applicationsData = await Promise.all(
      applications.map(async (application) => {
        const customQuestionReply = application.customQuestionsReplies[0];
        const customQuestion = role.customQuestions.find((cq) => cq.id === customQuestionReply?.question);
        const customQuestionData =
          customQuestion && customQuestionReply
            ? {
                questionText: customQuestion.text,
                replyText: customQuestionReply.text,
              }
            : null;

        const [badgesResult, userResult] = await Promise.all([
          this.platformServiceClient.getUserBadges(application.uid),
          this.commonUsersRepository.findUserByIdNeverthrow(application.uid),
        ]);

        if (badgesResult.isErr()) {
          return badgesResult;
        }

        if (userResult.isErr()) {
          return userResult;
        }

        const [badges, user] = [badgesResult.value, userResult.value];

        const [stageResult, talentSpecializationResult] = await Promise.all([
          this.commonMissionApplicationsService.getApplicationStatusStage(application, badges),
          user?.talentProfile?.mainTalentSpecializationId
            ? this.commonRoleCategoriesRepository.findRoleCategoryById(user.talentProfile.mainTalentSpecializationId)
            : ok(null),
        ]);

        if (stageResult.isErr()) {
          return stageResult;
        }

        if (talentSpecializationResult.isErr()) {
          return talentSpecializationResult;
        }

        const [stage, talentSpecialization] = [stageResult.value, talentSpecializationResult.value];

        const title = talentSpecialization ? talentSpecialization.title : null;

        return ok<ProposalRoleApplication>({
          id: application.aid,
          stage,
          hourlyRateRange: {
            min: application.hourlyRateRange?.min,
            max: application.hourlyRateRange?.max,
          },
          monthlyRateRange: {
            min: application.monthlyRateRange?.min,
            max: application.monthlyRateRange?.max,
          },
          user: {
            id: user.id,
            name: `${user.firstName} ${user.lastName}`,
            title,
            pictureURL: user.pictureURL,
            badges,
            cvUrl: user.cvURL,
            websites: {
              personalWebsite: user.websites?.[0],
              githubUsername: user.github?.username,
              linkedinUsername: user.linkedin?.username,
            },
          },
          customQuestion: customQuestionData,
        });
      })
    );

    const combinedResult = Result.combine(applicationsData);

    if (combinedResult.isErr()) {
      return combinedResult;
    }

    return ok(combinedResult.value);
  };

  createProposal = async (data: CreateProposalBodyDto, createdById: string) => {
    const missionResult = await this.missionsRepository.findMissionById(data.missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const allApplications = data.teamMembers.map((member) => member.applicationId);
    const uniqueApplications = [...new Set(allApplications)];

    const proposalResult = await this.proposalsRepository.createProposal({
      mission: { connect: { mid: mission.mid } },
      currency: data.currency,
      createdByModel: { connect: { id: createdById } },
      schemaVersion: 'v3',
      version: 1,
      applications: uniqueApplications,
      teamAdvisor: { connect: { id: data.teamAdvisorId } },
      adminReview: {
        status: ProposalReviewStatus.pending,
      },
      rolesData: data.roles.map((role) => ({
        rid: role.roleId,
        aboutBuildersSection: role.aboutBuildersSection,
      })),
      candidates: data.teamMembers.map((member) => {
        const role = mission.roles.find((r) => r.id === member.roleId)!;

        return {
          aid: member.applicationId,
          userId: member.builderId,
          rid: member.roleId,
          roleTitle: role.headline,
          hourlyRate: member.hourlyRate,
          monthlyRate: member.monthlyRate,
          showHourlyRate: member.showHourlyRate,
          showMonthlyRate: member.showMonthlyRate,
          cvUrl: member.cvUrl,
          linkedInUrl: member.linkedInUrl,
          githubUrl: member.githubUrl,
          portfolioUrl: member.portfolioUrl,
          portfolioPassword: member.portfolioPassword,
          roleCategoryId: role.categoryId,
          includeCustomQuestionReply: member.includeCustomQuestionReply,
          cardSections: {
            requirements: {
              title: member.requirementsSectionTitle,
              text: member.requirementsSectionText,
            },
            experience: {
              title: member.experienceSectionTitle,
              text: member.experienceSectionText,
            },
            // TODO: Remove this from prisma schema
            extraSkill: {
              title: '',
              text: '',
            },
          },
          tfsPitch: {
            website: member.website,
          },
        };
      }),
    });

    if (proposalResult.isErr()) {
      return proposalResult;
    }

    const proposal = proposalResult.value;

    const sendProposalStatusNotificationResult = await this.sendProposalStatusNotification(proposal, createdById, 'created');

    if (sendProposalStatusNotificationResult.isErr()) {
      return sendProposalStatusNotificationResult;
    }

    return ok({ id: proposal.id });
  };

  getTeamAdvisors = async () => {
    const teamAdvisorsResult = await this.commonUsersRepository.findAdminUsers();

    if (teamAdvisorsResult.isErr()) {
      return teamAdvisorsResult;
    }

    const teamAdvisors = teamAdvisorsResult.value;

    const teamAdvisorsData: GetTeamAdvisorsResponseDto = teamAdvisors.map((advisor) => ({
      id: advisor.id,
      name: `${advisor.firstName} ${advisor.lastName}`,
      pictureURL: advisor.pictureURL,
    }));

    return ok(teamAdvisorsData);
  };
}
