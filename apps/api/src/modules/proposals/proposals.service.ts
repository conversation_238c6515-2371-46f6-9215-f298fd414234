import { Mission, Proposal, User } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ClientReviewData, GetBuilderRecommendationsResponse, GetProposalBuildersResponseDto, GetProposalResponseDto, ProposalRole } from '@packages/contracts';
import { ok, ResultAsync } from 'neverthrow';

import { CommonAccountsRepository } from '@common/accounts/accounts.repository';
import { CommonClientInterviewsRepository } from '@common/client-interviews/client-interviews.repository';
import { CommonClientReviewsRepository } from '@common/client-reviews/client-reviews.repository';
import { CommonCompaniesRepository } from '@common/companies/companies.repository';
import { CommonFeatureFlagsService } from '@common/feature-flags/feature-flags.service';
import { CommonLinkedinRecommendationsRepository } from '@common/linkedin-recommendations/linkedin-recommendations.repository';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { CommonTalentIndustriesRepository } from '@common/talent-industries/talent-industries.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { TalentSkillInfo } from '@modules/missions/missions.repository';
import { MissionsService } from '@modules/missions/missions.service';
import { ProposalsRepository } from '@modules/proposals/proposals.repository';

import { Errors } from '@lib/errors';
import { Pagination } from '@packages/contracts/src/lib/schemas/shared';

type GetProposalResponse = {
  proposal: Proposal;
  roles: ProposalRole[];
  sharedByUser: User | null;
  teamAdvisorUser: User;
  missionId: string;
  accountId: string;
};

type GetProposalByIdErrorCodes = 'GET_PROPOSAL_BY_ID_TEAM_ADVISOR_NOT_FOUND';

type GetProposalBuildersErrorCodes = 'GET_PROPOSAL_BUILDERS_BUILDER_NOT_FOUND' | 'GET_PROPOSAL_BUILDERS_CANDIDATE_NOT_FOUND';

@Injectable()
export class ProposalsService {
  /**
   * Helper function that groups the list of proposal's candidate into the list
   * of roles these builders are a candidate for.
   * The result is the list of roles with every role having the list its candidates
   *
   * @param   {Proposal} proposal - Proposal data
   * @returns {ProposalRole[]} - List of proposal roles with builders
   */
  // TODO: Refactor to use neverthrow after missions module is refactored
  private groupProposalCandidates = async (proposal: Proposal): Promise<ProposalRole[]> => {
    const missionResult = await this.missionsService.findMissionById(proposal.missionId!);

    if (missionResult.isErr()) {
      return [];
    }

    const mission = missionResult.value;

    const talentSkillsResult = await this.missionsService.findAllTalentSkills();

    if (talentSkillsResult.isErr()) {
      return [];
    }

    const talentSkills = talentSkillsResult.value;
    const talentSkillsMap = new Map<string, { id: string; name: string }>(talentSkills.map((ts) => [ts.id, ts]));

    const groupedData: GetProposalResponseDto['roles'] = [];
    const builders = await this.commonUsersRepository.findUsersByIds(proposal.candidates.map((c) => c.userId));

    for (const candidate of proposal.candidates) {
      if (!candidate.rid) {
        continue;
      }

      const builder = builders.find((b) => b.id === candidate.userId);
      const candidateData = {
        id: candidate.userId,
        pictureURL: builder?.pictureURL ?? undefined,
      };

      const matchingRole = groupedData.find((r) => r.id === candidate.rid);
      if (matchingRole) {
        matchingRole.builders.push(candidateData);
      } else {
        const role = mission?.roles.find((r) => r.id === candidate.rid);

        const requiredSkills = role?.requiredSkills.map((s) => talentSkillsMap.get(s.talentSkillId)!) ?? [];
        const preferredSkills = role?.preferredSkills.map((s) => talentSkillsMap.get(s.talentSkillId)!) ?? [];

        const roleCategoryResult = role?.categoryId ? await this.missionsService.findRoleCategoryById(role.categoryId) : ok(undefined);

        if (roleCategoryResult.isErr()) {
          return [];
        }

        const roleCategory = roleCategoryResult.value;
        const roleData = proposal.rolesData.find((r) => r.rid === candidate.rid);

        groupedData.push({
          id: candidate.rid,
          title: roleCategory?.title ?? '',
          description: role?.description ?? role?.headline ?? '',
          weeklyAvailability: role?.availability?.weeklyHoursAvailable,
          locations: role?.locations ?? [],
          aboutBuildersSection: roleData?.aboutBuildersSection ?? '',
          requiredSkills,
          preferredSkills,
          builders: [candidateData],
        });
      }
    }

    return groupedData;
  };

  /**
   * Service function for getting builders skills for a proposal.
   * The proposal shows only the skills that are required for the role.
   *
   * @param   {User} builder - Builder object
   * @param   {TalentSkillInfo[]} talentSkills - List of talent skills
   * @param   {string[]} roleRequiredSkills - List of required skills for the role
   * @returns {Promise<TalentSkillInfo[]>} - List of builder skills
   */
  private getProposalBuilderSkills = async (builder: User, talentSkills: TalentSkillInfo[], roleRequiredSkills: string[]) => {
    const mainTalentSkills = builder.talentProfile?.talentSkills?.mainTalentSkills ?? [];
    const sortedSkills = mainTalentSkills.sort((firstSkill, secondSkill) => (secondSkill.rating ?? 0) - (firstSkill.rating ?? 0));
    const skills = sortedSkills.map((skill) => talentSkills.find((ts) => ts.id === skill.talentSkillId)!).filter((skill) => !!skill);

    const roleRequiredSkillsSet = new Set(roleRequiredSkills);
    const shownSkills = skills.filter((skill) => roleRequiredSkillsSet.has(skill.id));

    return shownSkills;
  };

  private userHasAccessToRates = async (userId: string, accountId: string, missionId: string) => {
    const result = await this.rbacService.checkClientPermission(userId, accountId, `builderrates:${missionId}`, 'read');

    if (result.isErr()) {
      return false;
    }

    return true;
  };

  private shouldShowProposalRates = async (mission: Mission, userId: string) => {
    if (!mission.mid || !mission.accountId) {
      return false;
    }

    const isFlagOpenResult = await this.featureFlagsService.isFlagOpenForAccount('showProposalRatesInClientApp', mission.accountId);
    const isFlagOpen = isFlagOpenResult.isOk() ? isFlagOpenResult.value : false;

    const userHasAccessToRates = await this.userHasAccessToRates(userId, mission.accountId, mission.mid);

    return isFlagOpen && userHasAccessToRates;
  };

  constructor(
    private readonly proposalsRepository: ProposalsRepository,
    private readonly missionsService: MissionsService,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly clientReviewsRepository: CommonClientReviewsRepository,
    private readonly linkedinRecommendationsRepository: CommonLinkedinRecommendationsRepository,
    private readonly clientInterviewsRepository: CommonClientInterviewsRepository,
    private readonly featureFlagsService: CommonFeatureFlagsService,
    private readonly rbacService: CommonRBACService,
    private readonly commonAccountsRepository: CommonAccountsRepository,
    private readonly commonCompaniesRepository: CommonCompaniesRepository,
    private readonly talentIndustriesRepository: CommonTalentIndustriesRepository
  ) {}

  getProposalById = async (id: string) => {
    const proposalResult = await this.proposalsRepository.findById(id);

    if (proposalResult.isErr()) {
      return proposalResult;
    }

    const proposal = proposalResult.value;

    const missionResult = await this.missionsService.findMissionById(proposal.missionId!);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;
    const roles = await this.groupProposalCandidates(proposal);

    const sharedByUser = proposal.sharedBy ? await this.commonUsersRepository.findUserById(proposal.sharedBy) : null;

    if (!proposal.teamAdvisorId) {
      return Errors.createError<GetProposalByIdErrorCodes>('GET_PROPOSAL_BY_ID_TEAM_ADVISOR_NOT_FOUND', { isUnexpectedError: true });
    }

    const teamAdvisorUser = await this.commonUsersRepository.findUserById(proposal.teamAdvisorId);

    if (!teamAdvisorUser) {
      return Errors.createError<GetProposalByIdErrorCodes>('GET_PROPOSAL_BY_ID_TEAM_ADVISOR_NOT_FOUND', { isUnexpectedError: true });
    }

    return ok<GetProposalResponse>({
      proposal,
      roles,
      sharedByUser,
      teamAdvisorUser,
      missionId: mission.mid,
      accountId: mission.accountId!,
    });
  };

  /**
   * Service function for getting builders data for a proposal.
   * Most of the builder's data is fetched from users collection,
   * but few fields are overriden when creating a proposal and they are stored
   * in the candidate object (github, cvUrl, website).
   *
   * @param   {string} proposalId - Id of the proposal
   * @param   {string[]} builderIds - List of builder ids
   * @returns {Promise<GetProposalBuildersResponseDto>} - List of builders data
   */
  // TODO: Refactor to use neverthrow after missions module is refactored
  getProposalBuilders = async (proposalId: string, builderIds: string[], userId?: string) => {
    const proposalResult = await this.proposalsRepository.findById(proposalId);

    if (proposalResult.isErr()) {
      return proposalResult;
    }

    const proposal = proposalResult.value;

    const missionResult = await this.missionsService.findMissionById(proposal.missionId!);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;
    const showProposalRatesInClientApp = userId ? await this.shouldShowProposalRates(mission, userId) : false;

    const result: GetProposalBuildersResponseDto = [];

    const builders = await this.commonUsersRepository.findUsersByIds(builderIds);
    const talentSkillsResult = await this.missionsService.findAllTalentSkills();

    const talentSkills = talentSkillsResult.isOk() ? talentSkillsResult.value : [];

    for (const builderId of builderIds) {
      const builder = builders.find((b) => b.id === builderId);

      if (!builder) {
        return Errors.createError<GetProposalBuildersErrorCodes>('GET_PROPOSAL_BUILDERS_BUILDER_NOT_FOUND');
      }

      const candidate = proposal.candidates.find((c) => c.userId === builder.id);

      if (!candidate) {
        return Errors.createError<GetProposalBuildersErrorCodes>('GET_PROPOSAL_BUILDERS_CANDIDATE_NOT_FOUND');
      }

      const applicationResult = candidate.aid ? await this.missionsService.findMissionApplicationById(candidate.aid) : ok(null);

      if (applicationResult.isErr()) {
        return applicationResult;
      }

      const application = applicationResult.value;
      const role = mission.roles.find((r) => r.id === candidate.rid);
      const customQuestionReply = application?.customQuestionsReplies[0];
      const customQuestion = role?.customQuestions.find((cq) => cq.id === customQuestionReply?.question);
      const customQuestionData =
        candidate.includeCustomQuestionReply && customQuestion?.id && customQuestionReply?.text
          ? {
              id: customQuestion.id,
              questionText: customQuestion.text,
              replyText: customQuestionReply.text,
            }
          : null;

      const roleRequiredSkills = role?.requiredSkills.map((s) => s.talentSkillId) ?? [];
      const skills = await this.getProposalBuilderSkills(builder, talentSkills, roleRequiredSkills);

      const billedMinutes = builder.platformExperience?.billedMinutes ?? 0;
      const hoursWorked = Math.round(billedMinutes / 60);

      const [clientReviewsCountResult, linkedinRecommendationsCountResult] = await Promise.all([
        this.clientReviewsRepository.getClientReviewsCount(builder.id),
        this.linkedinRecommendationsRepository.getLinkedinRecommendationsCount(builder.id),
      ]);

      if (clientReviewsCountResult.isErr()) {
        return clientReviewsCountResult;
      }

      if (linkedinRecommendationsCountResult.isErr()) {
        return linkedinRecommendationsCountResult;
      }

      const clientReviewsCount = clientReviewsCountResult.value;
      const linkedinRecommendationsCount = linkedinRecommendationsCountResult.value;

      const portfolio = candidate.portfolioUrl
        ? {
            url: candidate.portfolioUrl,
            hasPassword: !!candidate.portfolioPassword,
            password: userId ? candidate.portfolioPassword : null,
          }
        : null;

      const clientInterviewsResult = userId ? await this.clientInterviewsRepository.findClientInterviewsForProposalAndBuilder(proposalId, builder.id) : ok([]);

      if (clientInterviewsResult.isErr()) {
        return clientInterviewsResult;
      }

      const clientInterviews = clientInterviewsResult.value;
      const everHadClientInterview = clientInterviews.length > 0;

      result.push({
        id: candidate.userId,
        username: builder.username,
        firstName: builder.firstName,
        lastName: builder.lastName,
        pictureURL: builder.pictureURL,
        cvUrl: candidate.cvUrl,
        yearsOfExperience: builder.yearsExperience,
        availableHoursPerWeek: application?.availability?.hoursPerWeek,
        builderRecommendationsCount: clientReviewsCount + linkedinRecommendationsCount,
        websites: {
          linkedInUrl: candidate.linkedInUrl,
          personalWebsite: candidate.tfsPitch?.website,
          githubUrl: candidate.githubUrl,
        },
        portfolio,
        customQuestion: customQuestionData,
        cardSections: candidate.cardSections,
        skills,
        location: builder.location,
        hoursWorked,
        clientHourlyRate: showProposalRatesInClientApp && candidate.showHourlyRate ? candidate.hourlyRate : undefined,
        clientMonthlyRate: showProposalRatesInClientApp && candidate.showMonthlyRate ? candidate.monthlyRate : undefined,
        everHadClientInterview,
      });
    }

    return ok(result);
  };

  getBuilderRecommendations = async (userId: string, pagination: Pagination) => {
    const [clientReviewsResult, linkedinRecommendationsResult] = await Promise.all([
      this.clientReviewsRepository.getClientReviewsByUserId(userId),
      this.linkedinRecommendationsRepository.getLinkedinRecommendationsByUserId(userId),
    ]);

    if (clientReviewsResult.isErr()) {
      return clientReviewsResult;
    }

    if (linkedinRecommendationsResult.isErr()) {
      return linkedinRecommendationsResult;
    }

    const clientReviews = clientReviewsResult.value;
    const linkedinRecommendations = linkedinRecommendationsResult.value;

    const rawCombined = [
      ...clientReviews.map((review) => ({ ...review, source: 'client' as const })),
      ...linkedinRecommendations.map((rec) => ({ ...rec, source: 'linkedin' as const })),
    ];

    rawCombined.sort((a, b) => {
      const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
      const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
      return dateB - dateA;
    });

    const totalCount = rawCombined.length;
    const offset = pagination.page * pagination.pageSize;

    const paginatedRaw = rawCombined.slice(offset, offset + pagination.pageSize);

    const combinedRecommendations: ClientReviewData[] = [];

    for (const item of paginatedRaw) {
      if (item.source === 'client') {
        const review = item;

        // TODO: Update to use neverthrow
        const [accountResult, missionResult] = await Promise.all([
          this.commonAccountsRepository.findAccountById(review.account),
          this.missionsService.findMissionById(review.mission),
        ]);

        const mission = missionResult.isOk() ? missionResult.value : undefined;
        const account = accountResult.isOk() ? accountResult.value : null;

        const role = mission?.roles.find((role) => role.id === review.rid);
        const company = account?.companyId ? await this.commonCompaniesRepository.findCompanyById(account.companyId) : null;

        const industryId = company?.enrichment?.structured?.industries?.[0];

        // TODO: Update to use neverthrow
        const [roleCategoryResult, industryResult] = await Promise.all([
          role?.categoryId ? this.missionsService.findRoleCategoryById(role.categoryId) : ok(undefined),
          industryId ? this.talentIndustriesRepository.findTalentIndustryById(industryId) : ResultAsync.fromSafePromise(Promise.resolve()),
        ]);

        const industry = industryResult.isOk() ? industryResult.value : null;
        const roleCategory = roleCategoryResult.isOk() ? roleCategoryResult.value : undefined;

        combinedRecommendations.push({
          id: review.id,
          userId: review.toUser,
          review: review.publicFeedback ?? undefined,
          source: 'client',
          company: company
            ? {
                id: company.id,
                name: company.name ?? undefined,
                logo: account?.workspace?.logo ?? undefined,
                industry: industry?.name,
              }
            : undefined,
          role: role
            ? {
                id: role.id ?? undefined,
                name: roleCategory?.title ?? undefined,
                startDate: role.availability?.date ?? undefined,
                endDate: role.availability?.scheduledEndDate ?? undefined,
              }
            : undefined,
          createdAt: review.createdAt,
        });
      } else if (item.source === 'linkedin') {
        const recommendation = item;
        const recommenderName =
          recommendation.recommender.firstName + (recommendation.recommender?.lastName?.[0] ? ` ${recommendation.recommender.lastName[0]}.` : '');

        combinedRecommendations.push({
          id: recommendation.id,
          source: 'linkedin',
          recommenderName,
          companyLogo: recommendation.recommender.position?.companyLogo ?? undefined,
          companyName: recommendation.recommender.position?.companyName ?? '',
          title: recommendation.recommender.position?.title ?? '',
          occupation: undefined,
          recommendationText: recommendation.text,
          createdAt: recommendation.createdAt,
        });
      }
    }

    return ok<GetBuilderRecommendationsResponse>({ count: totalCount, recommendations: combinedRecommendations });
  };
}
