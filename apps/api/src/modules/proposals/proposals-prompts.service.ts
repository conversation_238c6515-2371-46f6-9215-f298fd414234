import { Injectable } from '@nestjs/common';

import { LangfuseClient } from '@lib/clients/langfuse.client';

export type SectionPromptData = {
  builderProfile: string;
  builderFirstName: string;
  roleName: string;
  roleDescription: string;
};

const getSectionPromptCommonPart = (data: SectionPromptData) => {
  return `
  You are trying to present a candidate for a job. Candidate is represented in the following JSON object:
  <CANDIDATE_PROFILE_INFORMATION>
    ${data.builderProfile}
    </CANDIDATE_PROFILE_INFORMATION>

    Job role description is provided here:
    <ROLE_DESCRIPTION>
    ${data.roleDescription}
    </ROLE_DESCRIPTION>
    
    <IMPORTANT>
    - Only rely on the explicit facts in the candidate's profile.
    - Guards against deductions, inferences, or speculation about unstated qualifications.
    - Do not use personal pronouns or offensive language.
    </IMPORTANT>
    `;
};

export const PROMPT_MAP = {
  'requirements-section': (data: SectionPromptData) => `
      ${getSectionPromptCommonPart(data)}

      Summarize the candidate's fit for the role in positive way in a statement of 25-30 words, 
      based solely on the CANDIDATE_PROFILE_INFORMATION and ROLE_DESCRIPTION. 
      Highlight the candidate's relevant skills and any distinct experience that aligns with the ${data.roleName} role. 
      Do not mention that candidate does not have experience. Do not include any information not 
      explicitly stated in the profile—such as the candidate's location, contact details, or subjective interpretations. 
      Avoid general statements or extensive skill lists that go beyond the role's requirements. 
      No introductory phrasing is needed, and do not start with the word "experienced."

      Guidelines:
      - Mention only skill proficiencies mentioned in the CANDIDATE_PROFILE_INFORMATION, as well as any other specifics from the SEARCH_QUERY.
      - Exclude any details not provided in the candidate's profile or irrelevant to the ${data.roleName} position.
      - Avoid assuming roles, projects, or skills that are not detailed in the provided information.
      - Do not mention word "candidate"
      - If role description is not provided, use the candidate's profile to generate the section.
    `,
  'experience-section': (data: SectionPromptData) => `
      ${getSectionPromptCommonPart(data)}

      Highlight the role in positive way in a statement of 25-30 words, 
      based solely on the CANDIDATE_PROFILE_INFORMATION and ROLE_DESCRIPTION, 
      one of the following areas of expertise for a candidate, in the following order of priority:
        - Experience building similar products.
        - Experience working in the same industry.
        - Experience building products for the same user persona. Do not mention that candidate does not have experience. 
        Do not include any information not explicitly stated in the profile—such as the candidate's location, contact details, or subjective interpretations. 
      Avoid general statements or extensive skill lists that go beyond the role's requirements. 
      No introductory phrasing is needed, and do not start with the word "experienced."

      Guidelines:
      - Mention only skill proficiencies mentioned in the CANDIDATE_PROFILE_INFORMATION, as well as any other specifics from the SEARCH_QUERY.
      - Exclude any details not provided in the candidate's profile or irrelevant to the ${data.roleName} position.
      - Avoid assuming roles, projects, or skills that are not detailed in the provided information.
  `,
  'experience-section-title': (data: { sectionText: string; builderName: string }) => `
        Generate a sentence of 4-7 words that will summarize the following text about ${data.builderName}:
        <TEXT>
        ${data.sectionText}
        </TEXT>

        Important guidelines:
        - The sentence should start with ${data.builderName}.
        - Only rely on the explicit facts in the provided text
        - Guards against deductions, inferences, or speculation about unstated qualifications.
        - Do not use personal pronouns or offensive language.
        - Do not put point at the end. 
  `,
  'about-builders-section': (data: { roleName: string; companyName: string }) => `
      Generate a paragraph of up to 60 words that will describe how the builders are
      fit for the role '${data.roleName}' at ${data.companyName}.
      Use the following information to generate the paragraph:
        Important guidelines:
        - The sentence should start with 'The following builders'.
        - Do not mention anyone by name.
        - Do not use personal pronouns or offensive language.
  `,
} as const;

export type PromptNames = 'requirements-section' | 'experience-section' | 'experience-section-title' | 'about-builders-section';

export type PromptVariablesType =
  | { promptName: 'requirements-section'; variables: SectionPromptData }
  | { promptName: 'experience-section'; variables: SectionPromptData }
  | { promptName: 'experience-section-title'; variables: { sectionText: string; builderName: string } }
  | { promptName: 'about-builders-section'; variables: { roleName: string; companyName: string } };

@Injectable()
export class ProposalsPromptsService {
  private getFallbackPrompt = (promptName: PromptNames, variables: PromptVariablesType['variables']): string => {
    const promptFunction = PROMPT_MAP[promptName] as (data: PromptVariablesType['variables']) => string;

    return promptFunction(variables);
  };

  constructor(private readonly langfuseClient: LangfuseClient) {}

  /**
   * Get a prompt from langfuse or use the prompt function if there is an error or the prompt is not found.
   *
   * @param   {PromptVariablesType} data - The data to get the prompt for with prompt name and variables.
   * @returns {Promise<string>} - The prompt.
   */
  getPrompt = async (data: PromptVariablesType): Promise<string> => {
    const promptResult = await this.langfuseClient.getPrompt(data.promptName, data.variables);

    if (promptResult.isErr()) {
      return this.getFallbackPrompt(data.promptName, data.variables);
    }

    return promptResult.value;
  };
}
