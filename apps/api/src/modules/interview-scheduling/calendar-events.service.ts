import { Injectable } from '@nestjs/common';
import { CreateCalendarEventRequestDto, InterviewWebhookPayload, WebhookTriggerEvent } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CalendarAvailabilitiesService } from './calendar-availabilities.service';
import { CalendarEventTypesRepository } from './calendar-event-types.repository';
import { CalendarEventsRepository } from './calendar-events.repository';
import { CalendarsRepository } from './calendars.repository';
import { CalendarsService } from './calendars.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ClientAppV1Client } from '@lib/clients/client-app-v1.client';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';
import { SentryService } from '@lib/global/sentry.service';

import { Errors } from '@lib/errors';

import { TimezoneHelper } from './helpers/timezone';

type CreateCalendarEventErrorCodes = 'USER_NOT_AVAILABLE_IN_GIVEN_SLOT' | 'SHARED_CALENDAR_NOT_SUPPORTED';

type AcceptCalendarEventErrorCodes = 'ORGANIZER_NOT_FOUND';

@Injectable()
export class CalendarEventsService {
  constructor(
    private readonly clientAppV1Client: ClientAppV1Client,
    private readonly calendarAvailabilitiesService: CalendarAvailabilitiesService,
    private readonly calendarsService: CalendarsService,
    private readonly calendarEventsRepository: CalendarEventsRepository,
    private readonly calendarEventTypesRepository: CalendarEventTypesRepository,
    private readonly calendarsRepository: CalendarsRepository,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly sentryService: SentryService,
    private readonly platformServiceClient: PlatformServiceClient
  ) {}

  scheduleEvent = async (eventRequest: CreateCalendarEventRequestDto) => {
    const eventTypeResult = await this.calendarEventTypesRepository.findCalendarEventTypeById(eventRequest.eventTypeId);

    if (eventTypeResult.isErr()) {
      return eventTypeResult;
    }

    const eventType = eventTypeResult.value;

    /**
     * Check if the user is available within the slot (double confirmation)
     */
    const userAvailabilityResult = await this.calendarAvailabilitiesService.getAvailabilitySlots(
      eventRequest.userId,
      eventRequest.eventTypeId,
      TimezoneHelper.toStartOfDay(eventRequest.start, eventRequest.timezone),
      TimezoneHelper.toEndOfDay(eventRequest.end, eventRequest.timezone)
    );

    if (userAvailabilityResult.isErr()) {
      return userAvailabilityResult;
    }

    const userAvailability = userAvailabilityResult.value;

    if (!userAvailability.availabilities.length) {
      return Errors.createError<CreateCalendarEventErrorCodes>('USER_NOT_AVAILABLE_IN_GIVEN_SLOT');
    }

    const isUserAvailableInSlot = userAvailability.availabilities.some((availability) => {
      return availability.utcStart <= eventRequest.start && availability.utcEnd >= eventRequest.end;
    });

    if (!isUserAvailableInSlot) {
      return Errors.createError<CreateCalendarEventErrorCodes>('USER_NOT_AVAILABLE_IN_GIVEN_SLOT');
    }

    if (!isUserAvailableInSlot) {
      return Errors.createError<CreateCalendarEventErrorCodes>('USER_NOT_AVAILABLE_IN_GIVEN_SLOT');
    }

    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(eventRequest.userId);

    if (userResult.isErr()) {
      return userResult;
    }

    /**
     * TODO: https://buildateam.atlassian.net/browse/CORE-740
     * If a user is using shared calendar, we'll have to get the shared calendar user id.
     * For now, we don't have one, but i'll remove this in a follow up and wire up an actual user
     * that's tied to `<EMAIL>` calendar
     */
    if (userResult.value.isUsingSharedCalendar) {
      return Errors.createError<CreateCalendarEventErrorCodes>('SHARED_CALENDAR_NOT_SUPPORTED');
    }

    const calendarOwner = eventRequest.userId;

    const calendarResult = await this.calendarsRepository.findCalendarByUserId(calendarOwner);

    if (calendarResult.isErr()) {
      return calendarResult;
    }

    const calendar = calendarResult.value;
    const status = eventType.autoConfirm ? 'created' : 'requested';
    let webhookTrigger: WebhookTriggerEvent = `booking.${status}`;

    const organizer = {
      id: calendarOwner,
      email: userResult.value.email,
      name: `${userResult.value.firstName} ${userResult.value.lastName}`,
      username: userResult.value.username,
      timezone: userAvailability.timezone,
    };

    const title = `${eventType.title} between ${organizer.name} and ${eventRequest.attendee.name}`;
    const description = `${eventType.description}`;

    const calendarEventResult = await this.calendarEventsRepository.createCalendarEventByUserAndEventTypeId({
      eventTypeId: eventRequest.eventTypeId,
      eventOwnerId: calendarOwner,
      calendarId: calendar.id,
      title,
      description,
      startTime: eventRequest.start,
      endTime: eventRequest.end,
      status,
      attendees: [
        {
          name: organizer.name,
          email: organizer.email,
          emailFrom: organizer.email,
          timezone: organizer.timezone,
          recordingOptOut: false,
        },
        {
          name: eventRequest.attendee.name,
          email: eventRequest.attendee.email,
          emailFrom: eventRequest.attendee.email,
          timezone: eventRequest.timezone,
          recordingOptOut: eventRequest.attendee.recordingOptOut,
        },
        ...eventRequest.attendee.guests.map((guest) => ({
          name: guest,
          email: guest,
          emailFrom: guest,
          timezone: eventRequest.timezone,
          recordingOptOut: eventRequest.attendee.recordingOptOut,
          isGuest: true,
        })),
      ],
      metadata: eventRequest.metadata,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    if (calendarEventResult.isErr()) {
      return calendarEventResult;
    }

    const eventDataResult = eventType.autoConfirm
      ? await this.calendarsService.createEvent(organizer.id, {
          summary: title,
          description,
          start: {
            dateTime: eventRequest.start,
            timezone: organizer.timezone, // Event will always be created on organizer's timezone
          },
          end: {
            dateTime: eventRequest.start,
            timezone: organizer.timezone,
          },
          attendees: calendarEventResult.value.attendees
            .filter(({ email }) => email !== organizer.email)
            .map((attendee) => ({
              displayName: attendee.name,
              email: attendee.email,
              organizer: false,
              responseStatus: 'accepted',
            })),
          organizer: {
            email: organizer.email,
            displayName: organizer.name,
          },
        })
      : ok(undefined);

    if (eventDataResult.isErr()) {
      return eventDataResult;
    }

    const calendarEvent = eventDataResult.value;

    const videoCallData = calendarEvent
      ? {
          id: calendarEvent.callId,
          type: calendarEvent.callType,
          url: calendarEvent.callUrl,
        }
      : null;
    const updatedStatus = calendarEvent?.calendarEventId ? 'accepted' : status;
    webhookTrigger = `booking.${updatedStatus}`;

    const updateEventResult = await this.calendarEventsRepository.updateCalendarEventById(calendarEventResult.value.id, {
      status: updatedStatus,
      calendarEventId: calendarEvent?.calendarEventId,
      ...(videoCallData ? { videoCallData } : undefined),
      updatedAt: new Date(),
    });

    if (updateEventResult.isErr()) {
      return updateEventResult;
    }

    const webhookPayload: InterviewWebhookPayload = {
      triggerEvent: webhookTrigger,
      payload: {
        ...updateEventResult.value,
        organizer: {
          id: organizer.id,
          name: organizer.name,
          email: organizer.email,
          timezone: organizer.timezone,
          recordingOptOut: false,
        },
      },
    };

    const clientAppWebhookResult = await this.clientAppV1Client.pingInterviewSchedulingWebhook(eventType.type, webhookPayload);
    const platformWebhookResult = await this.platformServiceClient.pingInterviewSchedulingWebhook(eventType.type, webhookPayload);

    /**
     * We can discuss if this should be a breaking error or not.
     * For now i'd like to simply sentry the error if unexpected and not break the flow.
     */
    if (clientAppWebhookResult.isErr()) {
      const error = clientAppWebhookResult.error;

      if (error.isUnexpectedError) {
        this.sentryService.logAndCaptureError(`[modules/calendar-events/calendar-events.service/scheduleEvent] - ${error.code}`, error.originalError);
      }
    }

    if (platformWebhookResult.isErr()) {
      const error = platformWebhookResult.error;

      if (error.isUnexpectedError) {
        this.sentryService.logAndCaptureError(`[modules/calendar-events/calendar-events.service/scheduleEvent] - ${error.code}`, error.originalError);
      }
    }

    return ok(webhookPayload.payload);
  };

  acceptEvent = async (eventId: string) => {
    const calendarEventResult = await this.calendarEventsRepository.findCalendarEventById(eventId);

    if (calendarEventResult.isErr()) {
      return calendarEventResult;
    }

    const calendarEvent = calendarEventResult.value;

    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(calendarEvent.eventOwnerId);

    if (userResult.isErr()) {
      return userResult;
    }

    const eventTypeResult = await this.calendarEventTypesRepository.findCalendarEventTypeById(calendarEvent.eventTypeId);

    if (eventTypeResult.isErr()) {
      return eventTypeResult;
    }

    const eventType = eventTypeResult.value;

    const organizerUser = calendarEvent.attendees.find((attendee) => attendee.email === userResult.value.email);

    if (!organizerUser) {
      return Errors.createError<AcceptCalendarEventErrorCodes>('ORGANIZER_NOT_FOUND');
    }

    const organizer = {
      id: calendarEvent.eventOwnerId,
      email: userResult.value.email,
      name: `${userResult.value.firstName} ${userResult.value.lastName}`,
      username: userResult.value.username,
      timezone: organizerUser.timezone,
    };

    const eventDataResult = await this.calendarsService.createEvent(organizer.id, {
      summary: calendarEvent.title ?? 'Interview',
      description: calendarEvent.description,
      start: {
        dateTime: calendarEvent.startTime,
        timezone: organizer.timezone ?? '', // Event will always be created on organizer's timezone
      },
      end: {
        dateTime: calendarEvent.endTime,
        timezone: organizer.timezone ?? '',
      },
      attendees: calendarEventResult.value.attendees
        .filter(({ email }) => email !== organizer.email)
        .map((attendee) => ({
          displayName: attendee.name,
          email: attendee.email,
          organizer: false,
          responseStatus: 'accepted',
        })),
      organizer: {
        email: organizer.email,
        displayName: organizer.name,
      },
    });

    if (eventDataResult.isErr()) {
      return eventDataResult;
    }

    const evebntData = eventDataResult.value;

    const videoCallData = calendarEvent
      ? {
          id: evebntData.callId,
          type: evebntData.callType,
          url: evebntData.callUrl,
        }
      : null;

    const updatedStatus = 'accepted';
    const webhookTrigger = `booking.${updatedStatus}`;

    const updateEventResult = await this.calendarEventsRepository.updateCalendarEventById(calendarEventResult.value.id, {
      status: updatedStatus,
      calendarEventId: calendarEvent?.calendarEventId,
      ...(videoCallData ? { videoCallData } : undefined),
      updatedAt: new Date(),
    });

    if (updateEventResult.isErr()) {
      return updateEventResult;
    }

    const webhookPayload: InterviewWebhookPayload = {
      triggerEvent: webhookTrigger,
      payload: {
        ...updateEventResult.value,
        organizer: {
          id: organizer.id,
          name: organizer.name,
          email: organizer.email,
          timezone: organizer.timezone ?? '',
          recordingOptOut: false,
        },
      },
    };

    const clientAppWebhookResult = await this.clientAppV1Client.pingInterviewSchedulingWebhook(eventType.type, webhookPayload);
    const platformWebhookResult = await this.platformServiceClient.pingInterviewSchedulingWebhook(eventType.type, webhookPayload);

    /**
     * We can discuss if this should be a breaking error or not.
     * For now i'd like to simply sentry the error if unexpected and not break the flow.
     */
    if (clientAppWebhookResult.isErr()) {
      const error = clientAppWebhookResult.error;

      if (error.isUnexpectedError) {
        this.sentryService.logAndCaptureError(`[modules/calendar-events/calendar-events.service/scheduleEvent] - ${error.code}`, error.originalError);
      }
    }

    if (platformWebhookResult.isErr()) {
      const error = platformWebhookResult.error;

      if (error.isUnexpectedError) {
        this.sentryService.logAndCaptureError(`[modules/calendar-events/calendar-events.service/scheduleEvent] - ${error.code}`, error.originalError);
      }
    }

    return ok(webhookPayload.payload);
  };
}
