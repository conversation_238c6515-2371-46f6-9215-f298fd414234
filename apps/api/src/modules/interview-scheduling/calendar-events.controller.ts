import { Controller, HttpStatus, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON><PERSON><PERSON>, TsRestHandler } from '@ts-rest/nest';

import { CalendarEventsService } from './calendar-events.service';
import { SentryService } from '@lib/global/sentry.service';

import { JwtGuard } from '@lib/guards/jwt.guard';

@Controller()
@UseGuards(JwtGuard)
export class CalendarEventsController {
  constructor(
    private readonly calendarEventsService: CalendarEventsService,
    private readonly sentryService: SentryService
  ) {}

  @TsRestHandler(apiContract.calendarEvents.createCalendarEvent)
  createCalendarEvent(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendarEvents.createCalendarEvent, async ({ body }) => {
      const result = await this.calendarEventsService.scheduleEvent(body);

      if (result.isErr()) {
        const error = result.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(
            `[modules/calendar-events/calendar-events.controller/createCalendarEvent] - ${error.code}`,
            error.originalError
          );
        }

        switch (error.code) {
          case 'USER_NOT_AVAILABLE_IN_GIVEN_SLOT':
            return {
              status: HttpStatus.BAD_REQUEST,
              body: { message: 'User is not available in the given slot' },
            };
          case 'SHARED_CALENDAR_NOT_SUPPORTED':
            return {
              status: HttpStatus.BAD_REQUEST,
              body: { message: 'Shared calendar is not supported' },
            };
          default:
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.CREATED,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.calendarEvents.acceptCalendarEvent)
  acceptCalendarEvent(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendarEvents.acceptCalendarEvent, async ({ params }) => {
      const result = await this.calendarEventsService.acceptEvent(params.id);

      if (result.isErr()) {
        const error = result.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(
            `[modules/calendar-events/calendar-events.controller/acceptCalendarEvent] - ${error.code}`,
            error.originalError
          );
        }

        switch (error.code) {
          case 'UPDATE_CALENDAR_EVENT_BY_ID_DB_ERROR':
          default:
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
