import { Injectable } from '@nestjs/common';
import { CreateOrUpdateCalendarAvailabilityDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CalendarAvailabilitiesRepository } from './calendar-availabilities.repository';
import { CalendarEventTypesRepository } from './calendar-event-types.repository';
import { CalendarsService } from './calendars.service';
import { CommonUsersRepository } from '@common/users/users.repository';

import { AvailabilityCalculator } from './helpers/availability-calculator';

@Injectable()
export class CalendarAvailabilitiesService {
  constructor(
    private readonly calendarService: CalendarsService,
    protected readonly commonUsersRepository: CommonUsersRepository,
    private readonly calendarAvailabilitiesRepository: CalendarAvailabilitiesRepository,
    private readonly calendarEventTypesRepository: CalendarEventTypesRepository
  ) {}

  /**
   * Create a new calendar availability preference for a user and event type
   * @param userId ID of the user
   * @param eventTypeId ID of the event type
   * @param data Availability preference data
   * @returns Created availability preference
   */
  createCalendarAvailability = async (userId: string, eventTypeId: string, data: CreateOrUpdateCalendarAvailabilityDto) => {
    const result = await this.calendarAvailabilitiesRepository.createCalendarAvailabilityByUserAndEventTypeId({
      ...data,
      userId,
      eventTypeId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    if (result.isErr()) {
      return result;
    }

    return result;
  };

  /**
   * Get a calendar availability preference by ID
   * @param id ID of the availability preference
   * @returns Availability preference
   */
  getCalendarAvailability = async (id: string) => {
    const result = await this.calendarAvailabilitiesRepository.findCalendarAvailabilityById(id);

    if (result.isErr()) {
      return result;
    }

    return result;
  };

  /**
   * Get a calendar availability preference by user and event type ID
   * @param userId ID of the user
   * @param eventTypeId ID of the event type
   * @returns Availability preference
   */
  getCalendarAvailabilityByUserAndEventTypeId = async (userId: string, eventTypeId: string) => {
    const result = await this.calendarAvailabilitiesRepository.findCalendarAvailabilitiesByUserAndEventTypeId(userId, eventTypeId);

    if (result.isErr()) {
      return result;
    }

    return result;
  };

  /**
   * Update calendar availability preference
   * @param id ID of the availability preference
   * @param data Availability preference data
   * @returns Updated availability preference
   */
  updateCalendarAvailability = async (id: string, data: CreateOrUpdateCalendarAvailabilityDto) => {
    const result = await this.calendarAvailabilitiesRepository.updateCalendarAvailabilityById(id, data);

    if (result.isErr()) {
      return result;
    }

    return result;
  };

  /**
   * Delete calendar availability preference
   * @param id ID of the availability preference
   * @returns Deleted availability preference
   */
  deleteCalendarAvailability = async (id: string) => {
    const result = await this.calendarAvailabilitiesRepository.deleteCalendarAvailabilityById(id);

    if (result.isErr()) {
      return result;
    }

    return result;
  };

  /**
   * Get available time slots for a user and event type within a specified time range
   * @param userId ID of the user
   * @param eventTypeId ID of the event type
   * @param utcStartTimeString Start of the time range in ISO string format
   * @param utcEndTimeString End of the time range in ISO string format
   * @returns Available time slots and timezone information
   */
  getAvailabilitySlots = async (userId: string, eventTypeId: string, utcStartTimeString: string, utcEndTimeString: string) => {
    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(userId);

    if (userResult.isErr()) {
      return userResult;
    }
    const user = userResult.value;

    const eventTypeResult = await this.calendarEventTypesRepository.findCalendarEventTypeById(eventTypeId);

    if (eventTypeResult.isErr()) {
      return eventTypeResult;
    }

    const eventType = eventTypeResult.value;

    /**
     * If user is using shared calendar - we can't query their calendar,
     * so we only use user's availability settings and simply return empty array telling that user has no busy slots.
     * See: https://www.notion.so/ateams/Tech-Spec-Call-Scheduling-using-Google-Calendar-API-1ba373751adf80cf9afdfaa9f36d1f07?pvs=4#1bd373751adf803caa67d794c3cc8162
     */
    const busySlotsResult = user.isUsingSharedCalendar
      ? ok({
          slots: [],
          timezone: user.timezone?.name ?? 'UTC',
        })
      : await this.calendarService.getBusySlots(userId, utcStartTimeString, utcEndTimeString);

    if (busySlotsResult.isErr()) {
      return busySlotsResult;
    }

    const availabilityPreferencesResult = await this.calendarAvailabilitiesRepository.findCalendarAvailabilitiesByUserAndEventTypeId(userId, eventTypeId);

    if (availabilityPreferencesResult.isErr()) {
      return availabilityPreferencesResult;
    }

    const availabilityPreferences = availabilityPreferencesResult.value;
    const { slots: busySlots, timezone } = busySlotsResult.value;

    const availabilities = AvailabilityCalculator.calculateAvailabilitySlots(
      { utcStartTimeString, utcEndTimeString },
      busySlots,
      availabilityPreferences,
      eventType,
      timezone
    );

    return ok({
      timezone,
      availabilities,
    });
  };
}
