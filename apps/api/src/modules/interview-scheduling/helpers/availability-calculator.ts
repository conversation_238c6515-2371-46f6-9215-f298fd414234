import { toDate } from 'date-fns-tz';

import { TimezoneHelper } from './timezone';

/**
 * Represents a time period where the user is busy/unavailable
 * @property utcStart - ISO string representing the start time in UTC
 * @property utcEnd - ISO string representing the end time in UTC
 */
type BusySlot = {
  utcStart: string;
  utcEnd: string;
};

/**
 * Parsed representation of a busy slot with Date objects
 * @property start - Start time as Date object
 * @property end - End time as Date object
 */
type ParsedBusySlot = {
  start: Date;
  end: Date;
};

/**
 * Represents a user's availability preference for specific days or dates
 * @property days - Array of day numbers (0-6, Sunday-Saturday)
 * @property date - Optional specific date this preference applies to
 * @property startTime - Start time in format "HH:MM:SS"
 * @property endTime - End time in format "HH:MM:SS"
 */
type AvailabilityPreference = {
  days: number[];
  date?: Date | null;
  startTime: string;
  endTime: string;
};

/**
 * Details about a calendar event type
 * @property bookingTime - Duration of the event in minutes
 * @property beforeEventBuffer - Buffer time before events in minutes
 * @property afterEventBuffer - Buffer time after events in minutes
 */
type CalendarEventType = {
  bookingTime: number;
  beforeEventBuffer: number;
  afterEventBuffer: number;
};

/**
 * Represents a time window to search for availability
 * @property utcStartTimeString - ISO string representing the start time in UTC
 * @property utcEndTimeString - ISO string representing the end time in UTC
 */
type AvailabilityWindow = {
  utcStartTimeString: string;
  utcEndTimeString: string;
};

/**
 * Represents an available time slot that can be booked
 * @property utcStart - ISO string representing the start time in UTC
 * @property utcEnd - ISO string representing the end time in UTC
 */
type AvailableSlot = {
  utcStart: string;
  utcEnd: string;
};

const MINUTES_IN_MS = 60000;

/**
 * Helper class for calculating availability slots based on user preferences and busy periods
 * Provides static methods to handle various aspects of availability calculation
 * including finding available slots, filtering preferences, and processing time windows
 */
export class AvailabilityCalculator {
  /**
   * Creates a single availability slot at the given start time
   * @param startTime Start time for the slot
   * @param callTime Duration of the event in minutes
   * @returns An available slot object
   */
  private static createSlot(startTime: Date, callTime: number): AvailableSlot {
    const endTime = toDate(startTime.getTime() + callTime * MINUTES_IN_MS, { timeZone: 'UTC' });

    return {
      utcStart: startTime.toISOString(),
      utcEnd: endTime.toISOString(),
    };
  }

  /**
   * Calculate the next potential slot start time based on the current slot's end time and buffers
   * @param currentEnd End time of the current slot
   * @param afterBuffer Buffer time after events in minutes
   * @param beforeBuffer Buffer time before events in minutes
   * @returns Date object representing the next potential slot start time
   */
  private static calculateNextSlotStart(currentEnd: Date, afterBuffer: number, beforeBuffer: number): Date {
    return toDate(currentEnd.getTime() + (afterBuffer + beforeBuffer) * MINUTES_IN_MS, { timeZone: 'UTC' });
  }

  /**
   * Find available slots within a window with no busy periods
   * @param windowStart Start time of the availability window
   * @param windowEnd End time of the availability window
   * @param callTime Duration of the event in minutes
   * @param beforeBuffer Buffer time before events in minutes
   * @param afterBuffer Buffer time after events in minutes
   * @returns Array of available slots within the window
   */
  private static findSlotsInEmptyWindow(windowStart: Date, windowEnd: Date, callTime: number, beforeBuffer: number, afterBuffer: number): AvailableSlot[] {
    const availableSlots: AvailableSlot[] = [];
    let potentialStart = toDate(windowStart, { timeZone: 'UTC' });

    while (true) {
      // Calculate slot end time
      const potentialEnd = toDate(potentialStart.getTime() + callTime * MINUTES_IN_MS, { timeZone: 'UTC' });

      // Check if this slot (including after buffer) fits within the availability window
      if (potentialEnd.getTime() + afterBuffer * MINUTES_IN_MS > windowEnd.getTime()) {
        break; // We've reached the end of the availability window
      }

      // Add this slot
      availableSlots.push(this.createSlot(potentialStart, callTime));

      // Move to next potential slot start
      potentialStart = this.calculateNextSlotStart(potentialEnd, afterBuffer, beforeBuffer);
    }

    return availableSlots;
  }

  /**
   * Find available slots before a specific busy period
   * @param startTime Start time to begin searching from
   * @param busyStart Start time of the busy period
   * @param callTime Duration of the event in minutes
   * @param beforeBuffer Buffer time before events in minutes
   * @param afterBuffer Buffer time after events in minutes
   * @returns Array of available slots and the next potential start time
   */
  private static findSlotBeforeBusyPeriod(
    startTime: Date,
    busyStart: Date,
    callTime: number,
    beforeBuffer: number,
    afterBuffer: number
  ): { slots: AvailableSlot[]; nextStart: Date } {
    const slots: AvailableSlot[] = [];
    let nextStart = toDate(startTime, { timeZone: 'UTC' });

    while (true) {
      // Calculate slot end time
      const potentialEnd = toDate(nextStart.getTime() + callTime * MINUTES_IN_MS, { timeZone: 'UTC' });

      // Check if slot (including after buffer) would overlap with busy slot
      if (potentialEnd.getTime() + afterBuffer * MINUTES_IN_MS > busyStart.getTime()) {
        break; // No more slots fit before this busy period
      }

      // Add this slot
      slots.push(this.createSlot(nextStart, callTime));

      // Move to next potential slot start
      nextStart = this.calculateNextSlotStart(potentialEnd, afterBuffer, beforeBuffer);
    }

    return { slots, nextStart };
  }

  /**
   * Find available slots within a specific time window, considering busy periods
   * @param windowStart Start time of the availability window
   * @param windowEnd End time of the availability window
   * @param busySlots Array of busy time periods that are unavailable
   * @param callTime Duration of the event in minutes
   * @param beforeBuffer Buffer time before events in minutes
   * @param afterBuffer Buffer time after events in minutes
   * @returns Array of available slots within the window
   */
  private static findAvailableSlotsInWindow(
    windowStart: Date,
    windowEnd: Date,
    busySlots: ParsedBusySlot[],
    callTime: number,
    beforeBuffer: number,
    afterBuffer: number
  ): AvailableSlot[] {
    // If no busy slots in this window, use the simplified algorithm
    if (busySlots.length === 0) {
      return this.findSlotsInEmptyWindow(windowStart, windowEnd, callTime, beforeBuffer, afterBuffer);
    }

    const availableSlots: AvailableSlot[] = [];
    let nextStart = toDate(windowStart, { timeZone: 'UTC' });

    // Process slots around busy periods
    for (const busySlot of busySlots) {
      // Find slots before this busy period
      const { slots } = this.findSlotBeforeBusyPeriod(nextStart, busySlot.start, callTime, beforeBuffer, afterBuffer);

      availableSlots.push(...slots);

      // Update nextStart to be after this busy period (including buffers)
      nextStart = toDate(busySlot.end.getTime() + (afterBuffer + beforeBuffer) * MINUTES_IN_MS, { timeZone: 'UTC' });
    }

    // Find slots after the last busy period
    while (true) {
      // Calculate slot end time
      const potentialEnd = toDate(nextStart.getTime() + callTime * MINUTES_IN_MS, { timeZone: 'UTC' });

      // Check if this slot (including after buffer) fits within the availability window
      if (potentialEnd.getTime() + afterBuffer * MINUTES_IN_MS > windowEnd.getTime()) {
        break; // We've reached the end of the availability window
      }

      // Add this slot
      availableSlots.push(this.createSlot(nextStart, callTime));

      // Move to next potential slot start
      nextStart = this.calculateNextSlotStart(potentialEnd, afterBuffer, beforeBuffer);
    }

    return availableSlots;
  }

  /**
   * Find applicable availability preferences for a given day
   * @param currentDate The date to find preferences for
   * @param availabilityPreferences All available preferences
   * @returns Array of applicable preferences sorted by start time
   */
  private static findApplicablePreferences(currentDate: Date, availabilityPreferences: AvailabilityPreference[]): AvailabilityPreference[] {
    const currentDay = currentDate.getUTCDay(); // 0-6 (Sunday-Saturday)
    const dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD

    /**
     * First find the date specific preference.
     * Basically if a user has provided a specific override for this particular day.
     */
    const datePreference = availabilityPreferences.find((pref) => pref.date && pref.date.toISOString().split('T')[0] === dateString);

    /**
     * We need to now create an availability preference that we can apply to the busy slots
     * NOTE: date-specific preference takes priority over day of the week preference
     */
    let applicablePreferences: AvailabilityPreference[] = [];

    if (datePreference) {
      applicablePreferences.push(datePreference);
    } else {
      applicablePreferences = availabilityPreferences.filter((pref) => pref.days && pref.days.includes(currentDay));
    }

    // Sort availability preferences by start time
    return this.sortPreferencesByStartTime(applicablePreferences);
  }

  /**
   * Sort availability preferences by their start time
   * @param preferences Array of preferences to sort
   * @returns Sorted array of preferences
   */
  private static sortPreferencesByStartTime(preferences: AvailabilityPreference[]): AvailabilityPreference[] {
    return [...preferences].sort((a, b) => {
      const aHours = parseInt(a.startTime.split(':')[0]);
      const bHours = parseInt(b.startTime.split(':')[0]);
      if (aHours !== bHours) {
        return aHours - bHours;
      }

      const aMinutes = parseInt(a.startTime.split(':')[1]);
      const bMinutes = parseInt(b.startTime.split(':')[1]);
      return aMinutes - bMinutes;
    });
  }

  /**
   * Check if a date has unavailability marked in preferences
   * @param currentDate Date to check
   * @param availabilityPreferences All availability preferences
   * @returns True if the date is marked as unavailable
   */
  private static isDateMarkedUnavailable(currentDate: Date, availabilityPreferences: AvailabilityPreference[]): boolean {
    const dateString = currentDate.toISOString().split('T')[0]; // YYYY-MM-DD

    const datePreference = availabilityPreferences.find((pref) => pref.date && pref.date.toISOString().split('T')[0] === dateString);

    return !!(datePreference && datePreference.startTime === '00:00:00' && datePreference.endTime === '00:00:00');
  }

  /**
   * Filter busy slots to find only those relevant to a specific time window
   * @param busySlots All busy slots
   * @param effectiveStart Start of time window
   * @param effectiveEnd End of time window
   * @returns Array of busy slots that overlap with the time window
   */
  private static filterRelevantBusySlots(busySlots: ParsedBusySlot[], effectiveStart: Date, effectiveEnd: Date): ParsedBusySlot[] {
    return busySlots.filter((slot) => slot.start < effectiveEnd && slot.end > effectiveStart);
  }

  /**
   * Process a single preference window within a day
   * @param currentDate Current day being processed
   * @param pref Availability preference to process
   * @param windowStartDate Start date of the overall availability window
   * @param windowEndDate End date of the overall availability window
   * @param parsedBusySlots All parsed busy slots
   * @param eventType Calendar event type details
   * @param userTimezone User's timezone
   * @returns Array of available slots for this preference window
   */
  private static processPreferenceWindow(
    currentDate: Date,
    pref: AvailabilityPreference,
    windowStartDate: Date,
    windowEndDate: Date,
    parsedBusySlots: ParsedBusySlot[],
    eventType: CalendarEventType,
    userTimezone: string
  ): AvailableSlot[] {
    const { bookingTime, beforeEventBuffer, afterEventBuffer } = eventType;

    // Convert local preference time to UTC
    const { utcStart: availabilityStart, utcEnd: availabilityEnd } = TimezoneHelper.availabilityPreferenceToUtc(
      currentDate,
      pref.startTime,
      pref.endTime,
      userTimezone
    );

    // Skip if this availability window is outside our search range
    if (availabilityEnd <= windowStartDate || availabilityStart >= windowEndDate) {
      return [];
    }

    // Clamp the effective start/end times to the overall window boundaries
    const effectiveStart = availabilityStart < windowStartDate ? windowStartDate : availabilityStart;
    const effectiveEnd = availabilityEnd > windowEndDate ? windowEndDate : availabilityEnd;

    // Skip this preference window if it's too small for even one slot
    if (effectiveStart.getTime() + bookingTime * MINUTES_IN_MS > effectiveEnd.getTime()) {
      return [];
    }

    // Filter busy slots to only those that overlap with this availability window
    const relevantBusySlots = this.filterRelevantBusySlots(parsedBusySlots, effectiveStart, effectiveEnd);

    // Find available slots in this window
    return this.findAvailableSlotsInWindow(effectiveStart, effectiveEnd, relevantBusySlots, bookingTime, beforeEventBuffer, afterEventBuffer);
  }

  /**
   * Process a single day to find available slots
   * @param currentDate The day to process
   * @param windowStartDate Start date of the overall availability window
   * @param windowEndDate End date of the overall availability window
   * @param parsedBusySlots All parsed busy slots
   * @param availabilityPreferences All availability preferences
   * @param eventType Calendar event type details
   * @param userTimezone User's timezone
   * @returns Array of available slots for this day
   */
  private static processDay(
    currentDate: Date,
    windowStartDate: Date,
    windowEndDate: Date,
    parsedBusySlots: ParsedBusySlot[],
    availabilityPreferences: AvailabilityPreference[],
    eventType: CalendarEventType,
    userTimezone: string
  ): AvailableSlot[] {
    // Check if date is marked as unavailable in preferences
    if (this.isDateMarkedUnavailable(currentDate, availabilityPreferences)) {
      return [];
    }

    // Find applicable preferences for this day
    const applicablePreferences = this.findApplicablePreferences(currentDate, availabilityPreferences);

    // If no preferences found for this day, skip it
    if (applicablePreferences.length === 0) {
      return [];
    }

    // Process each applicable preference window for this day
    const daySlots: AvailableSlot[] = [];

    for (const pref of applicablePreferences) {
      const slotsForWindow = this.processPreferenceWindow(currentDate, pref, windowStartDate, windowEndDate, parsedBusySlots, eventType, userTimezone);

      daySlots.push(...slotsForWindow);
    }

    return daySlots;
  }

  /**
   * Advances the date to the next day at midnight UTC
   * @param currentDate Date to advance
   * @returns A new Date object set to the next day at 00:00:00 UTC
   */
  private static advanceToNextDay(currentDate: Date): Date {
    const nextDate = toDate(currentDate, { timeZone: 'UTC' });
    nextDate.setUTCDate(nextDate.getUTCDate() + 1);
    nextDate.setUTCHours(0, 0, 0, 0);
    return nextDate;
  }

  /**
   * Calculate available slots based on availability window, busy slots,
   * user preferences, event type, and timezone
   * @param availabilityWindow The overall window to search for availability
   * @param busySlots Periods where the user is unavailable
   * @param availabilityPreferences User's availability preferences
   * @param eventType Details of the event type including duration and buffers
   * @param userTimezone User's timezone
   * @returns Array of available slots
   */
  public static calculateAvailabilitySlots(
    availabilityWindow: AvailabilityWindow,
    busySlots: BusySlot[],
    availabilityPreferences: AvailabilityPreference[],
    eventType: CalendarEventType,
    userTimezone: string
  ): AvailableSlot[] {
    // Parse and sort busy slots
    const parsedBusySlots = busySlots
      .map((slot) => ({
        start: toDate(slot.utcStart, { timeZone: 'UTC' }),
        end: toDate(slot.utcEnd, { timeZone: 'UTC' }),
      }))
      .sort((a, b) => a.start.getTime() - b.start.getTime());

    const windowStartDate = toDate(availabilityWindow.utcStartTimeString, { timeZone: 'UTC' });
    const windowEndDate = toDate(availabilityWindow.utcEndTimeString, { timeZone: 'UTC' });

    // Initialize currentDate to start of the first day at midnight UTC
    let currentDate = toDate(windowStartDate, { timeZone: 'UTC' });
    currentDate.setUTCHours(0, 0, 0, 0);

    const availableSlots: AvailableSlot[] = [];

    // Process each day within the availability window
    while (currentDate <= windowEndDate) {
      const daySlots = this.processDay(currentDate, windowStartDate, windowEndDate, parsedBusySlots, availabilityPreferences, eventType, userTimezone);

      availableSlots.push(...daySlots);

      // Advance to next day
      currentDate = this.advanceToNextDay(currentDate);
    }

    return availableSlots;
  }
}
