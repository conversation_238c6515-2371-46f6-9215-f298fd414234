import { endOfDay, startOfDay } from 'date-fns';
import { formatInTimeZone, fromZonedTime, toZonedTime } from 'date-fns-tz';

/**
 * Utility class for handling timezone conversions.
 * Contains static methods for converting between local times and UTC.
 */
export class TimezoneHelper {
  /**
   * Gets the start of the day in UTC for a given date.
   *
   * @param {string} dateTime - The date and time in 'yyyy-MM-ddTHH:mm:ss' format
   */
  public static toStartOfDay = (dateTime: string, timezone: string) => {
    const originalDate = fromZonedTime(dateTime, timezone);
    const startOfDayDate = startOfDay(originalDate);
    const utcStartOfDayDate = toZonedTime(startOfDayDate, timezone);

    return utcStartOfDayDate.toISOString();
  };

  /**
   * Gets the end of the day in UTC for a given date.
   *
   * @param {string} dateTime - The date and time in 'yyyy-MM-ddTHH:mm:ss' format
   */
  public static toEndOfDay = (dateTime: string, timezone: string) => {
    const originalDate = fromZonedTime(dateTime, timezone);
    const endOfDayDate = endOfDay(originalDate);
    const utcEndOfDayDate = toZonedTime(endOfDayDate, timezone);

    return utcEndOfDayDate.toISOString();
  };

  /**
   * Converts a UTC date and time to a local date and time based on the specified timezone.
   *
   * @param {string} utcDateTime - The date and time in UTC in 'yyyy-MM-ddTHH:mm:ss' format
   * @param {string} timezone - The timezone identifier (e.g., 'America/New_York', 'Europe/London')
   * @returns {Date} A Date object representing the time in the local timezone
   */
  public static utcToLocal = (utcDateTime: string, timezone: string) => {
    return toZonedTime(utcDateTime, timezone);
  };

  /**
   * Converts a local date and time to UTC based on the specified timezone.
   *
   * @param {string} dateString - The date in 'yyyy-MM-dd' format
   * @param {string} timeString - The time in 'HH:mm:ss' format
   * @param {string} timezone - The timezone identifier (e.g., 'America/New_York', 'Europe/London')
   */
  public static localDateTimeToUtc = (dateTime: string, timezone: string) => {
    return fromZonedTime(dateTime, timezone);
  };

  /**
   * Converts a local date and time to UTC based on the specified timezone.
   *
   * @param {string} dateString - The date in 'yyyy-MM-dd' format
   * @param {string} timeString - The time in 'HH:mm:ss' format
   * @param {string} timezone - The timezone identifier (e.g., 'America/New_York', 'Europe/London')
   * @returns {Date} A Date object representing the time in UTC
   */
  public static localTimeToUtc = (dateString: string, timeString: string, timezone: string) => {
    const localDateTimeString = `${dateString}T${timeString}`;
    return fromZonedTime(localDateTimeString, timezone);
  };

  /**
   * Converts availability preferences (date with start and end times) to UTC times.
   *
   * @param {Date} date - The date for the availability
   * @param {string} startTime - The start time in 'HH:mm:ss' format
   * @param {string} endTime - The end time in 'HH:mm:ss' format
   * @param {string} timezone - The timezone identifier (e.g., 'America/New_York', 'Europe/London')
   * @returns {Object} An object containing the converted start and end times in UTC
   * @returns {Date} utcStart - The start time converted to UTC
   * @returns {Date} utcEnd - The end time converted to UTC
   */
  public static availabilityPreferenceToUtc = (date: Date, startTime: string, endTime: string, timezone: string) => {
    const dateString = formatInTimeZone(date, 'UTC', 'yyyy-MM-dd');
    const utcStart = TimezoneHelper.localTimeToUtc(dateString, startTime, timezone);
    const utcEnd = TimezoneHelper.localTimeToUtc(dateString, endTime, timezone);

    return { utcStart, utcEnd };
  };
}
