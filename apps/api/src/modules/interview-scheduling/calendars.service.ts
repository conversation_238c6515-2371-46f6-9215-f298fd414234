import { Calendar, CalendarProvider } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { CreateCalendarEventRequest } from '@packages/contracts';
import crypto from 'crypto';
import { Credentials, OAuth2Client } from 'google-auth-library';
import { google } from 'googleapis';
import { Result, ResultAsync, ok } from 'neverthrow';

import { CalendarsRepository } from './calendars.repository';
import { VideoCallService } from './video-call.service';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

import { TimezoneHelper } from './helpers/timezone';

type HandleGoogleCallbackErrorCodes =
  | 'HANDLE_GOOGLE_CALLBACK_INVALID_TOKEN'
  | 'HANDLE_GOOGLE_CALLBACK_CREATE_CALENDAR_ERROR'
  | 'HANDLE_GOOGLE_CALLBACK_INVALID_STATE';

type GetAuthenticatedCalendarClientErrorCodes = 'GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED';

type GetBusySlotsWithTimezoneErrorCodes = 'GET_BUSY_SLOTS_WITH_TIMEZONE_ERROR';

type CreateCalendarEventErrorCodes = 'CALENDAR_CLIENT_CREATE_EVENT_ERROR';

type UpdateCalendarEventErrorCodes = 'CALENDAR_CLIENT_UPDATE_EVENT_ERROR';

type StatePayload = {
  userId: string;
  exp: number;
  nonce: string;
};

type VerifyResult = {
  valid: boolean;
  payload?: StatePayload;
  error?: string;
};

type CalendarTimeSlot = {
  utcStart: string;
  utcEnd: string;
};

type RescheduleEventRequest = {
  calendarOwnerId: string;
  calendarEventId: string;
  newStart: {
    dateTime: string;
    timeZone: string;
  };
  newEnd: {
    dateTime: string;
    timeZone: string;
  };
};

const DEFAULT_STATE_EXPIRY_MS = 2 * 60 * 1000; // 2 minutes
const JOIN_CALL_BEFORE_EVENT_MS = 5 * 60 * 1000; // A video call can be joined 5 minutes before the event
const EXPIRE_CALL_AFTER_EVENT_MS = 60 * 60 * 1000; // A video call can be extended to 1 additional hour

@Injectable()
export class CalendarsService {
  private authClient: OAuth2Client;
  private readonly STATE_SECRET: string;
  private readonly STATE_EXPIRY_MS: number;

  private safeJsonParse = Result.fromThrowable(JSON.parse);

  private hasValidCredentials(calendar: Calendar | null): boolean {
    return !!calendar?.credentials && (!!calendar.credentials.id_token || !!calendar.credentials.access_token);
  }

  private generateState(userId: string): string {
    const payload: StatePayload = {
      userId,
      exp: Date.now() + this.STATE_EXPIRY_MS,
      nonce: crypto.randomBytes(16).toString('hex'),
    };

    const payloadString = JSON.stringify(payload);
    const signature = crypto.createHmac('sha256', this.STATE_SECRET).update(payloadString).digest('hex');

    return Buffer.from(`${payloadString}.${signature}`).toString('base64url');
  }

  private verifyState(state: string): VerifyResult {
    const decoded = Buffer.from(state, 'base64url').toString();
    const [payloadString, signature] = decoded.split('.');

    if (!payloadString || !signature) {
      return { valid: false, error: 'Invalid state format' };
    }

    const expectedSignature = crypto.createHmac('sha256', this.STATE_SECRET).update(payloadString).digest('hex');

    if (signature !== expectedSignature) {
      return { valid: false, error: 'Invalid signature' };
    }

    const payloadResult = this.safeJsonParse(payloadString);

    if (payloadResult.isErr()) {
      return { valid: false, error: 'Invalid state format' };
    }

    const payload = payloadResult.value as StatePayload;

    if (Date.now() > payload.exp) {
      return { valid: false, error: 'State expired' };
    }

    return { valid: true, payload };
  }

  private async getAuthenticatedCalendarClient(userId: string) {
    const calendarResult = await this.calendarsRepository.findCalendarByUserId(userId);

    if (calendarResult.isErr()) {
      return calendarResult;
    }

    const calendar = calendarResult.value;

    if (!this.hasValidCredentials(calendar)) {
      return Errors.createError<GetAuthenticatedCalendarClientErrorCodes>('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED');
    }

    let credentials: Credentials = {
      id_token: calendar.credentials.id_token,
      refresh_token: calendar.credentials.refresh_token,
      access_token: calendar.credentials.access_token,
      expiry_date: calendar.credentials.expiry_date,
    };

    /**
     * Set the credentials on the auth client.
     * NOTE: token refresh will automatically refresh the credentials on the auth client.
     * See below on the refresh implementation
     */
    this.authClient.setCredentials(credentials);

    /**
     * Since we have credentials with offline access,
     * we have to keep track of expiry and get new access token by redeeming the refresh token
     */
    if (credentials.expiry_date && new Date() >= new Date(credentials.expiry_date)) {
      const refreshResult = await ResultAsync.fromPromise(this.authClient.refreshAccessToken(), (error) =>
        error instanceof Error ? error : new Error(String(error))
      );

      if (refreshResult.isErr()) {
        /**
         * If this fails, we have to disconnect to ask user to reconnect calendar.
         * this is highly unlikely tho and can ONLY happen if google has revoked the refresh token.
         */
        const disconnnectionResult = await this.disconnect(userId);

        return disconnnectionResult.isErr()
          ? disconnnectionResult
          : Errors.createError('GET_AUTHENTICATED_CALENDAR_CLIENT_CALENDAR_NOT_CONNECTED', {
              isUnexpectedError: true,
              originalError: refreshResult.error,
            });
      }

      credentials = refreshResult.value.credentials;

      const updateResult = await this.calendarsRepository.updateCalendarById(calendar.id, {
        credentials: {
          ...calendar.credentials,
          access_token: credentials.access_token,
          expiry_date: credentials.expiry_date,
        },
      });

      if (updateResult.isErr()) {
        /**
         * Disconnecting calendar here would make no sense
         * since this situation can only occur if the database is down
         */
        return updateResult;
      }
    }

    return ok({
      calendarId: calendar.calendarId,
      client: google.calendar({
        version: 'v3',
        auth: this.authClient,
      }),
    });
  }

  constructor(
    private readonly calendarsRepository: CalendarsRepository,
    protected readonly commonUsersRepository: CommonUsersRepository,
    private readonly videoCallService: VideoCallService,
    private readonly configService: ConfigService
  ) {
    this.authClient = new google.auth.OAuth2(
      this.configService.get('GOOGLE_CLIENT_ID'),
      this.configService.get('GOOGLE_CLIENT_SECRET'),
      `${this.configService.get('API_BASE_URL')}/interview-scheduling/v1/calendars/auth/google/callback`
    );
    this.STATE_SECRET = crypto.randomBytes(32).toString('hex');
    this.STATE_EXPIRY_MS = DEFAULT_STATE_EXPIRY_MS;
  }

  initiateGoogleAuth = (userId: string) => {
    return this.authClient.generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent',
      scope: [
        'https://www.googleapis.com/auth/calendar.readonly',
        'https://www.googleapis.com/auth/userinfo.profile',
        'https://www.googleapis.com/auth/calendar.events',
      ],
      state: this.generateState(userId),
    });
  };

  handleGoogleCallback = async (code: string, state: string) => {
    const stateResult = this.verifyState(state);

    if (!stateResult.valid) {
      return Errors.createError<HandleGoogleCallbackErrorCodes>('HANDLE_GOOGLE_CALLBACK_INVALID_STATE');
    }

    const { userId } = stateResult.payload!;

    const tokenResult = await ResultAsync.fromPromise(this.authClient.getToken(code), (error) => (error instanceof Error ? error : new Error(String(error))));

    if (tokenResult.isErr()) {
      return Errors.createError<HandleGoogleCallbackErrorCodes>('HANDLE_GOOGLE_CALLBACK_INVALID_TOKEN', {
        isUnexpectedError: true,
        originalError: tokenResult.error,
      });
    }

    const { tokens } = tokenResult.value;

    if (!tokens.access_token || (!tokens.refresh_token && !tokens.id_token) || !tokens.expiry_date) {
      return Errors.createError<HandleGoogleCallbackErrorCodes>('HANDLE_GOOGLE_CALLBACK_INVALID_TOKEN', {
        isUnexpectedError: true,
      });
    }

    const existingCalendarResult = await this.calendarsRepository.findCalendarByUserIdAndProvider(userId, CalendarProvider.google);

    if (existingCalendarResult.isErr()) {
      if (existingCalendarResult.error.code === 'FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_DB_ERROR') {
        return existingCalendarResult;
      }

      const createCalendarResult = await this.calendarsRepository.createCalendar({
        userId,
        provider: CalendarProvider.google,
        calendarId: 'primary',
        credentials: tokens,
      });

      return createCalendarResult.isErr() ? createCalendarResult : ok();
    }

    const updateCalendarResult = await this.calendarsRepository.updateCalendarByUserId(userId, {
      credentials: tokens,
    });

    return updateCalendarResult.isErr() ? updateCalendarResult : ok();
  };

  getStatus = async (userId: string) => {
    const calendarResult = await this.calendarsRepository.findCalendarByUserId(userId);

    if (calendarResult.isErr()) {
      return calendarResult;
    }

    const calendar = calendarResult.value;
    const isConnected = this.hasValidCredentials(calendar);

    return ok({
      isConnected,
      provider: isConnected ? (calendar?.provider ?? null) : null,
    });
  };

  disconnect = async (userId: string) => {
    const calendarResult = await this.calendarsRepository.findCalendarByUserId(userId);

    if (calendarResult.isErr()) {
      return calendarResult;
    }

    const updateResult = await this.calendarsRepository.updateCalendarById(calendarResult.value.id, {
      credentials: {
        access_token: null,
        refresh_token: null,
        id_token: null,
        expiry_date: null,
      },
    });

    if (updateResult.isErr()) {
      return updateResult;
    }

    return ok({
      isConnected: false,
      provider: null,
    });
  };

  async getBusySlots(userId: string, utcStartTimeString: string, utcEndTimeString: string) {
    const calendarClientResult = await this.getAuthenticatedCalendarClient(userId);

    if (calendarClientResult.isErr()) {
      return calendarClientResult;
    }

    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(userId);

    if (userResult.isErr()) {
      return userResult;
    }

    const calendarClient = calendarClientResult.value.client;

    const freeBusyWithTimezoneResult = await ResultAsync.combineWithAllErrors([
      ResultAsync.fromPromise(
        calendarClient.freebusy.query({
          requestBody: {
            timeMin: utcStartTimeString,
            timeMax: utcEndTimeString,
            items: [{ id: 'primary' }],
            timeZone: 'UTC',
          },
        }),
        (error) => (error instanceof Error ? error : new Error(String(error)))
      ),
      ResultAsync.fromPromise(calendarClient.calendars.get({ calendarId: 'primary' }), (error) => (error instanceof Error ? error : new Error(String(error)))),
    ]);

    if (freeBusyWithTimezoneResult.isErr()) {
      const errorMessages = freeBusyWithTimezoneResult.error.map((error) => error.message).join('\n');
      return Errors.createError<GetBusySlotsWithTimezoneErrorCodes>('GET_BUSY_SLOTS_WITH_TIMEZONE_ERROR', {
        isUnexpectedError: true,
        originalError: new Error(errorMessages),
      });
    }

    const [freeBusy, calendar] = freeBusyWithTimezoneResult.value;

    /**
     * Although it's highly unlikely that there is no timezone set on the calendar,
     * in case it does we default to user's timezone preference in mongo or UTC.
     */
    const timezone = calendar.data.timeZone ?? userResult.value.timezone?.name ?? 'UTC';
    const busyData = freeBusy.data?.calendars?.primary.busy;

    if (!busyData) {
      /**
       * This shouldn't happen in theory but google's API has optionals everywhere.
       * so we have to make sure in case it's missing we can send default result.
       * This isn't non-breaking so we don't have to return error result
       */
      return ok({
        timezone,
        slots: [],
      });
    }

    const busySlots: CalendarTimeSlot[] = [];

    /**
     * This is annoying as google's api returns busy slots as optional string values.
     * This hack is to keep tslint happy.
     */
    for (const slot of busyData) {
      if (!slot.start || !slot.end) {
        continue;
      }
      busySlots.push({
        utcStart: slot.start,
        utcEnd: slot.end,
      });
    }

    return ok({
      slots: busySlots,
      timezone,
    });
  }

  async createEvent(calendarOwnerId: string, eventRequest: CreateCalendarEventRequest) {
    const calendarClientResult = await this.getAuthenticatedCalendarClient(calendarOwnerId);

    if (calendarClientResult.isErr()) {
      return calendarClientResult;
    }

    const calendarClient = calendarClientResult.value.client;
    const videoRoomId = crypto.randomUUID();
    const utcStartTime = TimezoneHelper.localDateTimeToUtc(eventRequest.start.dateTime, eventRequest.start.timezone).getTime() + JOIN_CALL_BEFORE_EVENT_MS;
    const utcEndTime = TimezoneHelper.localDateTimeToUtc(eventRequest.end.dateTime, eventRequest.end.timezone).getTime() + EXPIRE_CALL_AFTER_EVENT_MS;
    const videoLinkResult = await this.videoCallService.getVideoCallProviderUrl('daily.co', videoRoomId, utcStartTime, utcEndTime);

    if (videoLinkResult.isErr()) {
      return videoLinkResult;
    }

    const videoLink = videoLinkResult.value;

    const eventResult = await ResultAsync.fromPromise(
      calendarClient.events.insert({
        calendarId: 'primary',
        sendNotifications: false,
        sendUpdates: 'none',
        requestBody: {
          ...eventRequest,
          status: 'confirmed',
          conferenceData: {
            createRequest: {
              requestId: videoRoomId,
            },
          },
          location: videoLink,
          reminders: {
            useDefault: false,
          },
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (eventResult.isErr()) {
      return Errors.createError<CreateCalendarEventErrorCodes>('CALENDAR_CLIENT_CREATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: eventResult.error,
      });
    }

    if (!eventResult.value.data.id) {
      return Errors.createError<CreateCalendarEventErrorCodes>('CALENDAR_CLIENT_CREATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: new Error('Event id not found in response'),
      });
    }

    return ok({
      calendarEventId: eventResult.value.data.id,
      calendarId: calendarClientResult.value.calendarId,
      status: eventResult.value.data.status,
      callUrl: videoLink,
      callId: String(videoRoomId),
      callType: 'daily.co',
    });
  }

  async rescheduleEvent(calendarEventId: string, eventRequest: RescheduleEventRequest) {
    const calendarClientResult = await this.getAuthenticatedCalendarClient(eventRequest.calendarOwnerId);

    if (calendarClientResult.isErr()) {
      return calendarClientResult;
    }

    const calendarClient = calendarClientResult.value.client;

    const eventResult = await ResultAsync.fromPromise(
      calendarClient.events.update({
        calendarId: 'primary',
        eventId: calendarEventId,
        requestBody: {
          start: eventRequest.newStart,
          end: eventRequest.newEnd,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (eventResult.isErr()) {
      return Errors.createError<UpdateCalendarEventErrorCodes>('CALENDAR_CLIENT_UPDATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: eventResult.error,
      });
    }

    if (!eventResult.value.data.id) {
      return Errors.createError<UpdateCalendarEventErrorCodes>('CALENDAR_CLIENT_UPDATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: new Error('Event id not found in response'),
      });
    }

    return ok({
      calendarEventId: eventResult.value.data.id,
      calendarId: calendarClientResult.value.calendarId,
      status: eventResult.value.data.status,
    });
  }

  async cancelEvent(calendarEventId: string, calendarOwnerId: string) {
    const calendarClientResult = await this.getAuthenticatedCalendarClient(calendarOwnerId);

    if (calendarClientResult.isErr()) {
      return calendarClientResult;
    }

    const calendarClient = calendarClientResult.value.client;

    const eventResult = await ResultAsync.fromPromise(
      calendarClient.events.update({
        calendarId: 'primary',
        eventId: calendarEventId,
        requestBody: {
          status: 'cancelled',
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (eventResult.isErr()) {
      return Errors.createError<UpdateCalendarEventErrorCodes>('CALENDAR_CLIENT_UPDATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: eventResult.error,
      });
    }

    if (!eventResult.value.data.id) {
      return Errors.createError<UpdateCalendarEventErrorCodes>('CALENDAR_CLIENT_UPDATE_EVENT_ERROR', {
        isUnexpectedError: true,
        originalError: new Error('Event id not found in response'),
      });
    }

    return ok({
      calendarEventId: eventResult.value.data.id,
      calendarId: calendarClientResult.value.calendarId,
      status: eventResult.value.data.status,
    });
  }
}
