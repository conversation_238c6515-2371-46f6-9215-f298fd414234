import { Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindCalendarEventsByUserAndEventTypeIdErrorCodes = 'FIND_CALENDAR_EVENTS_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR';

type FindCalendarEventByIdErrorCodes = 'FIND_CALENDAR_EVENT_BY_ID_DB_ERROR' | 'FIND_CALENDAR_EVENT_BY_ID_NOT_FOUND';

type CreateCalendarEventByUserAndEventTypeIdErrorCodes = 'CREATE_CALENDAR_EVENT_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR';

type UpdateCalendarEventByIdErrorCodes = 'UPDATE_CALENDAR_EVENT_BY_ID_DB_ERROR';

@Injectable()
export class CalendarEventsRepository {
  constructor(private readonly prisma: DbService) {}

  findCalendarEventsByUserAndEventTypeId = async (userId: string, eventTypeId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.calendarEvent.findMany({
        where: {
          eventOwnerId: userId,
          eventTypeId,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindCalendarEventsByUserAndEventTypeIdErrorCodes>('FIND_CALENDAR_EVENTS_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  findCalendarEventById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.calendarEvent.findUnique({
        where: {
          id,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindCalendarEventByIdErrorCodes>('FIND_CALENDAR_EVENT_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindCalendarEventByIdErrorCodes>('FIND_CALENDAR_EVENT_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  createCalendarEventByUserAndEventTypeId = async (data: Prisma.CalendarEventUncheckedCreateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.calendarEvent.create({
        data,
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<CreateCalendarEventByUserAndEventTypeIdErrorCodes>('CREATE_CALENDAR_EVENT_BY_USER_AND_EVENT_TYPE_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  updateCalendarEventById = async (id: string, data: Prisma.CalendarEventUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.calendarEvent.update({
        where: {
          id,
        },
        data,
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateCalendarEventByIdErrorCodes>('UPDATE_CALENDAR_EVENT_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
