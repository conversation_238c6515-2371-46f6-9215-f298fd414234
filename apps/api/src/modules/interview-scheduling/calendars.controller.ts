import { Controller, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON>and<PERSON>, TsRestHandler } from '@ts-rest/nest';

import { CalendarsService } from './calendars.service';
import { SentryService } from '@lib/global/sentry.service';

import { AuthenticatedRequest, JwtGuard } from '@lib/guards/jwt.guard';

@Controller()
export class CalendarsController {
  constructor(
    private readonly calendarsService: CalendarsService,
    private readonly sentryService: SentryService
  ) {}

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.calendars.initiateGoogleAuth)
  initiateGoogleAuth(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendars.initiateGoogleAuth, async () => {
      const authUrl = this.calendarsService.initiateGoogleAuth(req.user.id);

      return {
        status: HttpStatus.OK,
        body: { authUrl },
      };
    });
  }

  @TsRestHandler(apiContract.calendars.googleAuthCallback)
  googleAuthCallback(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendars.googleAuthCallback, async ({ query }) => {
      const result = await this.calendarsService.handleGoogleCallback(query.code, query.state);

      if (result.isErr()) {
        const error = result.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/calendars/calendars.controller/googleAuthCallback] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'HANDLE_GOOGLE_CALLBACK_INVALID_STATE':
            return {
              status: HttpStatus.BAD_REQUEST,
              body: { message: 'Request expired. Please try again' },
            };
          case 'HANDLE_GOOGLE_CALLBACK_INVALID_TOKEN':
            return {
              status: HttpStatus.UNAUTHORIZED,
              body: { message: 'This request is not authorized' },
            };
          case 'CREATE_CALENDAR_DB_ERROR':
          case 'HANDLE_GOOGLE_CALLBACK_CREATE_CALENDAR_ERROR':
          default:
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
      };
    });
  }

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.calendars.getStatus)
  getStatus(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendars.getStatus, async () => {
      const result = await this.calendarsService.getStatus(req.user.id);

      if (result.isErr()) {
        const error = result.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/calendars/calendars.controller/getStatus] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_CALENDAR_BY_USER_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Calendar not found' },
            };
          case 'FIND_CALENDAR_BY_USER_ID_DB_ERROR':
          default:
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @UseGuards(JwtGuard)
  @TsRestHandler(apiContract.calendars.disconnect)
  disconnect(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.calendars.disconnect, async () => {
      const result = await this.calendarsService.disconnect(req.user.id);

      if (result.isErr()) {
        const error = result.error;

        if (error.isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/calendars/calendars.controller/disconnect] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_CALENDAR_BY_USER_ID_NOT_FOUND':
          case 'UPDATE_CALENDAR_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'Calendar not found' },
            };
          case 'FIND_CALENDAR_BY_USER_ID_DB_ERROR':
          case 'UPDATE_CALENDAR_BY_ID_DB_ERROR':
          default:
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
