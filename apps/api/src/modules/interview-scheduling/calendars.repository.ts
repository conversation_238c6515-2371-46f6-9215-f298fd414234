import { CalendarProvider, Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindCalendarByUserIdErrorCodes = 'FIND_CALENDAR_BY_USER_ID_DB_ERROR' | 'FIND_CALENDAR_BY_USER_ID_NOT_FOUND';
type FindCalendarByUserIdAndProviderErrorCodes = 'FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_DB_ERROR' | 'FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_NOT_FOUND';
type CreateCalendarErrorCodes = 'CREATE_CALENDAR_DB_ERROR';
type UpdateCalendarByIdErrorCodes = 'UPDATE_CALENDAR_BY_ID_DB_ERROR' | 'UPDATE_CALENDAR_BY_ID_NOT_FOUND';
type UpdateCalendarByUserIdErrorCodes = 'UPDATE_CALENDAR_BY_USER_ID_DB_ERROR' | 'UPDATE_CALENDAR_BY_USER_ID_NOT_FOUND';

@Injectable()
export class CalendarsRepository {
  constructor(private readonly prisma: DbService) {}

  findCalendarByUserId = async (userId: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.calendar.findUnique({ where: { userId } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindCalendarByUserIdErrorCodes>('FIND_CALENDAR_BY_USER_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindCalendarByUserIdErrorCodes>('FIND_CALENDAR_BY_USER_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  findCalendarByUserIdAndProvider = async (userId: string, provider: CalendarProvider) => {
    const result = await ResultAsync.fromPromise(this.prisma.calendar.findFirst({ where: { userId, provider } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindCalendarByUserIdAndProviderErrorCodes>('FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindCalendarByUserIdAndProviderErrorCodes>('FIND_CALENDAR_BY_USER_ID_AND_PROVIDER_NOT_FOUND');
    }

    return ok(result.value);
  };

  createCalendar = async (data: Prisma.CalendarUncheckedCreateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.calendar.create({ data }), (error) => (error instanceof Error ? error : new Error(String(error))));

    if (result.isErr()) {
      return Errors.createError<CreateCalendarErrorCodes>('CREATE_CALENDAR_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  updateCalendarById = async (id: string, data: Prisma.CalendarUpdateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.calendar.update({ where: { id }, data }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateCalendarByIdErrorCodes>('UPDATE_CALENDAR_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<UpdateCalendarByIdErrorCodes>('UPDATE_CALENDAR_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  updateCalendarByUserId = async (userId: string, data: Prisma.CalendarUpdateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.calendar.update({ where: { userId }, data }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateCalendarByUserIdErrorCodes>('UPDATE_CALENDAR_BY_USER_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<UpdateCalendarByUserIdErrorCodes>('UPDATE_CALENDAR_BY_USER_ID_NOT_FOUND');
    }

    return ok(result.value);
  };
}
