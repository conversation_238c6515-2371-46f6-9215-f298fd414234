import { Injectable } from '@nestjs/common';
import axios, { AxiosError } from 'axios';
import { ResultAsync, ok } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

type GetVideoCallProviderUrlErrorCodes = 'GET_VIDEO_CALL_URL_API_ERROR' | 'GET_VIDEO_CALL_URL_INVALID_PROVIDER';

type DailyApiResponse = {
  url: string;
};

type VideoCallProvider = 'daily.co' | 'zoom';

@Injectable()
export class VideoCallService {
  private async getDailyUrl(eventId: string, notBefore: number, expiresAt: number) {
    const result = await ResultAsync.fromPromise(
      axios.post<DailyApiResponse>(
        this.configService.get('DAILY_API_URL'),
        {
          name: eventId,
          privacy: 'public',
          properties: {
            enable_transcription_storage: true,
            nbf: notBefore,
            exp: expiresAt,
          },
        },
        {
          headers: {
            Authorization: `Bearer ${this.configService.get('DAILY_API_KEY')}`,
          },
        }
      ),
      (error) => (error instanceof AxiosError ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GetVideoCallProviderUrlErrorCodes>('GET_VIDEO_CALL_URL_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value.data?.url) {
      return Errors.createError<GetVideoCallProviderUrlErrorCodes>('GET_VIDEO_CALL_URL_API_ERROR', {
        isUnexpectedError: true,
        originalError: new Error('Daily.co API response missing URL'),
      });
    }

    return ok(result.value.data.url);
  }

  constructor(private readonly configService: ConfigService) {}

  async getVideoCallProviderUrl(provider: VideoCallProvider, roomName: string, notBefore: number, expiresAt: number) {
    switch (provider) {
      case 'daily.co':
        return await this.getDailyUrl(roomName, notBefore, expiresAt);
      default:
        return Errors.createError<GetVideoCallProviderUrlErrorCodes>('GET_VIDEO_CALL_URL_INVALID_PROVIDER');
    }
  }
}
