import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindCalendarEventTypeByIdErrorCode = 'FIND_CALENDAR_EVENT_TYPE_BY_ID_DB_ERROR' | 'FIND_CALENDAR_EVENT_TYPE_BY_ID_NOT_FOUND';

@Injectable()
export class CalendarEventTypesRepository {
  constructor(private readonly prisma: DbService) {}

  findCalendarEventTypeById = async (eventTypeId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.calendarEventType.findFirst({
        where: {
          id: eventTypeId,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindCalendarEventTypeByIdErrorCode>('FIND_CALENDAR_EVENT_TYPE_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindCalendarEventTypeByIdErrorCode>('FIND_CALENDAR_EVENT_TYPE_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };
}
