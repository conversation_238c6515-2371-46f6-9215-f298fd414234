import { Module } from '@nestjs/common';

import { CalendarAvailabilitiesController } from './calendar-availabilities.controller';
import { CalendarAvailabilitiesRepository } from './calendar-availabilities.repository';
import { CalendarAvailabilitiesService } from './calendar-availabilities.service';
import { CalendarEventTypesRepository } from './calendar-event-types.repository';
import { CalendarEventsController } from './calendar-events.controller';
import { CalendarEventsRepository } from './calendar-events.repository';
import { CalendarEventsService } from './calendar-events.service';
import { CalendarsController } from './calendars.controller';
import { CalendarsRepository } from './calendars.repository';
import { CalendarsService } from './calendars.service';
import { VideoCallService } from './video-call.service';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientAppV1Client } from '@lib/clients/client-app-v1.client';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';
import { GuardsModule } from '@lib/guards/guards.module';

@Module({
  imports: [GuardsModule, CommonUsersModule],
  controllers: [CalendarsController, CalendarAvailabilitiesController, CalendarEventsController],
  providers: [
    CalendarsService,
    CalendarAvailabilitiesService,
    CalendarEventsService,
    VideoCallService,
    CalendarsRepository,
    CalendarAvailabilitiesRepository,
    CalendarEventTypesRepository,
    CalendarEventsRepository,
    ClientAppV1Client,
    PlatformServiceClient,
  ],
})
export class InterviewSchedulingModule {}
