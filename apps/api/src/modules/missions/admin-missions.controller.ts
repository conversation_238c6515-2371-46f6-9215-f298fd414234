import { Controller, HttpStatus, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON><PERSON><PERSON>, TsRestHand<PERSON> } from '@ts-rest/nest';

import { AdminMissionsService } from './admin-missions.service';
import { SentryService } from '@lib/global/sentry.service';

import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

@Controller()
@UseGuards(AdminJwtGuard)
export class AdminMissionsController {
  constructor(
    private readonly adminMissionsService: AdminMissionsService,
    private readonly sentryService: SentryService
  ) {}

  @TsRestHandler(apiContract.adminMissions.getMissionIndustries)
  getMissionIndustries(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getMissionIndustries, async () => {
      const result = await this.adminMissionsService.getAllMissionIndustries();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getMissionIndustries] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ALL_TALENT_INDUSTRIES_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getTfsOwners)
  getTfsOwners(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getTfsOwners, async () => {
      const result = await this.adminMissionsService.getTfsOwners();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getTfsOwners] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_USERS_BY_EMAILS_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getMissionRoleCategories)
  getMissionRoleCategories(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getMissionRoleCategories, async () => {
      const result = await this.adminMissionsService.getRoleCategories();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getMissionRoleCategories] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ALL_ROLE_CATEGORIES_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getMissionRoleSkills)
  getMissionRoleSkills(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getMissionRoleSkills, async () => {
      const result = await this.adminMissionsService.getRoleSkills();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getMissionRoleSkills] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ALL_TALENT_SKILLS_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getUsersToExclude)
  getUsersToExclude(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getUsersToExclude, async ({ query: { query } }) => {
      const result = await this.adminMissionsService.getUsersByNameQuery(query);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getUsersToExclude] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_BASIC_USERS_DETAILS_BY_QUERY_NAME_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getAdminSettings)
  getAdminSettings(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getAdminSettings, async ({ params: { id } }) => {
      const result = await this.adminMissionsService.getAdminSettings(id);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getAdminSettings] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'GET_MISSION_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.updateAdminSettings)
  updateAdminSettings(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.updateAdminSettings, async ({ body, params: { id } }) => {
      const result = await this.adminMissionsService.updateAdminSettings(id, body);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/updateAdminSettings] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_BY_ID_API_ERROR':
          case 'GET_MISSION_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getMissionSettings)
  getMissionSettings(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getMissionSettings, async ({ params: { id } }) => {
      const result = await this.adminMissionsService.getMissionSettings(id);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getMissionSettings] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'GET_MISSION_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.updateMissionSettings)
  updateMissionSettings(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.updateMissionSettings, async ({ body, params: { id } }) => {
      const result = await this.adminMissionsService.updateMissionSettings(id, body);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/updateMissionSettings] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_BY_ID_API_ERROR':
          case 'GET_MISSION_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
      };
    });
  }

  @TsRestHandler(apiContract.adminMissions.getRoleSettings)
  getRoleSettings(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.adminMissions.getRoleSettings, async ({ params: { id } }) => {
      const result = await this.adminMissionsService.getRoleSettings(id);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/admin-missions.controller/getRoleSettings] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'GET_MISSION_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'Internal server error, please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
