import { MISSION_ROLE_TIME_COMMITMENT_VALUES, MISSION_TIME_OVERLAP_VALUES } from '@packages/contracts';

type TimeOverlapValue = (typeof MISSION_TIME_OVERLAP_VALUES)[number];
type TimeCommitmentValue = (typeof MISSION_ROLE_TIME_COMMITMENT_VALUES)[number];

export class TimeCalculator {
  public static mapTimeOverlapToMinutes(timeOverlapValue: TimeOverlapValue | undefined): number | null {
    switch (timeOverlapValue) {
      case 'no_overlap':
        return 0;
      case '2h':
        return 120;
      case '4h':
        return 240;
      case '6h':
        return 360;
      case '8h':
        return 480;
      default:
        return null;
    }
  }

  public static mapTimeCommitmentToHours(timeCommitment: TimeCommitmentValue | undefined): number {
    switch (timeCommitment) {
      case '5h':
        return 5;
      case '10h':
        return 10;
      case '20h':
        return 20;
      case '30h':
        return 30;
      case '40h':
        return 40;
      default:
        return 0;
    }
  }
}
