import { Prisma } from '@a_team/prisma';
import { DraftMissionDto } from '@packages/contracts';

import { RateCalculator } from '@modules/missions/helpers/rate';
import { TimeCalculator } from '@modules/missions/helpers/time';

export class MissionTransformer {
  public static transformFormRoleToMissionRole(role: DraftMissionDto['roles'][number]): Prisma.MissionRoleCreateInput {
    const markup = role.markupPercentage ? role.markupPercentage / 100 : 0.43; // TODO: Update to default based on the company, smb or enterprise

    const builderRateMax = role.budgetType === 'hourly' && role.budget ? RateCalculator.getBuilderRateFromClientRateAndMarkup(role.budget, markup) : null;

    const builderMonthlyRateMax =
      role.budgetType === 'monthly' && role.budget ? RateCalculator.getBuilderRateFromClientRateAndMarkup(role.budget, markup) : null;

    return {
      id: role.id,
      status: 'Open',
      createdAt: new Date(),
      updatedAt: new Date(),
      categoryId: role.categoryId,
      headline: role.headline,
      margin: markup, // for backward compatibility
      markup: markup,
      availability: {
        weeklyHoursAvailable: TimeCalculator.mapTimeCommitmentToHours(role.timeCommitment),
      },
      isFullTimeRetainer: role.budgetType === 'monthly',
      builderRateMax,
      builderMonthlyRateMax,
      customQuestions: role.screeningQuestion ? [{ text: role.screeningQuestion, isVisible: true }] : [],
      requiredSkills: role.requiredSkills ? role.requiredSkills.map((id) => ({ talentSkillId: id })) : [],
      preferredSkills: role.preferredSkills ? role.preferredSkills.map((id) => ({ talentSkillId: id })) : [],
      locations: role.locations,
    };
  }

  public static transformFormValuesToMissionFields(formValues: DraftMissionDto) {
    return {
      title: formValues.title,
      logoURL: formValues.logoURL,
      videoURL: formValues.videoURL,
      plannedStart: formValues.plannedStart,
      overlapMinutes: TimeCalculator.mapTimeOverlapToMinutes(formValues.timeOverlap),
      timezone: formValues.timezone,
      description: formValues.description,
      companyStory: formValues.companyStory,
      roles: formValues.roles.map((role) => this.transformFormRoleToMissionRole(role)),
    };
  }
}
