/*
 * The RateCalculator is designed to convert between markup and margin rates, specifically for
 * calculating builder and client hourly rates.
 *
 * Definitions:
 * - Margin: The percentage deducted from the client rate.
 * - Markup: The percentage added on top of the builder rate.
 *
 * Example:
 * Given:
 * - Builder rate = $87.5
 * - Client rate  = $125
 *
 * Calculations:
 * 1. Margin Calculation:
 *    Margin = 1 - (Builder Rate / Client Rate)
 *           = 1 - (87.5 / 125)
 *           = 0.3 (or 30% margin)
 *
 * 2. Markup Calculation:
 *    Client Rate = Builder Rate * (1 + Markup)
 *    125 = 87.5 * (1 + 0.428571429)
 *    Markup = 0.428571429 (or ~43% markup)
 *
 * Conversion Formulas:
 * - From Margin to Markup:
 *   Markup = Margin / (1 - Margin)
 *   Example: 0.3 / (1 - 0.3) = 0.428571429 (~43% markup)
 *
 * - From Markup to Margin:
 *   Margin = Markup / (1 + Markup)
 *   Example: 0.428571429 / (1 + 0.428571429) = 0.3 (30% margin)
 *
 * Client Rate Calculations:
 * - Based on Builder Rate & Margin:
 *   Client Rate = Builder Rate / (1 - Margin)
 *   Example: 87.5 / 0.7 = 125
 *
 * - Based on Builder Rate & Markup:
 *   Client Rate = Builder Rate * (1 + Markup)
 *   Example: 87.5 * 1.42857143 = 125
 */
export class RateCalculator {
  public static getMarginFromMarkup = (markup: number): number => {
    return markup / (1 + markup);
  };

  public static getMarkupFromMargin = (margin: number): number => {
    return margin / (1 - margin);
  };

  public static getClientRateFromBuilderRateAndMarkup(builderRate: number, markup: number): number {
    return builderRate * (1 + markup);
  }

  public static getClientRateFromBuilderRateAndMargin(builderRate: number, margin: number): number {
    return builderRate / (1 - margin);
  }

  public static getBuilderRateFromClientRateAndMarkup(clientRate: number, markup: number): number {
    return clientRate / (1 + markup);
  }

  public static getBuilderRateFromClientRateAndMargin(clientRate: number, margin: number): number {
    return clientRate * (1 - margin);
  }

  public static getMarkupFromPercentage(markupPercentage: string): number | undefined {
    const parsedNumber = Number(markupPercentage);

    if (isNaN(parsedNumber) || parsedNumber < 0 || parsedNumber > 100) {
      return undefined;
    }

    return parsedNumber / 100;
  }
}
