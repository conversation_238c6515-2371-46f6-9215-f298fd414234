import { MissionPrefill } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ResultAsync, ok } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

export type MissionPrefillRoleSkills = {
  requiredSkills: string[];
  preferredSkills: string[];
};

type FindMissionPrefillByUserIdErrorCodes = 'FIND_MISSION_PREFILL_NOT_FOUND' | 'FIND_MISSION_PREFILL_DB_ERROR';

type UpsetMissionPrefillErrorCodes = 'UPSERT_MISSION_PREFILL_DB_ERROR';

type GetClientRequestedSolutionErrorCodes = 'GET_CLIENT_REQUESTED_SOLUTION_DB_ERROR' | 'GET_CLIENT_REQUESTED_SOLUTION_NOT_FOUND';

type GetMostCommonSkillsForSpecializationErrorCodes = 'GET_MOST_COMMON_SKILLS_FOR_SPECIALIZATION_DB_ERROR';

@Injectable()
export class MissionsPrefillRepository {
  constructor(private readonly prisma: DbService) {}

  findMissionPrefillByUserId = async (userId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.missionPrefill.findUnique({
        where: {
          userId,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionPrefillByUserIdErrorCodes>('FIND_MISSION_PREFILL_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const optionalMissionPrefill = result.value;

    if (!optionalMissionPrefill) {
      return Errors.createError<FindMissionPrefillByUserIdErrorCodes>('FIND_MISSION_PREFILL_NOT_FOUND');
    }

    return ok<MissionPrefill>(optionalMissionPrefill);
  };

  upsertMissionPrefill = async (userId: string, data?: Partial<MissionPrefill>) => {
    let createProps = { userId };
    let updateProps = {};

    if (data) {
      updateProps = { ...data };
      createProps = { userId, ...data };
    }

    const result = await ResultAsync.fromPromise(
      this.prisma.missionPrefill.upsert({
        where: {
          userId,
        },
        update: updateProps,
        create: createProps,
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<UpsetMissionPrefillErrorCodes>('UPSERT_MISSION_PREFILL_DB_ERROR');
    }

    return ok(result.value);
  };

  getClientRequestedSolution = async (solutionId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.solution.findUnique({
        where: {
          id: solutionId,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<GetClientRequestedSolutionErrorCodes>('GET_CLIENT_REQUESTED_SOLUTION_DB_ERROR');
    }

    const solution = result.value;

    if (!solution) {
      return Errors.createError<GetClientRequestedSolutionErrorCodes>('GET_CLIENT_REQUESTED_SOLUTION_NOT_FOUND');
    }

    return ok(solution);
  };

  /**
   * Repository function used for getting the most common skills for a given talent specialization.
   *
   * The returned object has the following structure:
   * - `requiredSkills`: An array of the top 3 most common skill IDs.
   * - `preferredSkills`: An array of the next 3 most common skill IDs.
   *
   * If no users are found with the given specialization, both `requiredSkills` and `preferredSkills` will be empty arrays.
   *
   * @param mainTalentSpecializationId - The ID of the main talent specialization.
   */
  getMostCommonSkillsForSpecialization = async (mainTalentSpecializationId: string) => {
    const usersWithSpecializationResult = await ResultAsync.fromPromise(
      this.prisma.user.findMany({
        where: {
          talentProfile: {
            is: {
              mainTalentSpecializationId,
            },
          },
          status: 'Active',
          type: 'user',
        },
        select: {
          talentProfile: {
            select: {
              talentSkills: {
                select: {
                  mainTalentSkills: true,
                },
              },
            },
          },
        },
        take: 100,
      }),
      (e) => new Error(e as string)
    );

    if (usersWithSpecializationResult.isErr()) {
      return Errors.createError<GetMostCommonSkillsForSpecializationErrorCodes>('GET_MOST_COMMON_SKILLS_FOR_SPECIALIZATION_DB_ERROR', {
        isUnexpectedError: true,
        originalError: usersWithSpecializationResult.error,
      });
    }

    const usersWithSpecialization = usersWithSpecializationResult.value;

    if (usersWithSpecialization.length === 0) {
      return ok<MissionPrefillRoleSkills, never>({
        requiredSkills: [],
        preferredSkills: [],
      });
    }

    const skillCounts = new Map();

    for (const user of usersWithSpecialization) {
      const skills = user.talentProfile?.talentSkills?.mainTalentSkills || [];

      for (const skill of skills) {
        if (skill.talentSkillId) {
          skillCounts.set(skill.talentSkillId, (skillCounts.get(skill.talentSkillId) || 0) + 1);
        }
      }
    }

    const sortedSkills = Array.from(skillCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([id]) => id);

    const requiredCount = Math.min(3, sortedSkills.length);
    const preferredCount = Math.min(3, Math.max(0, sortedSkills.length - requiredCount));

    return ok<MissionPrefillRoleSkills, never>({
      requiredSkills: sortedSkills.slice(0, requiredCount),
      preferredSkills: sortedSkills.slice(requiredCount, requiredCount + preferredCount),
    });
  };
}
