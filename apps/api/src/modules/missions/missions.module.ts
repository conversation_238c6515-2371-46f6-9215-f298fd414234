import { Module } from '@nestjs/common';

import { AdminMissionsController } from './admin-missions.controller';
import { AdminMissionsService } from './admin-missions.service';
import { MissionsPrefillController } from './missions-prefill.controller';
import { MissionsPrefillRepository } from './missions-prefill.repository';
import { MissionsPrefillService } from './missions-prefill.service';
import { MissionsController } from './missions.controller';
import { MissionsRepository } from './missions.repository';
import { MissionsService } from './missions.service';
import { RoleRatesRepository } from './role-rates.repository';
import { RoleRatesService } from './role-rates.service';
import { CommonAccountsModule } from '@common/accounts/accounts.module';
import { CommonCompaniesModule } from '@common/companies/companies.module';
import { CommonHubspotModule } from '@common/hubspot/hubspot.module';
import { CommonMailModule } from '@common/mail/mail.module';
import { CommonMissionsModule } from '@common/missions/missions.module';
import { CommonRBACModule } from '@common/rbac/rbac.module';
import { CommonSlackModule } from '@common/slack/slack.module';
import { CommonTalentIndustriesModule } from '@common/talent-industries/talent-industries.module';
import { CommonUsersModule } from '@common/users/users.module';
import { ClientsModule } from '@lib/clients/clients.module';
import { GuardsModule } from '@lib/guards/guards.module';

@Module({
  imports: [
    GuardsModule,
    ClientsModule,
    CommonMailModule,
    CommonRBACModule,
    CommonSlackModule,
    CommonUsersModule,
    CommonHubspotModule,
    CommonMissionsModule,
    CommonAccountsModule,
    CommonCompaniesModule,
    CommonTalentIndustriesModule,
  ],
  controllers: [MissionsController, MissionsPrefillController, AdminMissionsController],
  providers: [
    MissionsService,
    MissionsRepository,
    AdminMissionsService,
    MissionsPrefillService,
    MissionsPrefillRepository,
    RoleRatesService,
    RoleRatesRepository,
  ],
  exports: [MissionsService],
})
export class MissionsModule {}
