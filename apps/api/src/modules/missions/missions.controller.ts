import { Controller, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { apiContract, MISSION_EDITABLE_STATUSES } from '@packages/contracts';
import { tsR<PERSON><PERSON><PERSON><PERSON>, TsRest<PERSON>and<PERSON> } from '@ts-rest/nest';
import { ok } from 'neverthrow';

import { MissionsService } from './missions.service';
import { RoleRatesService } from './role-rates.service';
import { CommonHubspotService } from '@common/hubspot/hubspot.service';
import { CommonRBACService } from '@common/rbac/rbac.service';
import { PolicyAction } from '@common/rbac/types';
import { CommonUsersRepository } from '@common/users/users.repository';
import { SentryService } from '@lib/global/sentry.service';
import { RateCalculator } from '@modules/missions/helpers/rate';

import { Errors, CodedError } from '@lib/errors';
import { AccountJwtGuard, AuthenticatedAccountRequest } from '@lib/guards/account-jwt.guard';
import { AdminJwtGuard } from '@lib/guards/admin-jwt.guard';

type MissionAccessErrorResponseStatus = HttpStatus.FORBIDDEN | HttpStatus.NOT_FOUND | HttpStatus.INTERNAL_SERVER_ERROR;
type MissionAccessErrorResponse = { status: MissionAccessErrorResponseStatus; body: { message: string } };

type CheckPermissionGetMissionErrorCodes =
  | 'CHECK_PERMISSION_GET_MISSION_UNAUTHORIZED'
  | 'CHECK_PERMISSION_GET_MISSION_RBAC_API_ERROR'
  | 'CHECK_PERMISSION_GET_MISSION_NOT_FOUND'
  | 'CHECK_PERMISSION_GET_MISSION_DB_ERROR';

@Controller()
@UseGuards(AccountJwtGuard)
export class MissionsController {
  private checkPermissionAndGetMission = async (userId: string, accountId: string, missionId: string, action: PolicyAction) => {
    const [permissionResult, missionResult] = await Promise.all([
      this.rbacService.checkClientPermission(userId, accountId, `mission:${missionId}`, action),
      this.missionsService.getMissionById(missionId),
    ]);

    if (permissionResult.isErr()) {
      const error = permissionResult.error;

      if (error.code === 'CHECK_CLIENT_PERMISSION_API_ERROR') {
        return Errors.createError<CheckPermissionGetMissionErrorCodes>('CHECK_PERMISSION_GET_MISSION_RBAC_API_ERROR', {
          isUnexpectedError: error.isUnexpectedError,
          originalError: error.isUnexpectedError ? error.originalError : undefined,
        });
      }

      return Errors.createError<CheckPermissionGetMissionErrorCodes>('CHECK_PERMISSION_GET_MISSION_UNAUTHORIZED');
    }

    if (missionResult.isErr()) {
      const error = missionResult.error;

      if (error.code === 'FIND_MISSION_BY_ID_DB_ERROR') {
        return Errors.createError<CheckPermissionGetMissionErrorCodes>('CHECK_PERMISSION_GET_MISSION_DB_ERROR', {
          isUnexpectedError: error.isUnexpectedError,
          originalError: error.isUnexpectedError ? error.originalError : undefined,
        });
      }

      return Errors.createError<CheckPermissionGetMissionErrorCodes>('CHECK_PERMISSION_GET_MISSION_NOT_FOUND');
    }

    return ok(missionResult.value);
  };

  private mapMissionAccessErrorResponse = (error: CodedError<CheckPermissionGetMissionErrorCodes>): MissionAccessErrorResponse => {
    switch (error.code) {
      case 'CHECK_PERMISSION_GET_MISSION_UNAUTHORIZED':
        return {
          status: HttpStatus.FORBIDDEN,
          body: { message: 'Access denied. Ask your workspace admin to grant you access to this mission.' },
        };
      case 'CHECK_PERMISSION_GET_MISSION_NOT_FOUND':
        return {
          status: HttpStatus.NOT_FOUND,
          body: { message: 'Not Found' },
        };
      case 'CHECK_PERMISSION_GET_MISSION_DB_ERROR':
      case 'CHECK_PERMISSION_GET_MISSION_RBAC_API_ERROR':
        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          body: { message: 'Internal server error. Please contact support.' },
        };
    }
  };

  private hardDeleteMission = async (endpoint: string, missionId: string) => {
    const result = await this.missionsService.deleteMission(missionId, true);

    if (result.isErr()) {
      const error = result.error;
      const isUnexpectedError = error.isUnexpectedError;

      if (isUnexpectedError) {
        this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/hardDeleteMission][${endpoint}] - ${error.code}`, error.originalError);
      }
    }
  };

  constructor(
    private readonly missionsService: MissionsService,
    private readonly roleRateService: RoleRatesService,
    private readonly hubspotService: CommonHubspotService,
    private readonly usersRepository: CommonUsersRepository,
    private readonly sentryService: SentryService,
    private readonly rbacService: CommonRBACService
  ) {}

  @TsRestHandler(apiContract.missions.getMission)
  getMissionById(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.getMission, async ({ params }) => {
      const missionId = params.id;

      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, missionId, 'read');
      const user = await this.usersRepository.findUserById(req.user.id);

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      return {
        status: HttpStatus.OK,
        body: {
          mid: missionId,
          status: mission.status,
          title: mission.title,
          logoURL: mission.logoURL,
          videoURL: mission.videoURL,
          description: mission.description,
          companyStory: mission.companyStory,
          plannedStart: mission.plannedStart,
          overlapMinutes: mission.overlapMinutes,
          timezone: mission.timezone,
          roles: mission.roles.map((role) => {
            const markup = role.markup ?? 0.43; // TODO: Update to default based on the company, smb or enterprise

            const hourlyBudget = role.builderRateMax ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderRateMax, markup) : null;
            const monthlyBudget = role.builderMonthlyRateMax ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderMonthlyRateMax, markup) : null;

            return {
              id: role.id,
              status: role.status,
              categoryId: role.categoryId,
              assignedUser: role.assignedUser,
              headline: role.headline,
              isFullTimeRetainer: role.isFullTimeRetainer,
              markup: user?.isAdmin ? role.markup : null,
              budget: role.isFullTimeRetainer ? monthlyBudget : hourlyBudget,
              requiredSkills: role.requiredSkills,
              preferredSkills: role.preferredSkills,
              locations: role.locations,
              availability: role.availability,
              customQuestions: role.customQuestions,
            };
          }),
        },
      };
    });
  }

  @TsRestHandler(apiContract.missions.getRoleCategories)
  getRoleCategories(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.getRoleCategories, async () => {
      const result = await this.missionsService.getRoleCategories();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/getRoleCategories] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ALL_ROLE_CATEGORIES_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @TsRestHandler(apiContract.missions.getTalentSkills)
  getTalentSkills(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.getTalentSkills, async () => {
      const result = await this.missionsService.getTalentSkills();

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/getTalentSkills] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_ALL_TALENT_SKILLS_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }

  @UseGuards(AdminJwtGuard)
  @TsRestHandler(apiContract.missions.getHubspotDeal)
  getHubspotDeal(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.getHubspotDeal, async ({ params }) => {
      const result = await this.hubspotService.getDealById(params.dealId);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/getHubspotDeal] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'GET_DEAL_BY_ID_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      const deal = result.value;

      return {
        status: HttpStatus.OK,
        body: {
          id: deal.id,
          name: deal.name,
          createdAt: deal.createdAt,
        },
      };
    });
  }

  @TsRestHandler(apiContract.missions.createMission)
  createMission(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.createMission, async ({ body }) => {
      const createMissionResult = await this.missionsService.createMission(body);

      if (createMissionResult.isErr()) {
        const error = createMissionResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/createMission] - ${error.code}`, error.originalError);
        }

        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          body: { message: 'An error occurred. Please contact support.' },
        };
      }

      const newMission = createMissionResult.value;

      const grantResult = await this.rbacService.grantRole(req.user.id, req.account.id, { role: 'missionadmin', missionId: newMission.mid });

      if (grantResult.isErr()) {
        const error = grantResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/createMission] - ${error.code}`, error.originalError);
        }

        // In case the granting fails, the mission can be hard deleted
        void this.hardDeleteMission('createMission', newMission.mid);

        return {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          body: { message: 'An error occurred. Please contact support.' },
        };
      }

      return {
        status: HttpStatus.CREATED,
        body: {
          mid: newMission.mid,
        },
      };
    });
  }

  @TsRestHandler(apiContract.missions.updateMission)
  updateMission(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.updateMission, async ({ params, body }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      if (!MISSION_EDITABLE_STATUSES.includes(mission.status)) {
        return {
          status: HttpStatus.UNPROCESSABLE_ENTITY,
          body: {
            message: 'Mission is not in editable status',
          },
        };
      }

      const updatedMissionResult = await this.missionsService.updateMission(params.id, body);

      if (updatedMissionResult.isErr()) {
        const error = updatedMissionResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/updateMission] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      const updatedMission = updatedMissionResult.value;

      await this.missionsService.processAssetsChanges(mission, updatedMission);

      return {
        status: HttpStatus.OK,
      };
    });
  }

  @TsRestHandler(apiContract.missions.confirmMission)
  confirmMission(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.confirmMission, async ({ params, body }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      const confirmMissionResult = await this.missionsService.confirmMission(params.id, body);

      if (confirmMissionResult.isErr()) {
        const error = confirmMissionResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/confirmMission] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      const confirmedMission = confirmMissionResult.value;

      await this.missionsService.processAssetsChanges(mission, confirmedMission);

      return {
        status: HttpStatus.OK,
        body: undefined,
      };
    });
  }

  @UseGuards(AdminJwtGuard)
  @TsRestHandler(apiContract.missions.publishMission)
  publishMission(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.publishMission, async ({ params, body }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      if (mission.status !== 'Formation') {
        return {
          status: HttpStatus.BAD_REQUEST,
          body: {
            message: 'Mission is not in formation status',
          },
        };
      }

      const publishedMissionResult = await this.missionsService.publishMission(params.id, body);

      if (publishedMissionResult.isErr()) {
        const error = publishedMissionResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/publishMission] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      const publishedMission = publishedMissionResult.value;

      const createRoleDealsResult = await this.missionsService.createRoleDeals(
        publishedMission,
        publishedMission.roles.map((role) => role.id),
        req.user.id
      );

      if (createRoleDealsResult.isErr()) {
        const error = createRoleDealsResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/publishMission] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'FIND_USER_BY_ID_NOT_FOUND':
          case 'GET_OWNER_BY_EMAIL_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: { message: 'User/owner was not found.' },
            };
          case 'CREATE_ROLE_DEALS_FAILED_TO_CREATE_ALL_ROLE_DEALS':
          case 'FIND_USER_BY_ID_DB_ERROR':
          case 'FIND_ALL_ROLE_CATEGORIES_DB_ERROR':
          case 'GET_OWNER_BY_EMAIL_API_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: undefined,
      };
    });
  }

  @TsRestHandler(apiContract.missions.requestNewRole)
  requestNewRole(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.requestNewRole, async ({ params, body }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      if (MISSION_EDITABLE_STATUSES.includes(mission.status)) {
        return {
          status: HttpStatus.UNPROCESSABLE_ENTITY,
          body: {
            message: 'Mission is in editable status',
          },
        };
      }

      await this.missionsService.requestNewRole(params.id, body);
      return {
        status: HttpStatus.OK,
      };
    });
  }

  @UseGuards(AdminJwtGuard)
  @TsRestHandler(apiContract.missions.approveRoleRequest)
  approveRoleRequest(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.approveRoleRequest, async ({ params }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const updatedMissionResult = await this.missionsService.approveRoleRequest(params.id, params.roleId);

      if (updatedMissionResult.isErr()) {
        const error = updatedMissionResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/approveRoleRequest] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_ROLE_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      const updatedMission = updatedMissionResult.value;

      await this.missionsService.createRoleDeals(updatedMission, [params.roleId], req.user.id);

      return {
        status: HttpStatus.OK,
        body: undefined,
      };
    });
  }

  @UseGuards(AdminJwtGuard)
  @TsRestHandler(apiContract.missions.rejectRoleRequest)
  rejectRoleRequest(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.rejectRoleRequest, async ({ params }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const rejectRoleRequestResult = await this.missionsService.rejectRoleRequest(params.id, params.roleId);

      if (rejectRoleRequestResult.isErr()) {
        const error = rejectRoleRequestResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/rejectRoleRequest] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'UPDATE_MISSION_ROLE_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: undefined,
      };
    });
  }

  @TsRestHandler(apiContract.missions.requestRoleRemoval)
  requestRoleRemoval(@Req() req: AuthenticatedAccountRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.requestRoleRemoval, async ({ params, body }) => {
      const missionResult = await this.checkPermissionAndGetMission(req.user.id, req.account.id, params.id, 'write');

      if (missionResult.isErr()) {
        return this.mapMissionAccessErrorResponse(missionResult.error);
      }

      const mission = missionResult.value;

      if (!mission) {
        return {
          status: HttpStatus.UNPROCESSABLE_ENTITY,
          body: {
            message: 'Mission not found. Please contact support.',
          },
        };
      }

      const requestRoleRemovalResult = await this.missionsService.requestRoleRemoval(params.id, params.roleId, body);

      if (requestRoleRemovalResult.isErr()) {
        const error = requestRoleRemovalResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/requestRoleRemoval] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'REQUEST_ROLE_REMOVAL_ACCOUNT_ID_MISSING':
            return {
              status: HttpStatus.UNPROCESSABLE_ENTITY,
              body: { message: 'Mission not associated with any account, please contact support.' },
            };
          case 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.UNPROCESSABLE_ENTITY,
              body: { message: 'User not found.' },
            };
          case 'REQUEST_ROLE_REMOVAL_ROLE_NOT_FOUND':
            return {
              status: HttpStatus.UNPROCESSABLE_ENTITY,
              body: { message: 'Role not found.' },
            };
          case 'SEND_EMAIL_FAILED_AFTER_MAX_RETRIES':
          case 'SEND_EMAIL_VALIDATION_EMAIL_DATA_ERROR':
          case 'UPDATE_MISSION_ROLE_DB_ERROR':
          case 'FIND_ACTIVE_USER_BY_ID_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
      };
    });
  }

  @TsRestHandler(apiContract.missions.getRoleRateGuidance)
  getRoleRateGuidance(): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missions.getRoleRateGuidance, async ({ query }) => {
      const result = await this.roleRateService.getRoleRateGuidance(query);

      if (result.isErr()) {
        const error = result.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(`[modules/missions/missions.controller/getRoleRateGuidance] - ${error.code}`, error.originalError);
        }

        switch (error.code) {
          case 'GET_ROLE_RATE_RANGE_NO_RATES':
            return {
              status: HttpStatus.NOT_FOUND,
            };
          case 'FIND_HOURLY_RATES_FOR_ROLE_CATEGORY_DB_ERROR':
          case 'GET_ROLE_RATE_GUIDANCE_CATEGORY_ID_OR_MARKUP_PERCENTAGE_NOT_FOUND':
          case 'GET_ROLE_RATE_GUIDANCE_ERROR_RATE_BOUNDS':
          case 'GET_ROLE_RATE_GUIDANCE_ERROR_MARKUP_BOUNDS':
          case 'CALCULATE_PERCENTILE_NOT_BETWEEN_0_AND_100':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: { message: 'An error occurred. Please contact support.' },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: result.value,
      };
    });
  }
}
