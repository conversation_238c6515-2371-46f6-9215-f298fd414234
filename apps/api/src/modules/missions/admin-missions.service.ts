import { Injectable } from '@nestjs/common';
import {
  GetAdminSettingsDto,
  GetMissionSettingsDto,
  GetRoleSettingsDto,
  RoleSettingDto,
  UpdateAdminSettingsDto,
  UpdateMissionSettingsDto,
} from '@packages/contracts';
import { ok } from 'neverthrow';

import { MissionsRepository } from './missions.repository';
import { CommonTalentSkillsRepository } from '@common/missions/talent-skills.repository';
import { CommonTalentIndustriesRepository } from '@common/talent-industries/talent-industries.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { PlatformServiceClient } from '@lib/clients/platform/platform.client';

import { UpdateMissionData } from '@lib/clients/platform/platform.types';

import { RateCalculator } from './helpers/rate';

export type MissionIndustry = {
  id: string;
  name: string;
};

@Injectable()
export class AdminMissionsService {
  private readonly TFS_OWNERS_EMAILS: string[];

  constructor(
    private readonly platformClient: PlatformServiceClient,
    private readonly commonUsersRepository: CommonUsersRepository,
    private readonly commonTalentIndustriesRepository: CommonTalentIndustriesRepository,
    private readonly commonTalentSkillsRepository: CommonTalentSkillsRepository,
    private readonly missionsRepository: MissionsRepository,
    configService: ConfigService
  ) {
    this.TFS_OWNERS_EMAILS = configService.get('TFS_EMAILS');
  }

  getAllMissionIndustries = async () => {
    const talentIndustriesResult = await this.commonTalentIndustriesRepository.findAllTalentIndustries();

    if (talentIndustriesResult.isErr()) {
      return talentIndustriesResult;
    }

    const missionIndustries: MissionIndustry[] = talentIndustriesResult.value.map((t) => ({ id: t.id, name: t.name }));
    return ok(missionIndustries);
  };

  getTfsOwners = async () => {
    const tfsOwnersResult = await this.commonUsersRepository.findUsersByEmails(this.TFS_OWNERS_EMAILS);

    if (tfsOwnersResult.isErr()) {
      return tfsOwnersResult;
    }

    const tfsOwners = tfsOwnersResult.value;

    const tfsOwnersWithRequiredInfo = tfsOwners.map((t) => ({
      id: t.id,
      firstName: t.firstName!,
      lastName: t.lastName!,
      profilePictureURL: t.pictureURL,
    }));

    return ok(tfsOwnersWithRequiredInfo);
  };

  getRoleCategories = async () => {
    return await this.missionsRepository.findAllRoleCategories();
  };

  getRoleSkills = async () => {
    return await this.commonTalentSkillsRepository.findAllTalentSkills();
  };

  getUsersByNameQuery = async (nameQuery: string) => {
    const usersResult = await this.commonUsersRepository.findBasicUsersDetailsByQueryName(nameQuery);

    if (usersResult.isErr()) {
      return usersResult;
    }

    const users = usersResult.value.map((u) => ({ id: u.id, name: `${u.firstName} ${u.lastName}`, pictureUrl: u.pictureURL ?? undefined }));

    return ok(users);
  };

  getAdminSettings = async (missionId: string) => {
    // TODO: To change in V2, temporary for V1
    const missionResult = await this.platformClient.getMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const managers: GetAdminSettingsDto['managers'] = mission.managers
      .filter((m): m is typeof m & { user: NonNullable<typeof m.user> } => !!m.user)
      .map((m) => ({
        accessMode: m.accessMode,
        username: m.username,
        user: {
          firstName: m.user.firstName!,
          lastName: m.user.lastName!,
          uid: m.user.uid!,
          profilePictureURL: m.user.profilePictureURL,
        },
      }));

    return ok<GetAdminSettingsDto>({
      missionStatus: mission.status,
      applyStatus: mission.applyStatus,
      timesheetPeriods: mission.billingPeriod ?? undefined,
      tfsOwner: mission.owner?.uid,
      invoicePO: mission.invoicing?.purchaseOrderNumber,
      billingAddress: mission?.billingAccount?.billingInfo?.address ?? {
        line1: '',
        line2: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
      },
      paymentTerms: mission?.paymentTerms?.due,
      generateContracts: false,
      managers,
    });
  };

  updateAdminSettings = async (missionId: string, updateAdminSettingsDto: UpdateAdminSettingsDto) => {
    const { paymentTerms, ...rest } = updateAdminSettingsDto;

    // TODO: To change in V2, temporary for V1, since the call requires roles
    const missionResult = await this.platformClient.getMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const existingMission = missionResult.value;

    const missionUpdatedValues: UpdateMissionData = {
      ...rest,
      description: existingMission.description,
      title: existingMission.title,
      roles: existingMission.roles.map((role) => {
        role.cid = role.category.cid;
        return role;
      }),
      billingPeriod: existingMission.billingPeriod,
    };

    if (paymentTerms) {
      missionUpdatedValues.paymentTerms = { due: paymentTerms };
    }

    return await this.platformClient.updateMissionById(missionId, missionUpdatedValues);
  };

  getMissionSettings = async (missionId: string) => {
    // TODO: To change in V2, temporary for V1
    const missionResult = await this.platformClient.getMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const missionResources = mission.attachedLinks?.map((r) => ({ title: r.title, url: r.URL })) ?? [];

    return ok<GetMissionSettingsDto>({
      videoUrl: mission.videoURL,
      companyLogo: mission.logoURL,
      industries: mission.industries ?? [],
      missionName: mission.title,
      companyDescription: mission.shortCompanyDescription ?? '',
      missionDescription: mission.description,
      isInternalMission: mission.internalMission ?? false,
      duration: mission.expectedDurationMonths ?? 1,
      isUnderWraps: mission.hidden,
      missionResources,
    });
  };

  updateMissionSettings = async (missionId: string, updateMissionSettingsDto: UpdateMissionSettingsDto) => {
    const attachedLinks = updateMissionSettingsDto.missionResources?.map((r) => ({ title: r.title, URL: r.url })) ?? [];

    // TODO: To change in V2, temporary for V1, since the call requires roles
    const missionResult = await this.platformClient.getMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const existingMission = missionResult.value;

    const missionUpdatedValues: UpdateMissionData = {
      videoURL: updateMissionSettingsDto.videoUrl,
      logoURL: updateMissionSettingsDto.companyLogo,
      talentIndustryIds: updateMissionSettingsDto.industries.map((ti) => ti.id),
      title: updateMissionSettingsDto.missionName,
      shortCompanyDescription: updateMissionSettingsDto.companyDescription,
      description: updateMissionSettingsDto.missionDescription,
      internalMission: updateMissionSettingsDto.isInternalMission,
      expectedDurationMonths: updateMissionSettingsDto.duration ?? 1,
      hidden: updateMissionSettingsDto.isUnderWraps,
      attachedLinks,
      roles: existingMission.roles.map((role) => {
        role.cid = role.category.cid;
        return role;
      }),
      billingPeriod: existingMission.billingPeriod,
    };

    return await this.platformClient.updateMissionById(missionId, missionUpdatedValues);
  };

  getRoleSettings = async (missionId: string) => {
    // TODO: To change in V2, temporary for V1
    const missionResult = await this.platformClient.getMissionById(missionId);

    if (missionResult.isErr()) {
      return missionResult;
    }

    const mission = missionResult.value;

    const openRoles: RoleSettingDto[] = [];
    const activeRoles: RoleSettingDto[] = [];
    const endedRoles: RoleSettingDto[] = [];
    const canceledRoles: RoleSettingDto[] = [];

    for (const role of mission.roles) {
      const firstCustomQuestion = role.customQuestions?.[0];

      const customQuestion = firstCustomQuestion
        ? {
            qid: firstCustomQuestion.qid,
            text: firstCustomQuestion.text,
            isRequired: firstCustomQuestion.isRequired ?? false,
            isVisible: firstCustomQuestion.isVisible ?? false,
          }
        : undefined;

      // TODO: Update to default based on the company, smb or enterprise
      // in the database, values saved as margin are actually markups
      const roleMarkup = role.margin ?? 0.43;

      const roleSetting: RoleSettingDto = {
        rid: role.rid,
        status: role.status,
        title: role.category.title,
        visibilityStatus: role.visibility?.visibilityStatus ?? 'All',
        description: role.headlineHtml ?? '',
        customQuestion,
        isNiche: role.isNiche ?? false,
        markup: roleMarkup,
        // hourly rates
        builderRateMin: role.builderRateMin,
        builderRateMax: role.builderRateMax,
        clientRateMin: role.builderRateMin ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderRateMin, roleMarkup) : undefined,
        clientRateMax: role.builderRateMax ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderRateMax, roleMarkup) : undefined,
        collectBuilderHourlyRate: role.budgetSettings?.requireHourlyRate ?? false,
        showClientHourlyBudget: role.budgetSettings?.showHourlyBudget ?? false,
        // monthly rates
        builderMonthlyRateMin: role.builderMonthlyRateMin,
        builderMonthlyRateMax: role.builderMonthlyRateMax,
        clientMonthlyRateMin: role.builderMonthlyRateMin
          ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderMonthlyRateMin, roleMarkup)
          : undefined,
        clientMonthlyRateMax: role.builderMonthlyRateMax
          ? RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderMonthlyRateMax, roleMarkup)
          : undefined,
        collectBuilderMonthlyRate: role.budgetSettings?.requireMonthlyRate ?? false,
        showClientMonthlyBudget: role.budgetSettings?.showMonthlyBudget ?? false,
        // additional role info
        minimumHoursPerWeek: role.availability?.weeklyHoursAvailable,
        timezone: role.workingHours?.name,
        workingHoursStartTime: role.workingHours?.daily?.[0].startTime,
        workingHoursEndTime: role.workingHours?.daily?.[0].endTime,
        hoursOverlapMinutes: role.workingHours?.numberOfMinutesOverlap,
        requiredSkills: role.requiredSkills?.map((role) => ({ id: role.talentSkillId, name: role.talentSkillName! })) ?? [],
        preferredSkills: role.preferredSkills?.map((role) => ({ id: role.talentSkillId, name: role.talentSkillName! })) ?? [],
        allowedCountries: role.locations ?? [],
        automatedStatusesAsignmentDisabled: role.automatedStatusesDisabled ?? false,
        roleHiddenFromBuilders: [], // TODO: update later since it's a new feature
        readyForReview: role.readyForReview ?? false,
      };

      const roleStatus = role.status;

      if (roleStatus === 'Open') {
        openRoles.push(roleSetting);
      } else if (roleStatus === 'Active') {
        activeRoles.push(roleSetting);
      } else if (roleStatus === 'Ended') {
        endedRoles.push(roleSetting);
      } else if (roleStatus === 'Canceled') {
        canceledRoles.push(roleSetting);
      }
    }

    return ok<GetRoleSettingsDto>({
      openRoles,
      activeRoles,
      endedRoles,
      canceledRoles,
      missionStatus: mission.status,
    });
  };
}
