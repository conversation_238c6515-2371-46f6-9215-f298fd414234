import { Cache } from '@nestjs/cache-manager';
import { Injectable } from '@nestjs/common';
import { GetRateGuidaceSchemaDto, QueryRateGuidaceSchemaDto } from '@packages/contracts';
import { ok } from 'neverthrow';

import { RateCalculator } from '@modules/missions/helpers/rate';
import { RoleRatesRepository } from '@modules/missions/role-rates.repository';

import { Errors } from '@lib/errors';

type RoleRateStatistics = {
  total: number;
  percentiles: number[];
};

type RoleRateRange = {
  min: number;
  max: number;
};

type GetRoleRateGuidanceErrorCodes =
  | 'GET_ROLE_RATE_GUIDANCE_CATEGORY_ID_OR_MARKUP_PERCENTAGE_NOT_FOUND'
  | 'GET_ROLE_RATE_GUIDANCE_ERROR_RATE_BOUNDS'
  | 'GET_ROLE_RATE_GUIDANCE_ERROR_MARKUP_BOUNDS';

type GetRoleRateRangeErrorCodes = 'GET_ROLE_RATE_RANGE_NO_RATES';

type CalculatePercentileErrorCodes = 'CALCULATE_PERCENTILE_NOT_BETWEEN_0_AND_100';

@Injectable()
export class RoleRatesService {
  private readonly CACHE_TTL_MS = 24 * 60 * 60 * 1000; // 24 hours
  private readonly MONTHS_LOOKBACK = 6;
  private readonly MIN_RATES_REQUIRED = 3;
  private readonly ROUNDING_FACTOR = 5;

  private getCacheKey = (categoryId: string): string => `rate-guidance:${categoryId}`;

  constructor(
    private readonly cacheManager: Cache,
    private readonly roleRatesRepository: RoleRatesRepository
  ) {}

  async getRoleRateGuidance(query: QueryRateGuidaceSchemaDto) {
    const { categoryId, markupPercentage } = query;

    if (!categoryId || !markupPercentage) {
      return Errors.createError<GetRoleRateGuidanceErrorCodes>('GET_ROLE_RATE_GUIDANCE_CATEGORY_ID_OR_MARKUP_PERCENTAGE_NOT_FOUND');
    }

    const roleRateRangeResult = await this.getRoleRateRange(categoryId);

    if (roleRateRangeResult.isErr()) {
      return roleRateRangeResult;
    }

    const roleRateRange = roleRateRangeResult.value;

    const markup = RateCalculator.getMarkupFromPercentage(markupPercentage);

    if (!markup) {
      return Errors.createError<GetRoleRateGuidanceErrorCodes>('GET_ROLE_RATE_GUIDANCE_ERROR_MARKUP_BOUNDS');
    }

    const lowerBound = Math.round(RateCalculator.getClientRateFromBuilderRateAndMarkup(roleRateRange.min, markup));
    const upperBound = Math.round(RateCalculator.getClientRateFromBuilderRateAndMarkup(roleRateRange.max, markup));

    if (lowerBound <= 0 || upperBound <= 0 || lowerBound >= upperBound) {
      return Errors.createError<GetRoleRateGuidanceErrorCodes>('GET_ROLE_RATE_GUIDANCE_ERROR_RATE_BOUNDS');
    }

    return ok<GetRateGuidaceSchemaDto>({
      lowerBound,
      upperBound,
    });
  }

  async getRoleRateRange(categoryId: string) {
    const cacheKey = this.getCacheKey(categoryId);
    const cached = await this.cacheManager.get<RoleRateRange>(cacheKey);

    if (cached) {
      return ok<RoleRateRange>(cached);
    }

    const startDateTime = new Date();
    const endDateTime = new Date();

    startDateTime.setMonth(startDateTime.getMonth() - this.MONTHS_LOOKBACK);

    const ratesResult = await this.roleRatesRepository.findHourlyRatesForRoleCategory(categoryId, startDateTime, endDateTime);

    if (ratesResult.isErr()) {
      return ratesResult;
    }

    const rates = ratesResult.value;

    if (!rates || rates.length < this.MIN_RATES_REQUIRED) {
      return Errors.createError<GetRoleRateRangeErrorCodes>('GET_ROLE_RATE_RANGE_NO_RATES');
    }

    const statisticsResult = this.calculateStatistics(rates);

    if (statisticsResult.isErr()) {
      return statisticsResult;
    }

    const statistics = statisticsResult.value;

    const min = Math.round(statistics.percentiles[2] / this.ROUNDING_FACTOR) * this.ROUNDING_FACTOR;
    const max = Math.round(statistics.percentiles[8] / this.ROUNDING_FACTOR) * this.ROUNDING_FACTOR;

    const result = {
      min,
      max,
    };

    await this.cacheManager.set(cacheKey, result, this.CACHE_TTL_MS);

    return ok<RoleRateRange>(result);
  }

  calculatePercentile(sorted: number[], percentile: number) {
    if (sorted.length === 0) {
      return ok(0);
    }

    if (percentile < 0 || percentile > 100) {
      return Errors.createError<CalculatePercentileErrorCodes>('CALCULATE_PERCENTILE_NOT_BETWEEN_0_AND_100');
    }

    if (sorted.length === 1) {
      return ok(sorted[0]);
    }

    const index = (percentile / 100) * (sorted.length - 1);
    const lower = Math.floor(index);
    const upper = Math.ceil(index);

    if (lower === upper) {
      return ok(sorted[lower]);
    }

    const fraction = index - lower;
    return ok(sorted[lower] + (sorted[upper] - sorted[lower]) * fraction);
  }

  calculateStatistics(rates: number[]) {
    if (rates.length === 0) {
      return ok<RoleRateStatistics>({ total: 0, percentiles: new Array<number>(11).fill(0) });
    }

    const validRates = rates.filter((rate) => rate > 0);
    const sorted = [...validRates].sort((a, b) => a - b);

    const percentiles = [];

    for (let i = 0; i <= 100; i += 10) {
      const percentileResult = this.calculatePercentile(sorted, i);

      if (percentileResult.isErr()) {
        return percentileResult;
      }

      const percentile = percentileResult.value;
      percentiles.push(percentile);
    }

    return ok<RoleRateStatistics>({
      total: validRates.length,
      percentiles,
    });
  }
}
