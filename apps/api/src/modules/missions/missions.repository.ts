import { <PERSON><PERSON><PERSON><PERSON>, Prisma, RoleCategory, TalentCategory } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ResultAsync, ok } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

export type RoleCategoryInfo = Pick<RoleCategory, 'id' | 'title'>;
export type TalentSkillInfo = Pick<TalentCategory, 'id' | 'name'>;

type FindMissionApplicationByIdErrorCodes = 'FIND_MISSION_APPLICATION_BY_ID_NOT_FOUND' | 'FIND_MISSION_APPLICATION_BY_ID_DB_ERROR';

type CreateMissionErrorCodes = 'CREATE_MISSION_DB_ERROR';

type DeleteMissionErrorCodes = 'DELETE_MISSION_DB_ERROR' | 'DELETE_MISSION_DB_UPDATE_ERROR';

type FindAllRoleCategoriesErrorCodes = 'FIND_ALL_ROLE_CATEGORIES_DB_ERROR';

type UpdateMissionRoleErrorCodes = 'UPDATE_MISSION_ROLE_DB_ERROR';

type FindMissionByIdErrorCodes = 'FIND_MISSION_BY_ID_DB_ERROR' | 'FIND_MISSION_BY_ID_NOT_FOUND';

type FindRoleCategoryByIdErrorCodes = 'FIND_ROLE_CATEGORY_BY_ID_DB_ERROR' | 'FIND_ROLE_CATEGORY_BY_ID_NOT_FOUND';

type FindAllTalentSkillsErrorCodes = 'FIND_ALL_TALENT_SKILLS_DB_ERROR';

type UpdateMissionErrorCodes = 'UPDATE_MISSION_DB_ERROR';

type AddRoleToMissionErrorCodes = 'ADD_ROLE_TO_MISSION_DB_ERROR';

@Injectable()
export class MissionsRepository {
  constructor(private readonly prisma: DbService) {}

  findMissionById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.findUnique({
        where: {
          mid: id,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionByIdErrorCodes>('FIND_MISSION_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const mission = result.value;

    if (!mission) {
      return Errors.createError<FindMissionByIdErrorCodes>('FIND_MISSION_BY_ID_DB_ERROR');
    }

    return ok(mission);
  };

  findAllRoleCategories = async () => {
    const result = await ResultAsync.fromPromise(
      this.prisma.roleCategory.findMany({
        select: {
          id: true,
          title: true,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindAllRoleCategoriesErrorCodes>('FIND_ALL_ROLE_CATEGORIES_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok<RoleCategoryInfo[]>(result.value);
  };

  findRoleCategoryById = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.roleCategory.findUnique({ where: { id } }), (e) => new Error(e as string));

    if (result.isErr()) {
      return Errors.createError<FindRoleCategoryByIdErrorCodes>('FIND_ROLE_CATEGORY_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const roleCategory = result.value;

    if (!roleCategory) {
      return Errors.createError<FindRoleCategoryByIdErrorCodes>('FIND_ROLE_CATEGORY_BY_ID_NOT_FOUND');
    }

    return ok(roleCategory);
  };

  findAllTalentSkills = async () => {
    const result = await ResultAsync.fromPromise(
      this.prisma.talentCategory.findMany({
        where: {
          nodeType: 'skill',
          parentTalentCategoryIds: {
            isEmpty: false,
          },
        },
        select: {
          id: true,
          name: true,
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindAllTalentSkillsErrorCodes>('FIND_ALL_TALENT_SKILLS_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok<TalentSkillInfo[]>(result.value);
  };

  createMission = async (data: Prisma.MissionCreateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.mission.create({ data }), (e) => new Error(e as string));

    if (result.isErr()) {
      return Errors.createError<CreateMissionErrorCodes>('CREATE_MISSION_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  deleteMission = async (mid: string, hardDelete = false) => {
    if (hardDelete) {
      const result = await ResultAsync.fromPromise(this.prisma.mission.delete({ where: { mid } }), (error) => new Error(error as string));

      if (result.isErr()) {
        return Errors.createError<DeleteMissionErrorCodes>('DELETE_MISSION_DB_ERROR', {
          isUnexpectedError: true,
          originalError: result.error,
        });
      }

      return ok(result.value);
    }

    const result = await ResultAsync.fromPromise(
      this.prisma.mission.update({ where: { mid }, data: { status: MissionStatus.Archived } }),
      (error) => new Error(error as string)
    );

    if (result.isErr()) {
      return Errors.createError<DeleteMissionErrorCodes>('DELETE_MISSION_DB_UPDATE_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  updateMission = async (mid: string, data: Prisma.MissionUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.update({
        where: {
          mid,
        },
        data,
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<UpdateMissionErrorCodes>('UPDATE_MISSION_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  addRoleToMission = async (mid: string, data: Prisma.MissionRoleCreateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.update({
        where: { mid },
        data: {
          roles: {
            push: data,
          },
        },
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<AddRoleToMissionErrorCodes>('ADD_ROLE_TO_MISSION_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  updateMissionRole = async (mid: string, roleId: string, data: Prisma.MissionRoleUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.update({
        where: { mid },
        data: {
          roles: {
            updateMany: {
              where: { id: roleId },
              data,
            },
          },
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateMissionRoleErrorCodes>('UPDATE_MISSION_ROLE_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  findMissionApplicationById = async (aid: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.missionApplication.findUnique({ where: { aid } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionApplicationByIdErrorCodes>('FIND_MISSION_APPLICATION_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const missionApplication = result.value;

    if (!missionApplication) {
      return Errors.createError<FindMissionApplicationByIdErrorCodes>('FIND_MISSION_APPLICATION_BY_ID_NOT_FOUND');
    }

    return ok(missionApplication);
  };
}
