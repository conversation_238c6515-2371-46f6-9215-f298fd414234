import { <PERSON>, HttpStatus, Req, UseGuards } from '@nestjs/common';
import { apiContract } from '@packages/contracts';
import { tsRest<PERSON><PERSON><PERSON>, TsRestHandler } from '@ts-rest/nest';

import { MissionsPrefillService } from './missions-prefill.service';
import { SentryService } from '@lib/global/sentry.service';

import { AuthenticatedRequest, JwtGuard } from '@lib/guards/jwt.guard';

@Controller()
@UseGuards(JwtGuard)
export class MissionsPrefillController {
  constructor(
    private readonly missionsPrefillService: MissionsPrefillService,
    private readonly sentryService: SentryService
  ) {}

  @TsRestHandler(apiContract.missionsPrefill.generateMissionPrefill)
  generateMissionPrefill(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missionsPrefill.generateMissionPrefill, async () => {
      const userId = req.user.id;
      const missionPrefillResult = await this.missionsPrefillService.generateMissionPrefill(userId);

      if (missionPrefillResult.isErr()) {
        const error = missionPrefillResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(
            `[modules/missions/missions-prefill.controller/generateMissionPrefill] userId: ${userId} - ${error.code}`,
            error.originalError
          );
        }

        switch (error.code) {
          case 'GET_USER_REGISTRATION_DATA_USER_REGISTRATION_DATA_NOT_FOUND':
          case 'FIND_USER_BY_ID_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: {
                message: 'User/registration data not found.',
              },
            };
          case 'FIND_USER_BY_ID_DB_ERROR':
          case 'GET_MOST_COMMON_SKILLS_FOR_SPECIALIZATION_DB_ERROR':
          case 'GENERATE_OBJECT_API_ERROR':
          case 'GENERATE_ARRAY_API_ERROR':
          case 'UPSERT_MISSION_PREFILL_DB_ERROR':
          case 'FIND_ALL_ROLE_CATEGORIES_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: {
                message: 'Internal server error, please contact support.',
              },
            };
        }
      }

      const missionPrefill = missionPrefillResult.value;

      return {
        status: HttpStatus.CREATED,
        body: missionPrefill,
      };
    });
  }

  @TsRestHandler(apiContract.missionsPrefill.getMissionPrefill)
  getMissionPrefillByUserId(@Req() req: AuthenticatedRequest): ReturnType<typeof tsRestHandler> {
    return tsRestHandler(apiContract.missionsPrefill.getMissionPrefill, async () => {
      const missionPrefillResult = await this.missionsPrefillService.getMissionPrefillByUserId(req.user.id);

      if (missionPrefillResult.isErr()) {
        const error = missionPrefillResult.error;
        const isUnexpectedError = error.isUnexpectedError;

        if (isUnexpectedError) {
          this.sentryService.logAndCaptureError(
            `[modules/missions/missions-prefill.controller/getMissionPrefillByUserId] - ${error.code}`,
            error.originalError
          );
        }

        switch (error.code) {
          case 'FIND_MISSION_PREFILL_DB_ERROR':
            return {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              body: {
                message: 'Internal server error, please contact support.',
              },
            };
          case 'FIND_MISSION_PREFILL_NOT_FOUND':
            return {
              status: HttpStatus.NOT_FOUND,
              body: {
                message: 'Mission prefill not found.',
              },
            };
        }
      }

      return {
        status: HttpStatus.OK,
        body: missionPrefillResult.value,
      };
    });
  }
}
