import { MissionRoleStatus, MissionStatus } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindHourlyRatesForRoleCategoryErrorCodes = 'FIND_HOURLY_RATES_FOR_ROLE_CATEGORY_DB_ERROR';

@Injectable()
export class RoleRatesRepository {
  private readonly VALID_MISSION_STATUSES: MissionStatus[] = [
    MissionStatus.Published,
    MissionStatus.Pending,
    MissionStatus.Running,
    MissionStatus.ScheduledToEnd,
    MissionStatus.Ended,
  ];

  private readonly VALID_ROLE_STATUSES: MissionRoleStatus[] = [
    MissionRoleStatus.Ended,
    MissionRoleStatus.Active,
    MissionRoleStatus.ScheduledToEnd,
    MissionRoleStatus.Canceled,
  ];

  constructor(private readonly prisma: DbService) {}

  /**
   * Find hourly rates for a specific role category within a date range
   *
   * @param roleCategoryId - The role category ID to find rates for
   * @param startDate - The start date for the search range
   * @param endDate - The end date for the search range
   */
  findHourlyRatesForRoleCategory = async (roleCategoryId: string, startDate: Date, endDate: Date) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.aggregateRaw({
        pipeline: [
          {
            $match: {
              status: { $in: this.VALID_MISSION_STATUSES },
            },
          },
          {
            $project: {
              roles: {
                category: 1,
                status: 1,
                updatedAt: 1,
                hourlyRate: 1,
              },
            },
          },
          {
            $unwind: '$roles',
          },
          {
            $match: {
              'roles.category': { $oid: roleCategoryId },
              'roles.status': { $in: this.VALID_ROLE_STATUSES },
              'roles.updatedAt': { $gte: { $date: startDate }, $lte: { $date: endDate } },
              'roles.hourlyRate': { $ne: null },
            },
          },
          {
            $group: {
              _id: null,
              hourlyRates: { $push: '$roles.hourlyRate' },
            },
          },
        ],
      }),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<FindHourlyRatesForRoleCategoryErrorCodes>('FIND_HOURLY_RATES_FOR_ROLE_CATEGORY_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const rates = result.value as unknown as {
      _id: null;
      hourlyRates: number[];
    }[];

    return ok(rates?.[0]?.hourlyRates ?? []);
  };
}
