import { MissionPrefill, RegisterRequestType, MissionPrefillRole, Solution } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { MISSION_PLANNED_START_VALUES } from '@packages/contracts';
import { err, ok } from 'neverthrow';
import { z } from 'zod';

import { MissionsPrefillRepository, MissionPrefillRoleSkills } from './missions-prefill.repository';
import { RoleCategoryInfo } from './missions.repository';
import { MissionsService } from './missions.service';
import { CommonCompaniesService, CompanyEnrichmentData } from '@common/companies/companies.service';
import { CommonUsersService, UserRegistrationData } from '@common/users/users.service';
import { OpenAIClient } from '@lib/clients/open-ai.client';

export type MissionPrefillInputs = {
  registrationData?: UserRegistrationData;
  enrichmentData?: CompanyEnrichmentData;
  requestedRoles: RoleCategoryInfo[];
  solution?: Solution;
};

export type PrefillLLMResponse = z.infer<typeof prefillLLMSchema>;
export type PrefillRoleLLMResponse = z.infer<typeof prefillRoleLLMSchema>;

export const requestTypeLabel: Record<RegisterRequestType, string> = {
  BUILD_PRODUCT: 'build a new product',
  SUPERCHARGE: 'add team members to an existing project',
};

const prefillLLMSchema = z.object({
  project: z.object({
    title: z.string().nullable(),
    description: z.string().nullable(),
    plannedStart: z.enum(MISSION_PLANNED_START_VALUES).nullable(),
  }),
  company: z
    .object({
      name: z.string().nullable(),
      description: z.string().nullable().optional(),
      timezone: z.string().nullable().optional(),
    })
    .optional(),
});

const prefillRoleLLMSchema = z.object({
  categoryId: z.string(),
  headline: z.string().nullable(),
  screeningQuestion: z.string().nullable(),
});

/**
 * Service responsible for generating and managing mission prefill data for users.
 *
 * A mission prefill is unique per user ID and utilizes LLM (Language Model) generated data to assist users in pre-filling the necessary information when creating a new mission.
 * The service ensures that the prefill data is up-to-date and relevant by regenerating the LLM data through various trigger actions initiated by the user in the core platform.
 * This means that the prefill data is overridden with more accurate and relevant information each time the user provides additional input.
 *
 * The trigger actions that initiate the regeneration of LLM data will include:
 * - User registration completed
 * - TFAI transpcript update
 * - User searches
 *
 * The data sources used to generate the mission prefill data include:
 * - User registration data
 * - Company enrichment data
 * - TFAI transcript data
 * - User searches (not yet implemented)
 *
 * The service performs the following key functions:
 * - Composes mission prefill data sources based on user registration and company enrichment data.
 * - Generates mission prefill prompts and guidelines for LLM.
 * - Maps LLM responses to mission prefill data.
 * - Upserts and retrieves mission prefill data for users.
 *
 * The generated mission prefill data includes project and company details, planned start timelines, and other relevant information to assist users in creating comprehensive mission descriptions.
 */
@Injectable()
export class MissionsPrefillService {
  private getCompanyPrompt = (enrichmentData: CompanyEnrichmentData): string => {
    const name = this.companiesService.getCompanyEnrichemntName(enrichmentData);
    const industries = this.companiesService.getCompanyEnrichmentIndustries(enrichmentData);
    const description = this.companiesService.getCompanyEnrichmentDescription(enrichmentData);
    const timezone = this.companiesService.getCompanyEnrichmentTimezone(enrichmentData);

    return `
        - The client has provided the following details for the project and company, please rely on them to generate the output:
        ${name ? `* The company name is ${name}` : ''}
        ${industries && industries.length > 0 ? `* The company industries are ${industries.join(', ')}` : ''}
        ${description ? `* The company description is: ${description}` : ''}
        ${timezone ? `* The company timezone is ${timezone}` : ''}
      `;
  };

  private getClientRegistrationPrompt = (registrationData: UserRegistrationData, roles: RoleCategoryInfo[], solution?: Solution): string => {
    const { requestType, requestHelp, hiringTimeline } = registrationData!;
    const rolesNameList = roles?.map((role) => role.title).join(', ');

    return `
      ${requestType ? `* The client is looking to ${requestTypeLabel[requestType]}` : ''}
      ${requestHelp ? `* The client provided this description when asked what they are trying to build: ${requestHelp}` : ''}
      ${hiringTimeline ? `* The client's timeline to hire is ${hiringTimeline}` : ''}
      ${solution ? `* The client indicated they are looking to build a ${solution.title}` : ''}
      ${roles.length > 0 ? `* The client is looking to hire the following roles: ${rolesNameList}` : ''}
      `;
  };

  private getGeneralPromptGuidelines = (): string => {
    return `
        - You are a helpful assistant helping a company generate project details for a job posting. Follow these rules and rely on the client details to generate the project and company details:
        - For the project itself: the project should provide a technological solution to a problem or opportunity this company has. 
          if no specific solution is requested by the client, try to understand what potential technological solution this company would need and use that context for the project title and description.   
      `;
  };

  private getTitleGuidelines = (): string => {
    return ` 
      - For project title: Avoid redundancy, unnecessary flair, and overly specific phrasing.
      - For project title: Do not add a period at the end of the title.
      - For project title: Do not include the company name in the title.
    `;
  };

  private getPlannedStartGuidelines = (): string => {
    return `
      - For project planned start: Keep the planned start format to one of these options: "Immediately", "Next month", "Within 3 months" or "Exploring".
      - For project planned start: Try and understand the client's urgency and planned start and adjust the planned start option accordingly, within the options provided.
      - For project planned start: For context, today's date is ${new Date().toDateString()}.
      - For project planned start: If no planned start is indicated by the client, fill in the planned start with "exploring".
      `;
  };

  private getProjectDescriptionGuidelines = (): string => {
    return `
     - For the project description, state the project goal: "The goal of this project is to [specific objective, e.g., develop a new platform or solve a particular challenge].", 
        Context: "Currently, [brief description of the problem or opportunity].", 
        Deliverables: "The project involves [key deliverables, e.g., building systems, APIs, dashboards]." 
        and Expertise needed: "We are looking for [specific roles or expertise] to deliver impactful results."
      `;
  };

  private getCompanyDescriptionGuidelines = (): string => {
    return `
        - For the company description: please rewrite the company description provided in a professional and concise way.
        - For the company description: Do not mention anything about joining the company as this will already be shown next to a job description. 
        - For the company description: Use only the context provided and do not elaborate or make up innacurate details. 
        - For the company description: The target audience are potential job seekers who will see the optimized company description on a job posting. 
        - For the company description: Do not use third person or the word "revolutionizing" in the optimized description. 
  `;
  };

  private getRoleListPrompt = (roles: RoleCategoryInfo[]): string => {
    const rolesMapped = roles.map((role, i) => `* Role ${i + 1} is ${role.title} with category id ${role.id}`).join('\n');

    return `The client has provided the following required roles for the project, please rely on this following list to generate the roles:
    - The client has provided the following required roles for the project, please rely on this following list to generate the roles:
    ${rolesMapped}
    - Make sure to keep the category id for each role as it is, and provide a brief description and a screening question for each role.`;
  };

  /**
   * Helper function used for building up the prompt for mission prefill.
   *
   * @param {MissionPrefillInputs} inputs - The inputs used for mission prefill
   * @returns Promise - A promise that resolves to a string containing the composed mission prefill prompt.
   */
  private composeMissionPrefillPrompt = async (inputs: MissionPrefillInputs): Promise<string> => {
    const { registrationData, enrichmentData, requestedRoles, solution } = inputs;

    const companyPrompt = enrichmentData ? this.getCompanyPrompt(enrichmentData) : '';
    const clientRegistrationPrompt = registrationData ? this.getClientRegistrationPrompt(registrationData, requestedRoles, solution) : '';

    return `
    ${this.getGeneralPromptGuidelines()}
    ${this.getTitleGuidelines()}
    ${this.getPlannedStartGuidelines()}
    ${this.getProjectDescriptionGuidelines()}
    ${this.getCompanyDescriptionGuidelines()}
    ${companyPrompt}
    ${clientRegistrationPrompt}
    `;
  };

  /**
   * Helper function used for building up the prompt for mission prefill roles.
   *
   * The prompt includes:
   * - A list of required roles provided by the client, if available.
   * - Instructions for generating role descriptions and screening questions.
   *
   * @param {MissionPrefillInputs} inputs - The data sources containing registration and enrichment data.
   * @returns Promise - A promise that resolves to the composed prompt string.
   */
  private composeMissionPrefillRolesPrompt = async (inputs: MissionPrefillInputs): Promise<string> => {
    const { registrationData, requestedRoles, enrichmentData, solution } = inputs;

    const companyPrompt = enrichmentData ? this.getCompanyPrompt(enrichmentData) : '';
    const clientRegistrationPrompt = registrationData ? this.getClientRegistrationPrompt(registrationData, requestedRoles, solution) : '';
    const roleListPrompt = requestedRoles.length > 0 ? this.getRoleListPrompt(requestedRoles) : '';

    return `
      - You are a helpful assistant helping a company generate role details for a job posting.
       ${clientRegistrationPrompt}
       ${companyPrompt}
       ${roleListPrompt}
      - For each role description: Explain the purpose: "[Summarize the role's purpose and objectives, e.g., responsible for [specific tasks or goals]]."
      - For each role description: Detail the Requirements: "Requires expertise in [key skills or technologies]."
      - For each role description: Explain any soft skills that are likely needed: "Strong [collaboration/leadership/technical] abilities are a must."
      - For each role description: Keep the description under 300 characters.
      - For screening question, you can ask about the candidate's experience, motivation, or any other relevant information regarding that area of expertise. keep it under 200 characters.
      `;
  };

  /**
   * Helper function used for mapping the generated mission prefill role response to {@link MissionPrefillRole}.
   *
   * @param  {PrefillRoleLLMResponse} generatedResponse - The generated response
   * @param  {string[]} requiredSkillIds - The required skill ids
   * @param  {string[]} preferredSkillIds - The preferred skill ids
   * @returns MissionPrefillRole - The mission prefill role updated
   */
  private mapLLMResponseToMissionPrefillRole = (
    generatedResponse: PrefillRoleLLMResponse,
    requiredSkillIds: string[],
    preferredSkillIds: string[]
  ): MissionPrefillRole => {
    return {
      categoryId: generatedResponse.categoryId,
      headline: generatedResponse.headline ?? null,
      requiredSkills: requiredSkillIds,
      preferredSkills: preferredSkillIds,
      timeCommitment: null,
      screeningQuestion: generatedResponse.screeningQuestion ?? null,
    };
  };

  /**
   * Helper function used for mapping the generated mission prefill response to {@link MissionPrefill}.
   *
   * @param  {PrefillLLMResponse} generatedResponse - The generated response
   * @returns Partial<MissionPrefill> - The mission prefill updated
   */
  private mapLLMResponseToMissionPrefill = (generatedResponse: PrefillLLMResponse): Partial<MissionPrefill> => {
    const companyInfo = generatedResponse.company;

    return {
      missionName: generatedResponse.project.title,
      missionDescription: generatedResponse.project.description,
      plannedStart: generatedResponse.project.plannedStart,
      companyDescription: companyInfo?.description,
      timezone: companyInfo?.timezone,
    };
  };

  /**
   * Helper function used for generating the mission prefill based on the inputs provided.
   *
   * @param  {MissionPrefillInputs} inputs - The mission prefill inputs
   */
  private generateMissionPrefillViaLLM = async (inputs: MissionPrefillInputs) => {
    return await this.openAIClient.generateObject({
      prompt: await this.composeMissionPrefillPrompt(inputs),
      schema: prefillLLMSchema,
    });
  };

  /**
   * Helper function used for generating the roles info for mission prefill based on the inputs provided.
   *
   * @param  {MissionPrefillInputs} inputs - The mission prefill inputs
   */
  private generateMissionPreffillRolesViaLLM = async (inputs: MissionPrefillInputs) => {
    return await this.openAIClient.generateArray({
      prompt: await this.composeMissionPrefillRolesPrompt(inputs),
      schema: prefillRoleLLMSchema,
    });
  };

  /**
   * Helper function used for getting the role category by id.
   *
   * @param  {string} id - The id
   * @param  {RoleCategoryInfo[]} roleCategories - The role categories
   * @returns RoleCategoryInfo - The role category info needed
   */
  private getRoleCategoryById = (id: string, roleCategories: RoleCategoryInfo[]): RoleCategoryInfo | undefined => {
    return roleCategories.find((category) => category.id === id);
  };

  /**
   * Helper function used for getting all of the possible mission prefill data sources for a given user.
   *
   * @param {string} userId - The ID of the user for whom to compose the mission prefill data sources.
   */
  private gatherMissionPrefillInputs = async (userId: string) => {
    const [registrationDataResult, roleCategoriesResult] = await Promise.all([
      this.usersService.getUserRegistrationData(userId),
      this.missionsService.getRoleCategories(),
    ]);

    if (registrationDataResult.isErr()) {
      return registrationDataResult;
    }

    if (roleCategoriesResult.isErr()) {
      return roleCategoriesResult;
    }

    const registrationData = registrationDataResult.value;
    const roleCategories = roleCategoriesResult.value;

    let requestedRoleIds = registrationData.requestRoles ?? [];

    const companyId = registrationData.signupCompany;
    const enrichmentResult = companyId ? await this.companiesService.getCompanyEnrichment(companyId) : err();
    const enrichmentData = enrichmentResult.isOk() ? enrichmentResult.value : undefined;

    let solution: Solution | undefined = undefined;
    const requestedSolutionId = registrationData.requestSolution;

    if (typeof requestedSolutionId === 'string') {
      const solutionResult = await this.missionsPrefillRepository.getClientRequestedSolution(requestedSolutionId);
      solution = solutionResult.isOk() ? solutionResult.value : undefined;

      if (requestedRoleIds.length === 0) {
        requestedRoleIds = solution?.roles.map((role) => role.category) ?? [];
      }
    }

    const requestedRoles = requestedRoleIds
      .map((role) => this.getRoleCategoryById(role, roleCategories))
      .filter((roleCategory): roleCategory is RoleCategoryInfo => !!roleCategory);

    return ok({
      registrationData,
      enrichmentData,
      requestedRoles,
      solution,
    });
  };

  /**
   * Generates Prefill roles based on the provided data sources.
   *
   * The function performs the following steps:
   * 1. Fetches the most common skills for each requested role specialization.
   * 2. Generates mission prefill roles using an llm.
   * 3. Maps the generated roles to prefill roles, including required and preferred skills.
   *
   * @param {MissionPrefillInputs} inputs - The data sources required to generate prefill roles.
   */
  private generatePrefillRoles = async (inputs: MissionPrefillInputs) => {
    const rolesSkills: Record<string, MissionPrefillRoleSkills> = {};
    const { requestedRoles } = inputs;

    for (const role of requestedRoles) {
      const skillsResult = await this.missionsPrefillRepository.getMostCommonSkillsForSpecialization(role.id);

      if (skillsResult.isErr()) {
        return skillsResult;
      }

      const skills = skillsResult.value;
      rolesSkills[role.id] = { requiredSkills: skills.requiredSkills, preferredSkills: skills.preferredSkills };
    }

    const generatedRolesLLMDataResult = await this.generateMissionPreffillRolesViaLLM(inputs);

    if (generatedRolesLLMDataResult.isErr()) {
      return generatedRolesLLMDataResult;
    }

    const generatedRoles = generatedRolesLLMDataResult.value;

    return ok(
      generatedRoles.object.map((role) => {
        const roleSkills = rolesSkills[role.categoryId];
        return this.mapLLMResponseToMissionPrefillRole(role, roleSkills.requiredSkills, roleSkills.preferredSkills);
      })
    );
  };

  constructor(
    private readonly missionsPrefillRepository: MissionsPrefillRepository,
    private readonly missionsService: MissionsService,
    private readonly usersService: CommonUsersService,
    private readonly companiesService: CommonCompaniesService,
    private readonly openAIClient: OpenAIClient
  ) {}

  /**
   * Generates a mission prefill for a given user.
   *
   * This function uses the user ID to generate a mission prefill with the following steps:
   * 1. Generates LLM data for the mission prefill and roles.
   * 2. Maps the generated LLM responses to the mission prefill and updates it.
   *
   * @param {string} userId - The ID of the user for whom to generate the mission prefill.
   */
  generateMissionPrefill = async (userId: string) => {
    const inputsResult = await this.gatherMissionPrefillInputs(userId);

    if (inputsResult.isErr()) {
      return inputsResult;
    }

    const inputs = inputsResult.value;

    const [generatedMissionResult, generatedRolesResult] = await Promise.all([this.generateMissionPrefillViaLLM(inputs), this.generatePrefillRoles(inputs)]);

    if (generatedMissionResult.isErr()) {
      return generatedMissionResult;
    }

    if (generatedRolesResult.isErr()) {
      return generatedRolesResult;
    }

    const generatedMission = generatedMissionResult.value;
    const generatedRoles = generatedRolesResult.value;

    const data: Partial<MissionPrefill> = {
      ...this.mapLLMResponseToMissionPrefill(generatedMission.object),
      openRoles: generatedRoles || [],
    };

    return await this.upsertMissionPrefill(userId, data);
  };

  upsertMissionPrefill = async (userId: string, data: Partial<MissionPrefill>) => {
    return await this.missionsPrefillRepository.upsertMissionPrefill(userId, data);
  };

  getMissionPrefillByUserId = async (userId: string) => {
    return await this.missionsPrefillRepository.findMissionPrefillByUserId(userId);
  };
}
