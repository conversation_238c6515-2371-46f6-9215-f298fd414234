import { Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';

import { DbService } from '@lib/global/db.service';

@Injectable()
export class CommonBillingAccountsRepository {
  constructor(private readonly prisma: DbService) {}

  getBillingAccountByAccountId = async (accountId: string) => {
    const billingAccount = await this.prisma.account.findFirst({
      where: { id: accountId },
      include: {
        billingAccountModel: true,
      },
    });

    return billingAccount ? billingAccount.billingAccountModel : null;
  };

  updateBillingAccount = async (id: string, billingAccount: Prisma.BillingAccountUpdateInput) => {
    return this.prisma.billingAccount.update({
      where: { id },
      data: billingAccount,
    });
  };

  createBillingAccount = async (billingAccount: Prisma.BillingAccountCreateInput) => {
    return this.prisma.billingAccount.create({
      data: billingAccount,
    });
  };
}
