import { z } from 'zod';

// Policies
export type PolicyAction = 'read' | 'write';

// Resources
export type ResourceId = string;
export type ResourceType = 'workspace' | 'paymentsource' | 'billinginfo' | 'mission' | 'contract' | 'paymentcycle' | 'builderrates' | 'invoice';
export type Resource = ResourceType | `${ResourceType}:${ResourceId}`;

// Roles & their options
const baseGrantOptionsSchema = z.object({
  /**
   * Use only when the resource already exists and not when creating a new resource.
   * This check will help enforce a user with lesser privilege granting a higher privilege role to another user or the same user.
   */
  checkPrivilegeLevel: z.boolean().optional(),
});

const missionGrantOptionsSchema = baseGrantOptionsSchema.extend({
  missionId: z.string(),
});

export const grantRoleOptionsSchema = z.discriminatedUnion('role', [
  // Workspace roles
  baseGrantOptionsSchema.extend({
    role: z.literal('workspaceadmin'),
  }),
  baseGrantOptionsSchema.extend({
    role: z.literal('workspacemember'),
  }),
  // Mission roles
  missionGrantOptionsSchema.extend({
    role: z.literal('missionadmin'),
  }),
  missionGrantOptionsSchema.extend({
    role: z.literal('missionmember'),
  }),
]);

export type GrantRoleOptions = z.infer<typeof grantRoleOptionsSchema>;
export type Role = GrantRoleOptions['role'];
