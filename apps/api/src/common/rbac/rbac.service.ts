import { Injectable } from '@nestjs/common';
import axios, { Axios } from 'axios';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

import { GrantRoleOptions, PolicyAction, Resource } from './types';

type CheckClientPermissionErrorCodes = 'CHECK_CLIENT_PERMISSION_UNAUTHORIZED' | 'CHECK_CLIENT_PERMISSION_API_ERROR';

type GrantRoleErrorCodes = 'GRANT_ROLE_PERMISSION_UNAUTHORIZED' | 'GRANT_ROLE_API_ERROR';

@Injectable()
export class CommonRBACService {
  private client: Axios;

  constructor(configService: ConfigService) {
    this.client = axios.create({
      baseURL: configService.get('CLIENT_APP_SERVER_V1_URL'),
      headers: {
        'x-client-server-api-key': configService.get('CLIENT_APP_SERVER_V1_API_KEY'),
      },
    });
  }

  /**
   * A method that checks if a user has access to do an action on a certain resource.
   */
  checkClientPermission = async (userId: string, accountId: string, resource: Resource, action: PolicyAction) => {
    const result = await ResultAsync.fromPromise(
      this.client.get<{ isAuthorized: boolean }>('/rbac/v1/check', {
        params: {
          permission: `${userId},${accountId},${resource},${action}`,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<CheckClientPermissionErrorCodes>('CHECK_CLIENT_PERMISSION_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const { data } = result.value;

    if (!data.isAuthorized) {
      return Errors.createError<CheckClientPermissionErrorCodes>('CHECK_CLIENT_PERMISSION_UNAUTHORIZED');
    }

    return ok();
  };

  /**
   * Generic method used for granting a role to a user.
   */
  grantRole = async (userId: string, accountId: string, options: GrantRoleOptions) => {
    const { role, ...restOptions } = options;

    const payload = {
      userId,
      accountId,
      role,
      options: restOptions,
    };

    const result = await ResultAsync.fromPromise(this.client.post<{ success: boolean }>('/rbac/v1/grant-role', payload), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<GrantRoleErrorCodes>('GRANT_ROLE_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok();
  };
}
