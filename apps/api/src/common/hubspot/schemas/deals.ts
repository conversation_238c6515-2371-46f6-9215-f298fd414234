export type UpdateDealProperties = {
  has_billing_form: boolean | null;
  role_description: string | null;
  role_builder_name: string | null;
  client_rate: string | null;
  role_schedule: string | null;
  role_start_date: string;
  contract_company_name: string | null;
  msa_signing_date: string | null;
  billing_user_name: string | null;
  billing_email: string | null;
  billing_entity_name: string | null;
  billing_federal_id: string | null;
  billing_payment_terms: string | null;
  billing_payment_terms_days: string | null;
  billing_period: string | null;
  po_number: string | null;
  is_monthly_role: boolean | null;
};

export type HubspotDeal = {
  id: string;
  createdAt: Date;
  name: string | null;
  roleId: string | null;
  missionId: string | null;
  associations: string | null;
  class: string | null;
  contactSource: string | null;
  industry: string | null;
  industryCompany: string | null;
  leadOwner: string | null;
  leadType: string | null;
  medium: string | null;
  productOfferings: string | null;
  referralDetails: string | null;
  referralSource: string | null;
  typeOfTeam: string | null;
};
