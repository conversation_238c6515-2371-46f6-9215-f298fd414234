import { Mission, MissionRoleStatus, RoleCategory } from '@a_team/prisma';
import * as hubspot from '@hubspot/api-client';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';
import { RateCalculator } from '@modules/missions/helpers/rate';

import { Errors } from '@lib/errors';

import { HubspotCompany, HubspotDeal, HubspotObject, HubspotOwner, HubspotProperties, UpdateDealProperties } from './schemas';

type CreateMissionDealPayload = {
  mission: Mission;
  roleId: string;
  hubspotOwner: HubspotOwner | null;
  roleCategories: Pick<RoleCategory, 'id' | 'title'>[];
  leadDealProperties: HubspotProperties;
};

type GetOwnerByEmailErrorCodes = 'GET_OWNER_BY_EMAIL_API_ERROR' | 'GET_OWNER_BY_EMAIL_NOT_FOUND';

type GetCompanyByIdErrorCodes = 'GET_COMPANY_BY_ID_API_ERROR';

type GetDealByIdErrorCodes = 'GET_DEAL_BY_ID_API_ERROR';

type CreateDealErrorCodes = 'CREATE_DEAL_API_ERROR';

type UpdateDealByIdErrorCodes = 'UPDATE_DEAL_BY_ID_API_ERROR';

@Injectable()
export class CommonHubspotService {
  private readonly platformWebUrl: string;
  private readonly opportunityPipelineId: string;
  private readonly missionSentStageId: string;

  private apiClient: hubspot.Client;

  private createHubspotObject = (properties: HubspotProperties): HubspotObject => {
    return {
      properties: Object.fromEntries(
        Object.entries(properties)
          .filter(([, value]) => value !== null && value !== undefined)
          .map(([key, value]) => [key, String(value)])
      ),
    };
  };

  constructor(configService: ConfigService) {
    const accessToken = configService.get('HUBSPOT_ACCESS_TOKEN');

    this.apiClient = new hubspot.Client({ accessToken });

    this.platformWebUrl = configService.get('PLATFORM_WEB_URL');
    this.opportunityPipelineId = configService.get('HUBSPOT_OPPORTUNITY_PIPELINE_ID');
    this.missionSentStageId = configService.get('HUBSPOT_MISSION_SENT_STAGE_ID');
  }

  createDeal = async (properties: HubspotProperties) => {
    const result = await ResultAsync.fromPromise(this.apiClient.crm.deals.basicApi.create(this.createHubspotObject(properties)), (e) => new Error(e as string));

    if (result.isErr()) {
      return Errors.createError<CreateDealErrorCodes>('CREATE_DEAL_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value.id);
  };

  updateDealById = async (id: string, payload: UpdateDealProperties) => {
    const result = await ResultAsync.fromPromise(
      this.apiClient.crm.deals.basicApi.update(id, this.createHubspotObject(payload)),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<UpdateDealByIdErrorCodes>('UPDATE_DEAL_BY_ID_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok();
  };

  getDealById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.apiClient.crm.deals.basicApi.getById(id, [
        'associations',
        'class',
        'contact_source',
        'dealname',
        'industry__company_',
        'industry',
        'lead_owner',
        'lead_type',
        'medium',
        'mission_id',
        'product_offerings',
        'referral_details',
        'referral_source',
        'role_id',
        'type_of_team',
      ]),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<GetDealByIdErrorCodes>('GET_DEAL_BY_ID_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const deal = result.value;
    const { properties } = deal;

    return ok<HubspotDeal>({
      id: deal.id,
      createdAt: deal.createdAt,
      name: properties.dealname,
      roleId: properties.role_id,
      missionId: properties.mission_id,
      associations: properties.associations,
      class: properties.class,
      contactSource: properties.contact_source,
      industryCompany: properties.industry__company_,
      industry: properties.industry,
      leadOwner: properties.lead_owner,
      leadType: properties.lead_type,
      medium: properties.medium,
      productOfferings: properties.product_offerings,
      referralDetails: properties.referral_details,
      referralSource: properties.referral_source,
      typeOfTeam: properties.type,
    });
  };

  getCompanyById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.apiClient.crm.companies.basicApi.getById(id, ['company_id', 'website', 'platform_id']),
      (e) => new Error(e as string)
    );

    if (result.isErr()) {
      return Errors.createError<GetCompanyByIdErrorCodes>('GET_COMPANY_BY_ID_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const { properties } = result.value;

    return ok<HubspotCompany>({
      companyId: properties.company_id!,
      website: properties.website,
      platformId: properties.platform_id,
    });
  };

  getOwnerByEmail = async (email: string) => {
    const result = await ResultAsync.fromPromise(this.apiClient.crm.owners.ownersApi.getPage(email), (e) => new Error(e as string));

    if (result.isErr()) {
      return Errors.createError<GetOwnerByEmailErrorCodes>('GET_OWNER_BY_EMAIL_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const ownersInfo = result.value.results;

    if (ownersInfo.length === 0) {
      return Errors.createError<GetOwnerByEmailErrorCodes>('GET_OWNER_BY_EMAIL_NOT_FOUND', { isUnexpectedError: true });
    }

    return ok<HubspotOwner>(ownersInfo[0]);
  };

  getLeadDealProperties = async (dealId: string) => {
    const dealResult = await this.getDealById(dealId);

    if (dealResult.isErr()) {
      return dealResult;
    }

    const deal = dealResult.value;

    return ok<HubspotProperties>({
      associations: deal.associations,
      contact_source: deal.contactSource,
      medium: deal.medium,
      referral_source: deal.referralSource,
      referral_details: deal.referralDetails,
      lead_owner: deal.leadOwner,
      industry__company_: deal.industryCompany,
      class: deal.class,
      industry: deal.industry,
      lead_type: deal.leadType,
      product_offerings: deal.productOfferings,
      type_of_team: deal.typeOfTeam,
    });
  };

  createMissionRoleDeal = async (payload: CreateMissionDealPayload) => {
    const { mission, roleId, hubspotOwner, roleCategories, leadDealProperties } = payload;

    const role = mission.roles.find((role) => role.id === roleId);
    const roleCategory = roleCategories.find((category) => category.id === role?.categoryId);
    const wonStatuses: MissionRoleStatus[] = [MissionRoleStatus.Active, MissionRoleStatus.Ended, MissionRoleStatus.ScheduledToEnd];
    const isOtherRolesInClosedWonState = mission.roles.filter((role) => role.id !== roleId).some((role) => wonStatuses.includes(role.status));

    const hourlyRate =
      role && role.builderRateMax && role.markup
        ? Math.round(RateCalculator.getClientRateFromBuilderRateAndMarkup(role.builderRateMax, role.markup))
        : undefined;

    const createDealPayload: HubspotProperties = {
      dealname: `${mission.title.slice(0, 40)} | ${roleCategory?.title}`,
      pipeline: this.opportunityPipelineId,
      dealstage: this.missionSentStageId,
      amount: 1,
      dealtype: 'existingbusiness',
      opportunity_type: isOtherRolesInClosedWonState ? 'Expansion Role' : 'New Role',
      referred_by: 'Client app (self serve)', // TODO: update?
      hubspot_owner_id: hubspotOwner?.id,
      tfs: `${hubspotOwner?.firstName} ${hubspotOwner?.lastName}`,
      mission_id: mission.mid,
      role_id: roleId,
      role_title: roleCategory?.title,
      lead_deal_id: mission.hubspotDealId,
      platform_url: `${this.platformWebUrl}/mission/${mission.mid}`,
      expected_hours__monthly_: role?.availability?.weeklyHoursAvailable,
      rate__hourly_: hourlyRate,
      ...leadDealProperties,
    };

    return await this.createDeal(createDealPayload);
  };
}
