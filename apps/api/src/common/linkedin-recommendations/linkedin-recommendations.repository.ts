import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type GetLinkedinRecommendationsCountErrorCodes = 'GET_LINKEDIN_RECOMMENDATIONS_COUNT_DB_ERROR';
type GetLinkedinRecommendationsByUserIdErrorCodes = 'GET_LINKEDIN_RECOMMENDATIONS_BY_USER_ID_DB_ERROR';

@Injectable()
export class CommonLinkedinRecommendationsRepository {
  constructor(private readonly prisma: DbService) {}

  getLinkedinRecommendationsCount = async (userId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.linkedInRecommendation.count({
        where: { userId },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GetLinkedinRecommendationsCountErrorCodes>('GET_LINKEDIN_RECOMMENDATIONS_COUNT_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  getLinkedinRecommendationsByUserId = async (userId: string) => {
    const dataResult = await ResultAsync.fromPromise(
      this.prisma.linkedInRecommendation.findMany({
        where: {
          userId,
        },
        orderBy: { createdAt: 'desc' },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (dataResult.isErr()) {
      return Errors.createError<GetLinkedinRecommendationsByUserIdErrorCodes>('GET_LINKEDIN_RECOMMENDATIONS_BY_USER_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: dataResult.error,
      });
    }

    return ok(dataResult.value);
  };
}
