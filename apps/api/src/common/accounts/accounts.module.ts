import { Module } from '@nestjs/common';

import { CommonAccountsRepository } from './accounts.repository';
import { CommonAccountsService } from './accounts.service';
import { CommonUsersModule } from '@common/users/users.module';

@Module({
  imports: [CommonUsersModule],
  providers: [CommonAccountsService, CommonAccountsRepository],
  exports: [CommonAccountsService, CommonAccountsRepository],
})
export class CommonAccountsModule {}
