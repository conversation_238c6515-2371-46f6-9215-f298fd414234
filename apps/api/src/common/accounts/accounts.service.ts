import { Injectable, Logger } from '@nestjs/common';
import { ok } from 'neverthrow';

import { CommonAccountsRepository } from './accounts.repository';
import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';
import { SentryService } from '@lib/global/sentry.service';

export type AccountTeamAdvisor = {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  pictureURL: string | null;
  accessLevel: 'bdAdmin' | 'bdManager' | 'clientAdmin';
  username: string;
};

@Injectable()
export class CommonAccountsService {
  private defaultTeamAdvisorId: string;

  private getDefaultAdvisor = async () => {
    const userResult = await this.commonUsersRepository.findActiveUserById(this.defaultTeamAdvisorId);

    if (userResult.isErr()) {
      return userResult;
    }

    const user = userResult.value;

    const defaultAdvisor: AccountTeamAdvisor = {
      id: user.id,
      email: user.email,
      accessLevel: 'bdAdmin',
      username: user.username!,
      firstName: user.firstName!,
      lastName: user.lastName ?? '',
      pictureURL: user.pictureURL,
    };

    return ok(defaultAdvisor);
  };

  constructor(
    configService: ConfigService,
    protected readonly sentryService: SentryService,
    protected readonly commonUsersRepository: CommonUsersRepository,
    protected readonly commonAccountsRepository: CommonAccountsRepository
  ) {
    this.defaultTeamAdvisorId = configService.get('DEFAULT_TEAM_ADVISOR_ID');
  }

  getAccountById = async (id: string) => {
    const accountResult = await this.commonAccountsRepository.findAccountById(id);

    if (accountResult.isErr()) {
      return accountResult;
    }

    return ok(accountResult.value);
  };

  getAccountTeamAdvisor = async (id: string) => {
    const accountResult = await this.getAccountById(id);

    if (accountResult.isErr()) {
      Logger.warn(`[common/accounts/accounts.service/getAccountTeamAdvisor] - Account not found: ${id}`);
      return this.getDefaultAdvisor();
    }

    const account = accountResult.value;

    const admins = [
      ...account.members.filter((member) => member.accessLevel === 'bdAdmin'),
      ...account.members.filter((member) => member.accessLevel === 'clientAdmin'),
    ];

    if (admins.length === 0) {
      Logger.warn(`[common/accounts/accounts.service/getAccountTeamAdvisor] - No account admins for account ${id}`);
      return this.getDefaultAdvisor();
    }

    const admin = admins[0];
    const adminUserId = admin.user;

    const userResult = await this.commonUsersRepository.findActiveUserById(adminUserId);

    if (userResult.isErr()) {
      Logger.warn(`[common/accounts/accounts.service/getAccountTeamAdvisor] - Admin admin user id not found: ${adminUserId}`);
      return this.getDefaultAdvisor();
    }

    const user = userResult.value;

    return ok({
      id: user.id,
      email: user.email,
      accessLevel: admin.accessLevel as 'bdAdmin' | 'clientAdmin',
      username: user.username!,
      firstName: user.firstName!,
      lastName: user.lastName ?? '',
      pictureURL: user.pictureURL,
    });
  };
}
