import { Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindAccountByIdErrorCodes = 'FIND_ACCOUNT_BY_ID_DB_ERROR' | 'FIND_ACCOUNT_BY_ID_NOT_FOUND';

type UpdateAccountByIdErrorCodes = 'UPDATE_ACCOUNT_BY_ID_DB_ERROR';

@Injectable()
export class CommonAccountsRepository {
  constructor(private readonly prisma: DbService) {}

  findAccountById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.account.findUnique({
        where: {
          id,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindAccountByIdErrorCodes>('FIND_ACCOUNT_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const account = result.value;

    if (!account) {
      return Errors.createError<FindAccountByIdErrorCodes>('FIND_ACCOUNT_BY_ID_NOT_FOUND');
    }

    return ok(account);
  };

  updateAccountById = async (id: string, data: Prisma.AccountUpdateInput) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.account.update({
        where: { id },
        data,
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateAccountByIdErrorCodes>('UPDATE_ACCOUNT_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok();
  };
}
