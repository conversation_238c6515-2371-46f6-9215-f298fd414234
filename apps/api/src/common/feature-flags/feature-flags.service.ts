import { Injectable } from '@nestjs/common';
import axios, { Axios } from 'axios';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

type FeatureFlag = 'showProposalRatesInClientApp';

type IsFlagOpenForUserAndAccountErrorCodes = 'IS_FLAG_OPEN_FOR_USER_AND_ACCOUNT_API_ERROR';

// TODO: Remove after feature flag service is built in new app
@Injectable()
export class CommonFeatureFlagsService {
  private client: Axios;

  constructor(configService: ConfigService) {
    const clientAppV1Url = configService.get('CLIENT_APP_SERVER_V1_URL');
    const clientAppV1ApiKey = configService.get('CLIENT_APP_SERVER_V1_API_KEY');

    this.client = axios.create({
      baseURL: clientAppV1Url,
      headers: {
        'x-client-server-api-key': clientAppV1ApiKey,
      },
    });
  }

  async isFlagOpenForUserAndAccount(flagName: FeatureFlag, userId?: string, accountId?: string) {
    const result = await ResultAsync.fromPromise(
      this.client.get<{ data: { isOpen: boolean } }>('/flags/proxy', {
        params: {
          flagName,
          userId,
          accountId,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<IsFlagOpenForUserAndAccountErrorCodes>('IS_FLAG_OPEN_FOR_USER_AND_ACCOUNT_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const { data } = result.value;

    return ok(data.data.isOpen);
  }

  async isFlagOpenForUser(flag: FeatureFlag, userId: string) {
    return this.isFlagOpenForUserAndAccount(flag, userId);
  }

  async isFlagOpenForAccount(flag: FeatureFlag, accountId: string) {
    return this.isFlagOpenForUserAndAccount(flag, undefined, accountId);
  }
}
