import { Injectable } from '@nestjs/common';
import { ChatPostMessageArguments, WebClient } from '@slack/web-api';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

export type SlackChannels = 'teamformations' | 'client-team-requests';

export type SlackMessageProps = ChatPostMessageArguments & { channel: SlackChannels };

type FindUserByEmailErrorCodes = 'FIND_USER_BY_EMAIL_NO_USER_FOUND' | 'FIND_USER_BY_EMAIL_API_ERROR';

@Injectable()
export class CommonSlackService {
  private client: WebClient;
  private readonly slackChannelOverride?: string;

  constructor(configService: ConfigService) {
    const slackToken = configService.get('SLACK_TOKEN');
    this.slackChannelOverride = configService.get('SLACK_CHANNEL_OVERRIDE');

    this.client = new WebClient(slackToken);
  }

  sendMessage = async (message: SlackMessageProps) => {
    return this.client.chat.postMessage({
      ...message,
      channel: this.slackChannelOverride ?? message.channel,
    });
  };

  findUserByEmail = async (email: string) => {
    const response = await ResultAsync.fromPromise(this.client.users.lookupByEmail({ email }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (response.isErr()) {
      return Errors.createError<FindUserByEmailErrorCodes>('FIND_USER_BY_EMAIL_API_ERROR', { isUnexpectedError: true, originalError: response.error });
    }

    const user = response.value.user;

    if (!user?.id) {
      return Errors.createError<FindUserByEmailErrorCodes>('FIND_USER_BY_EMAIL_NO_USER_FOUND', { isUnexpectedError: true });
    }

    return ok({
      id: user.id,
    });
  };

  /**
   * Helper function to get the Slack tag for a user by email.
   * Slack does not support email aliases, so we need to handle them.
   *
   * @param email - The email of the user to get the Slack tag for.
   * @returns The Slack tag for the user.
   */
  getUserSlackTagByEmail = async (email: string) => {
    if (email.includes('+')) {
      const splittedEmail = email.split(/\+|@/);
      email = `${splittedEmail[0]}@${splittedEmail[2]}`;
    }

    const userResult = await this.findUserByEmail(email);

    if (userResult.isErr()) {
      return userResult;
    }

    const user = userResult.value;

    return ok(`<@${user.id}>`);
  };
}
