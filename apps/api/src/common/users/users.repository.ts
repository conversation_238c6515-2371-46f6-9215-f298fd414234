import { Prisma, User, UserStatus } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type BasicUserDetails = Pick<User, 'id' | 'firstName' | 'lastName' | 'pictureURL'>;

type FindUserExperiencesErrorCodes = 'FIND_USER_EXPERIENCES_DB_ERROR';

type FindAdminUsersErrorCodes = 'FIND_ADMIN_USERS_DB_ERROR';

type FindUserByIdErrorCodes = 'FIND_USER_BY_ID_DB_ERROR' | 'FIND_USER_BY_ID_NOT_FOUND';

type FindUsersByEmailsCodes = 'FIND_USERS_BY_EMAILS_DB_ERROR';

type FindActiveUserByIdErrorCodes = 'FIND_ACTIVE_USER_BY_ID_DB_ERROR' | 'FIND_ACTIVE_USER_BY_ID_NOT_FOUND';

type UpdateUserByIdErrorCodes = 'UPDATE_USER_BY_ID_DB_ERROR';

type FindBasicUsersDetailsByQueryNameErrorCodes = 'FIND_BASIC_USERS_DETAILS_BY_QUERY_NAME_DB_ERROR';

@Injectable()
export class CommonUsersRepository {
  constructor(private readonly prisma: DbService) {}

  findUserByEmail = async (email: string) => {
    return await this.prisma.user.findFirst({ where: { email } });
  };

  findUserById = async (id: string) => {
    return await this.prisma.user.findFirst({ where: { id } });
  };

  // TODO: Rename to findUserById once the migration is done
  findUserByIdNeverthrow = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.user.findFirst({ where: { id } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindUserByIdErrorCodes>('FIND_USER_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindUserByIdErrorCodes>('FIND_USER_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  findUsersByIds = async (ids: string[]) => {
    return await this.prisma.user.findMany({ where: { id: { in: ids } } });
  };

  findUsersByEmails = async (emails: string[]) => {
    const result = await ResultAsync.fromPromise(this.prisma.user.findMany({ where: { email: { in: emails } } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindUsersByEmailsCodes>('FIND_USERS_BY_EMAILS_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  findUserTokenVersion = async (id: string): Promise<number | undefined> => {
    const tokenInfo = await this.prisma.user.findFirst({ where: { id }, select: { tokenVersion: true } });

    if (!tokenInfo) {
      return undefined;
    }

    return tokenInfo.tokenVersion;
  };

  findBasicUserDetailsByIds = async (ids: string[]): Promise<BasicUserDetails[]> => {
    return await this.prisma.user.findMany({
      where: {
        id: { in: ids },
      },
      select: {
        id: true,
        firstName: true,
        lastName: true,
        pictureURL: true,
      },
    });
  };

  findBasicUsersDetailsByQueryName = async (query: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.user.aggregateRaw({
        pipeline: [
          {
            $match: {
              $and: [
                {
                  type: 'user',
                  status: 'Active',
                  scrubbed: {
                    $in: ['Verified', 'Exceptional'],
                  },
                  firstName: { $exists: true },
                  lastName: { $exists: true },
                  $or: [{ isAdmin: { $exists: false } }, { isAdmin: false }],
                },
                {
                  $text: {
                    $search: query,
                    $caseSensitive: false,
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              score: { $meta: 'textScore' },
            },
          },
          {
            $sort: {
              score: { $meta: 'textScore' },
            },
          },
          /** projected fields must match BasicUserDetails */
          {
            $project: {
              id: { $toString: '$_id' },
              firstName: 1,
              lastName: 1,
              pictureURL: 1,
            },
          },
          {
            $limit: 10,
          },
        ],
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindBasicUsersDetailsByQueryNameErrorCodes>('FIND_BASIC_USERS_DETAILS_BY_QUERY_NAME_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const results = result.value as unknown as BasicUserDetails[];

    return ok<BasicUserDetails[]>(results);
  };

  /**
   * Repository function used for finding the active user by id.
   * This also represents a check if the user is eligible to sign in or not.
   *
   * An eligible user has the following:
   * - status: Active indicates the user completed registration and is also not deleted,
   * - scrubbed: Verified indicates the user has passed verification,
   * - type: companyUser indicates it's a client and not a builder,
   * - isAdmin: true, allows TAs/admins to sign in regardless of the type.
   *
   * If no user is found, `null` is returned.
   *
   * @param  {string} id - The user id
   */
  findActiveUserById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.user.findFirst({
        where: {
          id,
          status: UserStatus.Active,
          scrubbed: { in: ['Verified', 'Exceptional'] },
          OR: [
            { type: 'companyUser', isAdmin: false },
            { type: 'companyUser', isAdmin: undefined },
            { type: 'user', isAdmin: true },
          ],
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindActiveUserByIdErrorCodes>('FIND_ACTIVE_USER_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const user = result.value;

    if (!user) {
      return Errors.createError<FindActiveUserByIdErrorCodes>('FIND_ACTIVE_USER_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  findUserExperiences = async (userId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.experience.findMany({
        where: {
          members: {
            some: { user: userId },
          },
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindUserExperiencesErrorCodes>('FIND_USER_EXPERIENCES_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  findAdminUsers = async () => {
    const result = await ResultAsync.fromPromise(
      this.prisma.user.findMany({
        where: {
          type: 'user',
          isAdmin: true,
          status: { not: UserStatus.Deleted },
        },
        orderBy: [{ firstName: 'asc' }, { lastName: 'asc' }],
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindAdminUsersErrorCodes>('FIND_ADMIN_USERS_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  updateUserById = async (id: string, data: Prisma.UserUpdateInput) => {
    const result = await ResultAsync.fromPromise(this.prisma.user.update({ where: { id }, data }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateUserByIdErrorCodes>('UPDATE_USER_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
