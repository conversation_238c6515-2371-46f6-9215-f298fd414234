import { Module } from '@nestjs/common';

import { CommonClientReviewsRepository } from './client-reviews.repository';
import { CommonAccountsModule } from '@common/accounts/accounts.module';
import { CommonCompaniesModule } from '@common/companies/companies.module';
import { CommonTalentIndustriesModule } from '@common/talent-industries/talent-industries.module';
import { CommonUsersModule } from '@common/users/users.module';
import { GuardsModule } from '@lib/guards/guards.module';
import { MissionsModule } from '@modules/missions/missions.module';

@Module({
  imports: [GuardsModule, CommonUsersModule, CommonAccountsModule, MissionsModule, CommonCompaniesModule, CommonTalentIndustriesModule],
  controllers: [],
  providers: [CommonClientReviewsRepository],
  exports: [CommonClientReviewsRepository],
})
export class CommonClientReviewsModule {}
