import { Prisma, UserReviewStatus } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type GetClientReviewsCountErrorCodes = 'GET_CLIENT_REVIEWS_COUNT_DB_ERROR';
type GetClientReviewsByUserIdErrorCodes = 'GET_CLIENT_REVIEWS_BY_USER_ID_DB_ERROR';

@Injectable()
export class CommonClientReviewsRepository {
  constructor(private readonly prisma: DbService) {}

  getClientReviewsCount = async (userId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.userReview.count({
        where: { toUser: userId, status: UserReviewStatus.completed },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GetClientReviewsCountErrorCodes>('GET_CLIENT_REVIEWS_COUNT_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  getClientReviewsByUserId = async (userId: string) => {
    const where: Prisma.UserReviewWhereInput = {
      toUser: userId,
      status: UserReviewStatus.completed,
    };

    const result = await ResultAsync.fromPromise(
      this.prisma.userReview.findMany({
        where,
        orderBy: { createdAt: 'desc' },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GetClientReviewsByUserIdErrorCodes>('GET_CLIENT_REVIEWS_BY_USER_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
