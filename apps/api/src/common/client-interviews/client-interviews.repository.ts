import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindClientInterviewsForProposalAndBuilderErrorCodes = 'FIND_CLIENT_INTERVIEWS_FOR_PROPOSAL_AND_BUILDER_DB_ERROR';

@Injectable()
export class CommonClientInterviewsRepository {
  constructor(private readonly prisma: DbService) {}

  findClientInterviewsForProposalAndBuilder = async (proposalId: string, builderId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.clientInterview.findMany({
        where: { proposalId, builderId },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindClientInterviewsForProposalAndBuilderErrorCodes>('FIND_CLIENT_INTERVIEWS_FOR_PROPOSAL_AND_BUILDER_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
