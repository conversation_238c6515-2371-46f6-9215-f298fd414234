import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindMissionApplicationsForRoleWithUserErrorCodes =
  | 'FIND_MISSION_APPLICATIONS_FOR_ROLE_WITH_USER_DB_ERROR'
  | 'FIND_MISSION_APPLICATIONS_FOR_ROLE_WITH_USER_USER_NOT_FOUND';

type IsMissionApplicationNotifiedForInterviewErrorCodes = 'IS_MISSION_APPLICATION_NOTIFIED_FOR_INTERVIEW_DB_ERROR';

@Injectable()
export class CommonMissionApplicationsRepository {
  constructor(private readonly prisma: DbService) {}

  findMissionApplicationsForRoleWithUser = async (roleId: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.missionApplication.findMany({ where: { rid: roleId }, include: { userModel: true } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionApplicationsForRoleWithUserErrorCodes>('FIND_MISSION_APPLICATIONS_FOR_ROLE_WITH_USER_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const applications = result.value;

    if (applications.some((a) => !a.userModel)) {
      return Errors.createError<FindMissionApplicationsForRoleWithUserErrorCodes>('FIND_MISSION_APPLICATIONS_FOR_ROLE_WITH_USER_USER_NOT_FOUND', {
        isUnexpectedError: true,
      });
    }

    return ok(applications);
  };

  isMissionApplicationNotifiedForInterview = async (applicationId: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.proposal.findFirst({
        where: {
          candidates: {
            some: {
              aid: applicationId,
              clientReview: {
                is: {
                  status: 'NotifiedForInterview',
                },
              },
            },
          },
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<IsMissionApplicationNotifiedForInterviewErrorCodes>('IS_MISSION_APPLICATION_NOTIFIED_FOR_INTERVIEW_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(!!result.value);
  };
}
