import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindRoleCategoryByIdErrorCodes = 'FIND_ROLE_CATEGORY_BY_ID_DB_ERROR' | 'FIND_ROLE_CATEGORY_BY_ID_NOT_FOUND';

@Injectable()
export class CommonRoleCategoriesRepository {
  constructor(private readonly prisma: DbService) {}

  findRoleCategoryById = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.roleCategory.findFirst({ where: { id } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindRoleCategoryByIdErrorCodes>('FIND_ROLE_CATEGORY_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindRoleCategoryByIdErrorCodes>('FIND_ROLE_CATEGORY_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };
}
