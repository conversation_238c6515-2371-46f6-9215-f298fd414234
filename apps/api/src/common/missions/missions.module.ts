import { <PERSON>du<PERSON> } from '@nestjs/common';

import { CommonUsersModule } from '../users/users.module';
import { CommonMissionApplicationsRepository } from './mission-applications.repository';
import { CommonMissionApplicationsService } from './mission-applications.service';
import { CommonMissionsRepository } from './missions.repository';
import { CommonRoleCategoriesRepository } from './role-categories.repository';
import { CommonTalentSkillsRepository } from './talent-skills.repository';

@Module({
  imports: [CommonUsersModule],
  providers: [
    CommonMissionsRepository,
    CommonMissionApplicationsRepository,
    CommonTalentSkillsRepository,
    CommonRoleCategoriesRepository,
    CommonMissionApplicationsService,
  ],
  exports: [
    CommonMissionsRepository,
    CommonMissionApplicationsRepository,
    CommonTalentSkillsRepository,
    CommonRoleCategoriesRepository,
    CommonMissionApplicationsService,
  ],
})
export class CommonMissionsModule {}
