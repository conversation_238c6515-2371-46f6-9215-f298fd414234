import {
  MissionApplication,
  MissionApplicationReviewStatusWaitlisted,
  MissionApplicationReviewStatusNotSelected,
  MissionApplicationReviewStatusOther,
} from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { MissionApplicationStage, UserBadge } from '@packages/contracts';
import { ok } from 'neverthrow';

import { CommonMissionApplicationsRepository } from './mission-applications.repository';
import { CommonMissionsRepository } from './missions.repository';
import { CommonUsersRepository } from '@common/users/users.repository';

@Injectable()
export class CommonMissionApplicationsService {
  constructor(
    private readonly commonMissionsRepository: CommonMissionsRepository,
    private readonly commonMissionApplicationsRepository: CommonMissionApplicationsRepository,
    private readonly commonUsersRepository: CommonUsersRepository
  ) {}

  getApplicationStatusStage = async (application: MissionApplication, badges: UserBadge[]) => {
    const roleResult = await this.commonMissionsRepository.findRoleById(application.rid);

    if (roleResult.isErr()) {
      return roleResult;
    }

    const role = roleResult.value;

    if (application.uid === role.user) {
      return ok<MissionApplicationStage>('Accepted');
    }

    const reviewStatus = application.reviewStatus;

    if (reviewStatus?.other?.includes(MissionApplicationReviewStatusOther.Unavailable)) {
      return ok<MissionApplicationStage>('NotAvailable');
    }

    const userResult = await this.commonUsersRepository.findUserByIdNeverthrow(application.uid);

    if (userResult.isErr()) {
      return userResult;
    }

    const user = userResult.value;

    if (user.exclusiveApplication) {
      if (user.exclusiveApplication.aid === application.aid) {
        return ok<MissionApplicationStage>('Exclusive');
      } else {
        return ok<MissionApplicationStage>('OnHold');
      }
    }

    if (reviewStatus) {
      if (reviewStatus.notSelected.length > 0) {
        if (
          reviewStatus.notSelected.includes(MissionApplicationReviewStatusNotSelected.AfterInterview) ||
          reviewStatus.notSelected.includes(MissionApplicationReviewStatusNotSelected.AfterProposal)
        ) {
          return ok<MissionApplicationStage>('Rejected');
        } else {
          return ok<MissionApplicationStage>('LowCompetitiveness');
        }
      }

      if (reviewStatus && reviewStatus.opportunityToUpdate.length > 0) {
        return ok<MissionApplicationStage>('OpportunityToUpdate');
      }

      const isNotifiedForInterviewResult = await this.commonMissionApplicationsRepository.isMissionApplicationNotifiedForInterview(application.aid);

      if (isNotifiedForInterviewResult.isErr()) {
        return isNotifiedForInterviewResult;
      }

      const isNotifiedForInterview = isNotifiedForInterviewResult.value;

      if (isNotifiedForInterview || reviewStatus.other.includes(MissionApplicationReviewStatusOther.InterviewingFromFormation)) {
        return ok<MissionApplicationStage>('Interviewing');
      }

      if (
        application.proposal ||
        (reviewStatus.other.includes(MissionApplicationReviewStatusOther.PresentedToClient) && !application.proposalManuallySharedAt)
      ) {
        return ok<MissionApplicationStage>('ProposedButNotShared');
      }

      if (reviewStatus.other.includes(MissionApplicationReviewStatusOther.PresentedToClient)) {
        return ok<MissionApplicationStage>('Proposed');
      }

      if (reviewStatus.waitlisted.includes(MissionApplicationReviewStatusWaitlisted.WaitlistedGood)) {
        return ok<MissionApplicationStage>('ShortlistGood');
      }

      if (reviewStatus.waitlisted.includes(MissionApplicationReviewStatusWaitlisted.WaitlistedStrong)) {
        return ok<MissionApplicationStage>('ShortlistStrong');
      }
    }

    const hasVettingScheduled = badges.includes('VettingScheduled') || badges.includes('VettingInterviewDate');
    const stage = hasVettingScheduled ? 'VettingScheduled' : 'New';

    return ok<MissionApplicationStage>(stage);
  };
}
