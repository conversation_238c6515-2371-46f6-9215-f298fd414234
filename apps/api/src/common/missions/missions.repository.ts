import { Mission, Account, Prisma } from '@a_team/prisma';
import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

export type MissionWithAccountInfo = Mission & { accountModel: Account };

type FindMissionByIdErrorCodes = 'FIND_MISSION_BY_ID_NOT_FOUND' | 'FIND_MISSION_BY_ID_DB_ERROR';

type FindMissionWithAccountInfoByIdErrorCodes =
  | 'FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_MISSION_NOT_FOUND'
  | 'FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_DB_ERROR'
  | 'FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_ACCOUNT_NOT_FOUND';

type FindRoleByIdErrorCodes = 'FIND_ROLE_BY_ID_NOT_FOUND' | 'FIND_ROLE_BY_ID_DB_ERROR';

@Injectable()
export class CommonMissionsRepository {
  constructor(private readonly prisma: DbService) {}

  findMissionById = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.mission.findFirst({ where: { mid: id } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionByIdErrorCodes>('FIND_MISSION_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindMissionByIdErrorCodes>('FIND_MISSION_BY_ID_NOT_FOUND');
    }

    return ok(result.value);
  };

  findMissionWithAccountInfoById = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.prisma.mission.findFirst({ where: { mid: id }, include: { accountModel: true } }), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<FindMissionWithAccountInfoByIdErrorCodes>('FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindMissionWithAccountInfoByIdErrorCodes>('FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_MISSION_NOT_FOUND');
    }

    if (!result.value.accountModel) {
      return Errors.createError<FindMissionWithAccountInfoByIdErrorCodes>('FIND_MISSION_WITH_ACCOUNT_INFO_BY_ID_ACCOUNT_NOT_FOUND');
    }

    return ok(result.value as MissionWithAccountInfo);
  };

  updateMissionRole = async (mid: string, roleId: string, data: Prisma.MissionRoleUpdateInput): Promise<Mission> => {
    return this.prisma.mission.update({
      where: { mid },
      data: {
        roles: {
          updateMany: {
            where: { id: roleId },
            data,
          },
        },
      },
    });
  };

  updateMission = async (mid: string, data: Prisma.MissionUpdateInput): Promise<Mission> => {
    return this.prisma.mission.update({
      where: {
        mid,
      },
      data,
    });
  };

  findRoleById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.mission.findFirst({
        where: {
          roles: {
            some: {
              id,
            },
          },
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindRoleByIdErrorCodes>('FIND_ROLE_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    if (!result.value) {
      return Errors.createError<FindRoleByIdErrorCodes>('FIND_ROLE_BY_ID_NOT_FOUND');
    }

    const role = result.value.roles.find((role) => role.id === id);

    if (!role) {
      return Errors.createError<FindRoleByIdErrorCodes>('FIND_ROLE_BY_ID_NOT_FOUND');
    }

    return ok(role);
  };
}
