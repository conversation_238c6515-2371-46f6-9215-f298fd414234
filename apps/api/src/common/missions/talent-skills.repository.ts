import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindAllTalentSkillsErrorCodes = 'FIND_ALL_TALENT_SKILLS_DB_ERROR';

@Injectable()
export class CommonTalentSkillsRepository {
  constructor(private readonly prisma: DbService) {}

  findAllTalentSkills = async () => {
    const result = await ResultAsync.fromPromise(
      this.prisma.talentCategory.findMany({
        where: {
          nodeType: 'skill',
          parentTalentCategoryIds: {
            isEmpty: false,
          },
        },
        select: {
          id: true,
          name: true,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindAllTalentSkillsErrorCodes>('FIND_ALL_TALENT_SKILLS_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
