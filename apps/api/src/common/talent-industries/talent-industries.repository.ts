import { Injectable } from '@nestjs/common';
import { ok, ResultAsync } from 'neverthrow';

import { DbService } from '@lib/global/db.service';

import { Errors } from '@lib/errors';

type FindTalentIndustryByIdErrorCodes = 'FIND_TALENT_INDUSTRY_BY_ID_DB_ERROR';

type FindAllTalentIndustriesErrorCodes = 'FIND_ALL_TALENT_INDUSTRIES_DB_ERROR';

@Injectable()
export class CommonTalentIndustriesRepository {
  constructor(private readonly prisma: DbService) {}

  findTalentIndustryById = async (id: string) => {
    const result = await ResultAsync.fromPromise(
      this.prisma.talentIndustry.findFirst({
        where: { id },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<FindTalentIndustryByIdErrorCodes>('FIND_TALENT_INDUSTRY_BY_ID_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  findAllTalentIndustries = async () => {
    const result = await ResultAsync.fromPromise(this.prisma.talentIndustry.findMany(), (error) => (error instanceof Error ? error : new Error(String(error))));

    if (result.isErr()) {
      return Errors.createError<FindAllTalentIndustriesErrorCodes>('FIND_ALL_TALENT_INDUSTRIES_DB_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };
}
