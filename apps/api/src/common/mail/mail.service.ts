import { Injectable } from '@nestjs/common';
import SendGrid from '@sendgrid/mail';
import { ok, Result, ResultAsync } from 'neverthrow';
import { z } from 'zod';

import { ConfigService } from '@config/config.service';
import { Config } from '@config/configuration';

import { Errors } from '@lib/errors';
import { Utils } from '@lib/utils';

import { MAIL_SENDERS, MAIL_TEMPLATE_IDS } from './constants';

import { MAIL_FROM_OPTIONS, MailFrom, SendMailData, sendMailSchema } from './schemas';

type ValidateMailDataErrorCodes = 'VALIDATE_EMAIL_DATA_PARSE_ERROR';

type SendEmailErrorCodes = 'SEND_EMAIL_FAILED_AFTER_MAX_RETRIES' | 'SEND_EMAIL_VALIDATION_EMAIL_DATA_ERROR';

export type MailServiceConfig = {
  apiKey: string;
  maxRetries?: number;
  retryDelayMs?: number;
};

@Injectable()
export class CommonMailService {
  /** Environment */
  private readonly env: Config['ENV'];
  /** Maximum number of retry attempts for failed email sends */
  private readonly maxRetries: number;
  /** Delay in milliseconds between retry attempts */
  private readonly retryDelayMs: number;
  /** Sendgrid categories */
  private readonly categories: string[];

  private safeSendMailSchemaParse = Result.fromThrowable(
    (data) => sendMailSchema.parse(data),
    (e) => e as z.ZodError
  );

  /**
   * Checks if the from property is defined as {@link MailFrom}.
   *
   * @param  {SendMailData['from']} from - The from property
   * @returns boolean - Value that defines if the from is {@link MailFrom}
   */
  private isFromPredefined = (from: SendMailData['from']): from is MailFrom => {
    return typeof from === 'string' && MAIL_FROM_OPTIONS.includes(from);
  };

  /**
   * Validates required fields in email data using Zod schema.
   *
   * @param {MailData} mailData - Email data to validate
   */
  private validateMailData(mailData: SendMailData) {
    const result = this.safeSendMailSchemaParse(mailData);

    if (result.isErr()) {
      const error = result.error;

      const issues = error.issues.map((issue) => `${issue.path.join('.')}: ${issue.message}`).join('; ');
      const originalError = new Error(`Errors parsing mail schema: ${issues}`);

      return Errors.createError<ValidateMailDataErrorCodes>('VALIDATE_EMAIL_DATA_PARSE_ERROR', {
        isUnexpectedError: true,
        originalError,
      });
    }

    return ok();
  }

  /**
   * Combines base categories with additional categories. Ensures uniqueness of categories.
   *
   * @param {string[]} [categories=[]] - Additional categories to be added
   * @returns {string[]} - Array of unique combined categories
   */
  private addCategories(categories: string[] = []): string[] {
    const baseCategories = this.getBaseCategories();
    const uniqueCategories = new Set([...baseCategories, ...categories]);

    return Array.from(uniqueCategories);
  }

  /**
   * Prepares mail data for sending by adding template ID when template name is provided
   * and combines base categories with additional categories.
   *
   * @param {SendMailData} sendMailData - The mail data to prepare
   * @returns {SendGrid.MailDataRequired} - Prepared mail data with resolved template ID and categories
   */
  private prepareMailData(sendMailData: SendMailData): SendGrid.MailDataRequired {
    const fromInput = sendMailData.from;
    const templateId = MAIL_TEMPLATE_IDS[sendMailData.templateName];

    const fromData = this.isFromPredefined(fromInput) ? MAIL_SENDERS[fromInput] : fromInput;

    return {
      to: sendMailData.to,
      from: fromData,
      templateId,
      categories: this.addCategories(sendMailData.categories),
      dynamicTemplateData: sendMailData.dynamicTemplateData,
      customArgs: {
        tags: {
          env: this.env,
          sourceName: fromData.name ?? '',
          sourceAddress: fromData.email,
          templateName: sendMailData.templateName,
          subject: sendMailData.dynamicTemplateData.subject,
        },
      },
    };
  }

  constructor(configService: ConfigService) {
    SendGrid.setApiKey(configService.get('SENDGRID_API_KEY'));

    this.maxRetries = 3;
    this.retryDelayMs = 1000;

    this.categories = [`env:${configService.get('ENV')}`, 'app:core-platform-api'];
    this.env = configService.get('ENV');
  }

  protected getBaseCategories(): string[] {
    return this.categories;
  }

  /**
   * Sends a single email with retry mechanism.
   *
   * @public
   * @param {SendMailData} mailData - Email data to send
   */
  public async sendEmail(mailData: SendMailData) {
    const validationResult = this.validateMailData(mailData);

    if (validationResult.isErr()) {
      const error = validationResult.error;

      return Errors.createError<SendEmailErrorCodes>('SEND_EMAIL_VALIDATION_EMAIL_DATA_ERROR', {
        isUnexpectedError: error.isUnexpectedError,
        originalError: error.isUnexpectedError ? error.originalError : undefined,
      });
    }

    const preparedMailData = this.prepareMailData(mailData);

    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      const sendgridResult = await ResultAsync.fromPromise(SendGrid.send(preparedMailData, false), (e) => e);

      if (sendgridResult.isErr()) {
        await Utils.sleep(this.retryDelayMs);
        continue;
      }

      return ok(sendgridResult.value);
    }

    return Errors.createError<SendEmailErrorCodes>('SEND_EMAIL_FAILED_AFTER_MAX_RETRIES');
  }
}
