import { z } from 'zod';

export const MAIL_FROM_OPTIONS = ['Hello'] as const;

const singleRecipientSchema = z.union([z.string().email(), z.object({ email: z.string().email() })]);
const recipientsSchema = z.union([singleRecipientSchema, z.array(singleRecipientSchema)]);

const senderSchema = z.union([z.enum(MAIL_FROM_OPTIONS), z.object({ name: z.string().optional(), email: z.string() })]);

const baseMailSchema = z.object({
  from: senderSchema,
  to: recipientsSchema,
  categories: z.array(z.string()).optional(),
});

export const sendMailSchema = z.discriminatedUnion('templateName', [
  baseMailSchema.extend({
    templateName: z.literal('Generic'),
    dynamicTemplateData: z.object({
      subject: z.string(),
      title: z.string(),
      content: z.string(),
    }),
  }),
]);

export type SendMailData = z.infer<typeof sendMailSchema>;

export type MailTemplate = SendMailData['templateName'];
export type MailFrom = (typeof MAIL_FROM_OPTIONS)[number];
