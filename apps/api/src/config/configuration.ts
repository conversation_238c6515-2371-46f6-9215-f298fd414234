import { z } from 'zod';

const ENVIRONMENTS = ['development', 'production', 'local'] as const;

const configSchema = z
  .object({
    ENV: z.enum(ENVIRONMENTS).default('local'),
    PORT: z
      .string()
      .default('4000')
      .transform((val) => parseInt(val, 10)),

    JWT_SECRET: z.string(),
    ACCOUNT_JWT_SECRET: z.string(),

    SENTRY_DSN: z.string().optional(),

    WEB_BASE_URL: z.string().url(),

    ADMIN_WEB_BASE_URL: z.string().url(),

    API_BASE_URL: z.string(),

    MONGODB_URL: z.string(),

    OPENAI_API_KEY: z.string(),

    PANDADOC_WEBHOOK_SHARED_KEY: z.string(),
    PANDADOC_API_BASE_URL: z.string().default('https://api.pandadoc.com'),
    PANDADOC_API_KEY: z.string(),

    HUBSPOT_ACCESS_TOKEN: z.string(),
    HUBSPOT_OPPORTUNITY_PIPELINE_ID: z.string(),
    HUBSPOT_MISSION_SENT_STAGE_ID: z.string(),

    PLATFORM_WEB_URL: z.string().url(),
    PLATFORM_API_URL: z.string().url(),
    PLATFORM_ADMIN_JWT: z.string(),

    SLACK_TOKEN: z.string(),
    SLACK_CHANNEL_OVERRIDE: z.string().optional(),

    API_KEY_PLATFORM_API: z.string(),

    SENDGRID_API_KEY: z.string(),

    DEFAULT_TEAM_ADVISOR_ID: z.string(),

    CLIENT_APP_SERVER_V1_URL: z.string(),
    CLIENT_APP_SERVER_V1_API_KEY: z.string(),

    UPLOADCARE_PUBLIC_KEY: z.string(),
    UPLOADCARE_SECRET_KEY: z.string(),

    APOLLO_BASE_URL: z.string().default('https://api.apollo.io'),
    APOLLO_API_KEY: z.string(),

    GOOGLE_CLIENT_ID: z.string(),
    GOOGLE_CLIENT_SECRET: z.string(),

    LANGFUSE_BASE_URL: z.string(),
    LANGFUSE_SECRET_KEY: z.string(),
    LANGFUSE_PUBLIC_KEY: z.string(),

    TFS_EMAILS: z.array(z.string()).default(['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']),

    DAILY_API_URL: z.string().default('https://api.daily.co/v1/rooms'),
    DAILY_API_KEY: z.string(),
  })
  .transform((data) => {
    const { API_KEY_PLATFORM_API, ...env } = data;

    return {
      ...env,
      API_KEYS: [API_KEY_PLATFORM_API],
    };
  });

export type Config = z.infer<typeof configSchema>;

/**
 * Config function used for returning the required config on startup.
 *
 * @returns Config - The config returned
 */
export const configuration = (): Config => configSchema.parse(process.env);
