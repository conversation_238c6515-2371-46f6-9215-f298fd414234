/* eslint-disable @typescript-eslint/no-explicit-any */
import { logger } from '@a_team/logger';
import { Injectable, LoggerService } from '@nestjs/common';

@Injectable()
export class ServiceLogger implements LoggerService {
  private readonly IGNORE_CONTEXT_LOGS: string[];

  constructor() {
    this.IGNORE_CONTEXT_LOGS = ['InstanceLoader', 'RoutesResolver', 'RouterExplorer', 'NestFactory', 'NestApplication'];
  }

  log(message: any, ...optionalParams: any[]) {
    // For filtering initial nest logs
    if (optionalParams.length > 0 && typeof optionalParams[0] === 'string' && this.IGNORE_CONTEXT_LOGS.includes(optionalParams[0])) {
      return;
    }

    if (optionalParams.length > 0) {
      const context = optionalParams[0] as object;
      logger.info(context, message as string);

      return;
    }

    logger.info(message as string);
  }

  error(message: any, ...optionalParams: any[]) {
    if (optionalParams.length > 0) {
      const context = optionalParams[0] as object;
      logger.error(context, message as string);

      return;
    }

    logger.error(message as string);
  }

  warn(message: any, ...optionalParams: any[]) {
    if (optionalParams.length > 0) {
      const context = optionalParams[0] as object;
      logger.warn(context, message as string);

      return;
    }

    logger.warn(message as string);
  }

  debug(message: any, ...optionalParams: any[]) {
    if (optionalParams.length > 0) {
      const context = optionalParams[0] as object;
      logger.debug(context, message as string);

      return;
    }

    logger.debug(message as string);
  }

  verbose(message: any, ...optionalParams: any[]) {
    if (optionalParams.length > 0) {
      const context = optionalParams[0] as object;
      logger.trace(context, message as string);

      return;
    }

    logger.trace(message as string);
  }
}
