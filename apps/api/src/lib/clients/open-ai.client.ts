import { createOpenAI, OpenAIProvider } from '@ai-sdk/openai';
import { Injectable } from '@nestjs/common';
import { generateObject, GenerateObjectResult, LanguageModelV1 } from 'ai';
import { Err, ok, Result } from 'neverthrow';
import { ResultAsync } from 'neverthrow';
import { z } from 'zod';

import { ConfigService } from '@config/config.service';

import { CodedError, Errors } from '@lib/errors';

export type GenerateObjectParams<T extends z.ZodType> = {
  model?: LanguageModelV1;
  schema: T;
  prompt: string;
};

export type GenerateObjectErrorCodes = 'GENERATE_OBJECT_API_ERROR';
export type GenerateArrayErrorCodes = 'GENERATE_ARRAY_API_ERROR';

@Injectable()
export class OpenAIClient {
  private openai: OpenAIProvider;
  constructor(configService: ConfigService) {
    this.openai = createOpenAI({ apiKey: configService.get('OPENAI_API_KEY') });
  }

  /**
   * Generic integration function used for generating an object for the defined schema T and prompt.
   *
   * @param  {GenerateObjectParams<T>} params - The params to generate the object
   */
  generateObject = async <T extends z.ZodType>(
    params: GenerateObjectParams<T>
  ): Promise<Result<GenerateObjectResult<z.infer<T>>, never> | Err<never, CodedError<GenerateObjectErrorCodes>>> => {
    const { model, schema, prompt } = params;

    const result = await ResultAsync.fromPromise(
      generateObject({
        model: model ?? this.openai('gpt-4-turbo'),
        schema,
        prompt,
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GenerateObjectErrorCodes>('GENERATE_OBJECT_API_ERROR', {
        originalError: result.error,
        isUnexpectedError: true,
      });
    }

    return ok(result.value);
  };

  /**
   * Generic integration function used for generating an array of results for the defined schema T and prompt.
   *
   * @param  {GenerateObjectParams<T>} params - The params to generate the array
   */
  generateArray = async <T extends z.ZodType>(
    params: GenerateObjectParams<T>
  ): Promise<Result<GenerateObjectResult<z.infer<T>[]>, never> | Err<never, CodedError<GenerateArrayErrorCodes>>> => {
    const { model, schema, prompt } = params;

    const result = await ResultAsync.fromPromise(
      generateObject({
        model: model ?? this.openai('gpt-4-turbo'),
        schema,
        prompt,
        output: 'array',
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GenerateArrayErrorCodes>('GENERATE_ARRAY_API_ERROR', {
        originalError: result.error,
        isUnexpectedError: true,
      });
    }

    return ok(result.value);
  };
}
