import { Injectable, Logger } from '@nestjs/common';
import { convert, deleteFile, UploadcareSimpleAuthSchema, ConversionType } from '@uploadcare/rest-client';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

type GenerateVideoThumbnailErrorCodes = 'GENERATE_VIDEO_THUMBNAIL_CONVERT_ERROR';

type GenerateVideoThumbnailByUrlErrorCodes = 'GENERATE_VIDEO_THUMBNAIL_BY_URL_INVALID_URL';

type DeleteFileErrorCodes = 'DELETE_FILE_ERROR';

type DeleteFileByUrlErrorCodes = 'DELETE_FILE_BY_URL_INVALID_URL';

@Injectable()
export class UploadcareClient {
  private readonly UPLOADCARE_UUID_PATTERN = /\b([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})\b/i;

  private readonly uploadcareSimpleAuthSchema: UploadcareSimpleAuthSchema;

  /**
   * Helper function to extract the Uploadcare uuid from an url if present.
   *
   * @param url - The URL potentially containing an Uploadcare uuid
   * @returns The extracted uuid or empty string if no valid uuid is found
   */
  private extractUuid(url: string): string {
    if (!url) {
      return '';
    }

    const match = url.match(this.UPLOADCARE_UUID_PATTERN);
    return match?.[1] ?? '';
  }

  constructor(configService: ConfigService) {
    this.uploadcareSimpleAuthSchema = new UploadcareSimpleAuthSchema({
      publicKey: configService.get('UPLOADCARE_PUBLIC_KEY'),
      secretKey: configService.get('UPLOADCARE_SECRET_KEY'),
    });
  }

  /**
   * Generates a thumbnail from a video file using its uuid.
   *
   * @param videoUuid - The uuid of the video file in Uploadcare
   */
  generateVideoThumbnail = async (videoUuid: string) => {
    const result = await ResultAsync.fromPromise(
      convert(
        {
          type: ConversionType.VIDEO,
          paths: [`${videoUuid}/video/-/thumbs~1/`],
          store: true,
        },
        {
          authSchema: this.uploadcareSimpleAuthSchema,
        }
      ),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<GenerateVideoThumbnailErrorCodes>('GENERATE_VIDEO_THUMBNAIL_CONVERT_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  /**
   * Generates a thumbnail from a video file using its URL.
   *
   * @param videoUrl - The URL of the video file in Uploadcare
   */
  generateVideoThumbnailByUrl = async (videoUrl: string) => {
    const uuid = this.extractUuid(videoUrl);

    if (!uuid) {
      Logger.warn('[lib/clients/uploadcare.client/generateVideoThumbnailByUrl] - Invalid Uploadcare URL or missing uuid', videoUrl);
      return Errors.createError<GenerateVideoThumbnailByUrlErrorCodes>('GENERATE_VIDEO_THUMBNAIL_BY_URL_INVALID_URL');
    }

    return await this.generateVideoThumbnail(uuid);
  };

  /**
   * Deletes a file from Uploadcare using its uuid.
   *
   * @param fileUuid - The uuid of the file to delete
   */
  deleteFile = async (fileUuid: string) => {
    const result = await ResultAsync.fromPromise(
      deleteFile(
        { uuid: fileUuid },
        {
          authSchema: this.uploadcareSimpleAuthSchema,
        }
      ),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (result.isErr()) {
      return Errors.createError<DeleteFileErrorCodes>('DELETE_FILE_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok(result.value);
  };

  /**
   * Deletes a file from Uploadcare using its URL.
   *
   * @param fileUrl - The URL of the file to delete
   */
  deleteFileByUrl = async (fileUrl: string) => {
    const uuid = this.extractUuid(fileUrl);

    if (!uuid) {
      Logger.warn('[lib/clients/uploadcare.client/deleteFileByUrl] - Invalid Uploadcare URL or missing uuid', fileUrl);
      return Errors.createError<DeleteFileByUrlErrorCodes>('DELETE_FILE_BY_URL_INVALID_URL');
    }

    return await this.deleteFile(uuid);
  };
}
