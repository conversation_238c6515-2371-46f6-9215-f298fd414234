import { Injectable } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';
import { SentryService } from '@lib/global/sentry.service';

import { Errors } from '@lib/errors';

type LangfuseClientGetPromptError = 'LANGFUSE_CLIENT_GET_PROMPT_API_ERROR' | 'LANGFUSE_CLIENT_GET_PROMPT_NOT_FOUND';

@Injectable()
export class LangfuseClient {
  private readonly client: AxiosInstance;

  constructor(
    private readonly sentryService: SentryService,
    configService: ConfigService
  ) {
    this.client = axios.create({
      baseURL: configService.get('LANGFUSE_BASE_URL'),
      auth: {
        username: configService.get('LANGFUSE_PUBLIC_KEY'),
        password: configService.get('LANGFUSE_SECRET_KEY'),
      },
    });
  }

  /**
   * Client function used for getting a prompt from langfuse.
   *
   * @param {string} promptName - The name of the prompt to get.
   * @param {Record<string, string>} variables - The variables to build the prompt.
   */
  getPrompt = async (promptName: string, variables: Record<string, string>) => {
    const promptTemplate = await ResultAsync.fromPromise(this.client.get<{ prompt: string }>(`/api/public/v2/prompts/${promptName}`), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (promptTemplate.isErr()) {
      this.sentryService.logAndCaptureError(
        `[lib/clients/langfuse/langfuse.client/getPrompt] - Error fetching prompt ${promptName} - ${promptTemplate.error}`,
        promptTemplate.error
      );

      return Errors.createError<LangfuseClientGetPromptError>('LANGFUSE_CLIENT_GET_PROMPT_API_ERROR', {
        isUnexpectedError: true,
        originalError: promptTemplate.error,
      });
    }

    if (!promptTemplate.value.data.prompt) {
      const error = new Error(`[lib/clients/langfuse/langfuse.client/getPrompt] - Prompt ${promptName} not found`);
      this.sentryService.logAndCaptureError(error.message, error);
      return Errors.createError<LangfuseClientGetPromptError>('LANGFUSE_CLIENT_GET_PROMPT_NOT_FOUND');
    }

    const vars = variables;
    const langfusePrompt = promptTemplate.value.data.prompt;

    const prompt = Object.keys(vars).reduce((acc, key) => {
      return acc.replaceAll(`{{${key}}}`, vars[key]);
    }, langfusePrompt);

    return ok(prompt);
  };
}
