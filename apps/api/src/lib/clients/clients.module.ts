import { Modu<PERSON> } from '@nestjs/common';

import { ApolloClient } from './apollo.client';
import { ClientAppV1Client } from './client-app-v1.client';
import { LangfuseClient } from './langfuse.client';
import { OpenAIClient } from './open-ai.client';
import { PandadocApiClient } from './pandadoc-api.client';
import { PlatformServiceClient } from './platform/platform.client';
import { UploadcareClient } from './uploadcare.client';

@Module({
  providers: [OpenAIClient, PlatformServiceClient, UploadcareClient, PandadocApiClient, ApolloClient, LangfuseClient, ClientAppV1Client],
  exports: [OpenAIClient, PlatformServiceClient, UploadcareClient, PandadocApiClient, ApolloClient, LangfuseClient, ClientAppV1Client],
})
export class ClientsModule {}
