import { Injectable } from '@nestjs/common';
import axios, { Axios, AxiosError, AxiosResponse } from 'axios';
import { from<PERSON>rom<PERSON>, ok } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

type FundingEvent = {
  id: string;
  date: string;
  news_url: string | null;
  type: string;
  investors: string;
  amount: string;
  currency: string;
};

export type ApolloOrganizationBySearch = {
  id: string;
  name: string;
  website_url: string;
  blog_url: string | null;
  angellist_url: string | null;
  linkedin_url: string | null;
  twitter_url: string | null;
  facebook_url: string | null;
  primary_phone: Partial<{
    number: string;
    source: string;
    sanitized_number: string;
  }>;
  languages: string[];
  alexa_ranking: number;
  phone: string | null;
  linkedin_uid: string;
  founded_year: number;
  publicly_traded_symbol: string | null;
  publicly_traded_exchange: string | null;
  logo_url: string;
  crunchbase_url: string | null;
  primary_domain: string;
  sanitized_phone: string | null;
  owned_by_organization_id: string | null;
  intent_strength: number | null;
  show_intent: boolean;
  has_intent_signal_account: boolean;
};

export type EnrichedApolloOrganization = ApolloOrganizationBySearch & {
  industry: string;
  keywords: string[];
  estimated_num_employees: number;
  industries: string[];
  secondary_industries: string[];
  snippets_loaded: boolean;
  industry_tag_id: string;
  industry_tag_hash: Record<string, string>;
  retail_location_count: number;
  raw_address: string;
  street_address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  seo_description: string;
  short_description: string;
  num_suborganizations: number;
  annual_revenue_printed: string;
  annual_revenue: number;
  total_funding: number;
  total_funding_printed: string;
  latest_funding_round_date: string;
  latest_funding_stage: string;
  funding_events: FundingEvent[];
  technology_names: string[];
  org_chart_root_people_ids: string[];
  org_chart_sector: string;
  org_chart_removed: boolean;
  org_chart_show_department_filter: boolean;
  account_id: string;
};

type MixedCompaniesSearchResponseData = {
  organizations: ApolloOrganizationBySearch[];
};

type OrganizationsEnrichResponseData = {
  organization: EnrichedApolloOrganization;
};

type GetOrganizationByNameErrorCodes = 'GET_ORGANIZATION_BY_NAME_API_ERROR' | 'GET_ORGANIZATION_BY_NAME_NOT_FOUND';

type GetOrganizationByUrlErrorCodes = 'GET_ORGANIZATION_BY_URL_API_ERROR' | 'GET_ORGANIZATION_BY_URL_NOT_FOUND';

@Injectable()
export class ApolloClient {
  private readonly client: Axios;

  constructor(configService: ConfigService) {
    this.client = axios.create({
      baseURL: configService.get('APOLLO_BASE_URL'),
    });

    this.client.interceptors.request.use((config) => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      config.params = config.params ?? {};

      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      config.params.api_key = configService.get('APOLLO_API_KEY');
      return config;
    });
  }

  getOrganizationByName = async (companyName: string) => {
    const response = await fromPromise<AxiosResponse<MixedCompaniesSearchResponseData>, Error>(
      this.client.get<MixedCompaniesSearchResponseData>('/api/v1/mixed_companies/search', {
        params: {
          q_organization_name: companyName,
        },
      }),
      (error) => (error instanceof AxiosError ? error : new Error(String(error)))
    );

    if (response.isErr()) {
      return Errors.createError<GetOrganizationByNameErrorCodes>('GET_ORGANIZATION_BY_NAME_API_ERROR', {
        isUnexpectedError: true,
        originalError: response.error,
      });
    }

    const organization = response.value.data.organizations[0];

    if (!organization) {
      return Errors.createError<GetOrganizationByNameErrorCodes>('GET_ORGANIZATION_BY_NAME_NOT_FOUND');
    }

    return ok(organization);
  };

  getOrganizationByUrl = async (url: string) => {
    const domain = url.replace('http://', '').replace('https://', '');

    const response = await fromPromise<AxiosResponse<OrganizationsEnrichResponseData>, Error>(
      this.client.get<OrganizationsEnrichResponseData>('/v1/organizations/enrich', {
        params: {
          domain,
        },
      }),
      (error) => (error instanceof AxiosError ? error : new Error(String(error)))
    );

    if (response.isErr()) {
      return Errors.createError<GetOrganizationByUrlErrorCodes>('GET_ORGANIZATION_BY_URL_API_ERROR', {
        isUnexpectedError: true,
        originalError: response.error,
      });
    }

    const organization = response.value.data.organization;

    if (!organization) {
      return Errors.createError<GetOrganizationByUrlErrorCodes>('GET_ORGANIZATION_BY_URL_NOT_FOUND');
    }

    return ok(organization);
  };
}
