import { Injectable } from '@nestjs/common';
import { InterviewWebhookPayload } from '@packages/contracts';
import axios, { Axios } from 'axios';
import { ok, ResultAsync } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

type InviteMissionCollaboratorData = {
  missionSpecId: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'missionadmin' | 'missionmember';
  skipEmail: boolean;
};

type InviteMissionCollaboratorErrorCodes = 'INVITE_MISSION_COLLABORATOR_API_ERROR';

type PingInterviewSchedulingClientWebhookErrorCodes = 'PING_INTERVIEW_SCHEDULING_CLIENT_WEBHOOK_API_ERROR';

@Injectable()
export class ClientAppV1Client {
  private readonly client: Axios;

  constructor(configService: ConfigService) {
    this.client = axios.create({
      baseURL: configService.get('CLIENT_APP_SERVER_V1_URL'),
      headers: {
        Authorization: `Bearer ${configService.get('PLATFORM_ADMIN_JWT')}`,
      },
    });
  }

  // TODO: Remove this once we have RBAC setup on the new backend
  inviteMissionCollaborator = async (data: InviteMissionCollaboratorData, token: string) => {
    const response = await ResultAsync.fromPromise(
      this.client.post(`/mission-specs/${data.missionSpecId}/invite`, data, {
        headers: {
          Authorization: token,
        },
      }),
      (error) => (error instanceof Error ? error : new Error(String(error)))
    );

    if (response.isErr()) {
      return Errors.createError<InviteMissionCollaboratorErrorCodes>('INVITE_MISSION_COLLABORATOR_API_ERROR', {
        originalError: response.error,
        isUnexpectedError: true,
      });
    }

    return ok(response.value.data);
  };

  pingInterviewSchedulingWebhook = async (eventType: string, payload: InterviewWebhookPayload) => {
    const response = await ResultAsync.fromPromise(this.client.post(`/webhooks/v2/interview-scheduling/${eventType}`, payload), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (response.isErr()) {
      return Errors.createError<PingInterviewSchedulingClientWebhookErrorCodes>('PING_INTERVIEW_SCHEDULING_CLIENT_WEBHOOK_API_ERROR', {
        originalError: response.error,
        isUnexpectedError: true,
      });
    }

    return ok(response.value.data);
  };
}
