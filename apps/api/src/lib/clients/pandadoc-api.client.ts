import { Injectable } from '@nestjs/common';
import { PandadocContractData } from '@packages/contracts';
import axios, { Axios } from 'axios';

import { ConfigService } from '@config/config.service';

@Injectable()
export class PandadocApiClient {
  private client: Axios;

  constructor(configService: ConfigService) {
    this.client = axios.create({
      baseURL: configService.get('PANDADOC_API_BASE_URL'),
      headers: {
        Authorization: `API-Key ${configService.get('PANDADOC_API_KEY')}`,
      },
    });
  }

  public getPandadocSigningUrl = async (pandadocId: string, userEmail: string): Promise<string | undefined> => {
    const { data } = await this.client.get<PandadocContractData>(`/public/v1/documents/${pandadocId}/details`);

    const recipients = data.recipients ?? [];
    const matchingRecipient = recipients.find((r) => r.email === userEmail);

    return matchingRecipient?.shared_link;
  };

  public getPandadocDocumentPdf = async (pandadocId: string): Promise<Buffer> => {
    const { data } = await this.client.get<ArrayBuffer>(`/public/v1/documents/${pandadocId}/download`, {
      responseType: 'arraybuffer',
    });

    return Buffer.from(data);
  };
}
