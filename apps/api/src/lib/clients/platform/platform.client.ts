import { Injectable } from '@nestjs/common';
import { InterviewWebhookPayload, UserBadge } from '@packages/contracts';
import axios, { Axios, isAxiosError } from 'axios';
import { ResultAsync, ok } from 'neverthrow';

import { ConfigService } from '@config/config.service';

import { Errors } from '@lib/errors';

import { MissionAdminObject, UpdateMissionData } from './platform.types';

type PlatformSignInResponse = {
  user: { uid: string };
  token: string;
  refreshToken: string;
};

type GetMissionByIdErrorCodes = 'GET_MISSION_BY_ID_API_ERROR';

type GetUserBadgesErrorCodes = 'GET_USER_BADGES_API_ERROR';

type UpdateMissionByIdErrorCodes = 'UPDATE_MISSION_BY_ID_API_ERROR';

type SignInWithPasswordErrorCodes = 'SIGN_IN_WITH_PASSWORD_API_ERROR';

type PingInterviewSchedulingPlatformWebhookErrorCodes = 'PING_INTERVIEW_SCHEDULING_PLATFORM_WEBHOOK_API_ERROR';

export type PlatformSignInData = {
  userId: string;
  token: string;
  refreshToken: string;
};

@Injectable()
export class PlatformServiceClient {
  private readonly client: Axios;

  constructor(configService: ConfigService) {
    this.client = axios.create({
      baseURL: configService.get('PLATFORM_API_URL'),
      headers: {
        Authorization: `Bearer ${configService.get('PLATFORM_ADMIN_JWT')}`,
      },
    });
  }

  signInWithPassword = async (email: string, password: string) => {
    const result = await ResultAsync.fromPromise(this.client.post<PlatformSignInResponse>(`/auth/login`, { email, password }), (error) =>
      isAxiosError(error) ? error : new Error(String(error))
    );

    if (result.isErr()) {
      const error = result.error;

      return Errors.createError<SignInWithPasswordErrorCodes>('SIGN_IN_WITH_PASSWORD_API_ERROR', {
        isUnexpectedError: true,
        originalError: error,
      });
    }

    const { data } = result.value;

    return ok({
      userId: data.user.uid,
      token: data.token,
      refreshToken: data.refreshToken,
    });
  };

  // TODO: Remove after V2 adming settings implementation
  getMissionById = async (id: string) => {
    const result = await ResultAsync.fromPromise(this.client.get<MissionAdminObject>(`/missions/${id}?admin=true`), (error) =>
      isAxiosError(error) ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<GetMissionByIdErrorCodes>('GET_MISSION_BY_ID_API_ERROR', {
        originalError: result.error,
        isUnexpectedError: true,
      });
    }

    const response = result.value;
    return ok(response.data);
  };

  // TODO: Remove after V2 adming settings implementation
  updateMissionById = async (missionId: string, missionData: UpdateMissionData) => {
    const result = await ResultAsync.fromPromise(this.client.put(`/missions/${missionId}`, missionData), (error) =>
      isAxiosError(error) ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<UpdateMissionByIdErrorCodes>('UPDATE_MISSION_BY_ID_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    return ok();
  };

  // TODO: Remove this platform call once the logic for badges is implemented in v2 backend
  getUserBadges = async (userId: string) => {
    const result = await ResultAsync.fromPromise(this.client.get<{ badges: UserBadge[] }>(`/users/id/${userId}`), (error) =>
      isAxiosError(error) ? error : new Error(String(error))
    );

    if (result.isErr()) {
      return Errors.createError<GetUserBadgesErrorCodes>('GET_USER_BADGES_API_ERROR', {
        isUnexpectedError: true,
        originalError: result.error,
      });
    }

    const badges = result.value.data.badges;

    return ok(badges);
  };

  pingInterviewSchedulingWebhook = async (eventType: string, payload: InterviewWebhookPayload) => {
    const response = await ResultAsync.fromPromise(this.client.post(`/webhooks/interview-scheduling/${eventType}`, payload), (error) =>
      error instanceof Error ? error : new Error(String(error))
    );

    if (response.isErr()) {
      return Errors.createError<PingInterviewSchedulingPlatformWebhookErrorCodes>('PING_INTERVIEW_SCHEDULING_PLATFORM_WEBHOOK_API_ERROR', {
        originalError: response.error,
        isUnexpectedError: true,
      });
    }

    return ok(response.value.data);
  };
}
