import { z } from 'zod';

// TODO: Remove later after V2 mission settings are done
const MissionIdSchema = z.string();

const DateISOStringSchema = z.string();

const AccountIdSchema = z.string();

const UserUsernameSchema = z.string();

const MissionStatusSchema = z.enum(['Spec', 'Formation', 'Created', 'Published', 'Pending', 'Running', 'ScheduledToEnd', 'Ended', 'Archived']);

export const missionApplyStatusSchema = z.enum(['Collect', 'Review', 'Finalize', 'Deployed']);

export const MISSION_APPLY_STATUSES = missionApplyStatusSchema.options;
export type MissionApplyStatus = z.infer<typeof missionApplyStatusSchema>;

export const MissionBillingPeriodSchema = z.enum(['Daily (Test)', 'Weekly', 'BiWeekly', 'Monthly']);
export const MISSION_BILLING_PERIODS = MissionBillingPeriodSchema.options;

const MissionInvoicingPeriodSchema = z.enum(['BiWeekly', 'Monthly']);

const MissionThumbnailColorSchema = z.enum(['#FC2A50', '#FE8923', '#FFDF00', '#34B956', '#8CDF04', '#3CB9FF', '#BF5EFF', '#ECA86C', '#FF877B', '#D7CD9F']);

const MissionCardColorSchema = z.enum(['#AB54FF', '#08A5FE', '#FFAF14']);

const MissionManagerAccessModeSchema = z.enum(['RoleView']);

const BasicUserObjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  avatar: z.string().optional(),
  username: UserUsernameSchema,
  email: z.string().email().optional(),
});

const SenderUserObjectSchema = BasicUserObjectSchema.extend({
  email: z.string().email(),
  isAdmin: z.boolean().optional(),
});

const MissionLinkSchema = z.object({
  title: z.string(),
  URL: z.string().url(),
});

const TalentIndustrySummarySchema = z.object({
  id: z.string(),
  name: z.string(),
});

const TalentSkillRatingSchema = z.enum(['Basic', 'Intermediate', 'Advanced', 'Expert']);

const MissionRoleStatusSchema = z.enum(['Open', 'Pending', 'Active', 'ScheduledToEnd', 'Ended', 'Canceled']);
export const MissionRoleStatusOptions = MissionRoleStatusSchema.options;

const MissionRoleVisibilityStatusSchema = z.enum(['OnlyAdmin', 'All']);
export const MissionRoleVisibilityStatusOptions = MissionRoleVisibilityStatusSchema.options;

const MissionRoleVisibilitySchema = z.object({
  visibilityStatus: MissionRoleVisibilityStatusSchema,
});

export const RoleCategoryObjectSchema = z.object({
  cid: z.string(),
  title: z.string(),
  group: z.string(),
  anchors: z.array(z.string()),
  talentCategoryIds: z.array(z.string()),
});

export const RoleCategoryArraySchema = z.array(RoleCategoryObjectSchema);

export type RoleCategory = z.infer<typeof RoleCategoryObjectSchema>;

const TimezoneObjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  offset: z.number(),
  offsetString: z.string(),
});

const WorkingHoursMissionRoleSchemaSchema = z.object({
  name: z.string(),
  numberOfMinutesOverlap: z.number(),
  utcOffset: z.number(),
  daily: z.array(
    z.object({
      startTime: z.number(),
      endTime: z.number(),
    })
  ),
});

const MissionRoleSkillSchema = z.object({
  talentSkillId: z.string(),
  talentSkillName: z.string().optional(),
  rating: TalentSkillRatingSchema.optional(),
});

const MissionRoleRequiredSkillSchema = MissionRoleSkillSchema.extend({
  rating: TalentSkillRatingSchema,
});

const ClientRoleQuestionSchema = z.object({
  qid: z.string(),
  text: z.string(),
  isRequired: z.boolean().optional(),
  isVisible: z.boolean().optional(),
});

const MissionRoleBudgetSettingsSchema = z
  .object({
    requireHourlyRate: z.boolean().optional(),
    requireMonthlyRate: z.boolean().optional(),
    showHourlyBudget: z.boolean().optional(),
    showMonthlyBudget: z.boolean().optional(),
  })
  .optional();

const AnonymousUserObjectSchema = z.object({
  id: z.string().optional(),
  username: z.string().optional(),
  name: z.string(),
});

const BasicMissionRoleSchema = z.object({
  rid: z.string(),
  cid: z.string().optional(),
  category: RoleCategoryObjectSchema,
  headline: z.string(),
  headlineHtml: z.string().optional(),
  locations: z.array(z.string()).optional(),
  status: MissionRoleStatusSchema,
  recommended: z.boolean().nullable().optional(),
  user: z.union([AnonymousUserObjectSchema, BasicUserObjectSchema, z.null()]),
  availability: z
    .object({
      weeklyHoursAvailable: z.number(),
      date: DateISOStringSchema.optional(),
      scheduledEndDate: DateISOStringSchema.optional(),
    })
    .optional(),
  customQuestions: z.array(ClientRoleQuestionSchema).optional(),
  timezone: TimezoneObjectSchema.optional(),
  workingHours: WorkingHoursMissionRoleSchemaSchema.optional(),
  preferredSkills: z.array(MissionRoleSkillSchema).optional(),
  requiredSkills: z.array(MissionRoleRequiredSkillSchema).optional(),
  lookingForApplications: z.boolean().nullable().optional(),
  hourlyRate: z.number().optional(),
  monthlyRate: z.number().optional(),
  userAssignedAt: DateISOStringSchema.optional(),
  visibility: MissionRoleVisibilitySchema.optional(),
  builderRateMin: z.number().optional(),
  builderRateMax: z.number().optional(),
  showRateRangeToBuilders: z.boolean().optional(),
  builderMonthlyRateMin: z.number().optional(),
  builderMonthlyRateMax: z.number().optional(),
  collectMonthlyRate: z.boolean().optional(),
  isFullTimeRetainer: z.boolean().optional(),
  budgetSettings: MissionRoleBudgetSettingsSchema.optional(),
});

const UtcOffsetRangeSchema = z
  .object({
    from: TimezoneObjectSchema.optional(),
    to: TimezoneObjectSchema.optional(),
  })
  .optional();

const ApplicationRateRangeSchema = z.object({
  min: z.number(),
  max: z.number(),
});

const MissionApplicationBasicObjectSchema = z.object({
  aid: z.string(),
  rid: z.string(),
  status: z.string(),
  withdrawn: z.boolean().optional(),
  changes: z
    .object({
      lastReviewAt: DateISOStringSchema.optional(),
      pitchUpdatedAt: DateISOStringSchema.optional(),
      aboutUpdatedAt: DateISOStringSchema.optional(),
      rateUpdatedAt: DateISOStringSchema.optional(),
      availabilityUpdatedAt: DateISOStringSchema.optional(),
      workingHoursUpdatedAt: DateISOStringSchema.optional(),
      noteOnAvailabilityUpdatedAt: DateISOStringSchema.optional(),
      lastHourlyRateRange: ApplicationRateRangeSchema.optional(),
      customQuestionRepliesUpdatedAt: z.record(DateISOStringSchema).optional(),
    })
    .optional(),
});

const PendingPaymentTermSchema = z.object({
  missionId: z.string(),
  roleId: z.string(),
  builderContractId: z.string().optional(),
  clientContractId: z.string().optional(),
  type: z.enum(['monthly', 'hourly']),
  builderRate: z.number(),
  margin: z.number(),
  hoursPerWeek: z.number().optional(),
  effectiveFrom: DateISOStringSchema.optional(),
  clientSigned: z.boolean(),
  builderSigned: z.boolean(),
});

const MissionRoleSchema = BasicMissionRoleSchema.extend({
  application: MissionApplicationBasicObjectSchema.optional(),

  // will return only for the current user
  hourlyRate: z.number().optional(),
  marginVAT: z.number().optional(),

  // will return only for managers and admins
  clientHourlyRate: z.number().optional(),
  clientMonthlyRate: z.number().optional(),
  utcOffsetRange: UtcOffsetRangeSchema.optional(),
  lookingForApplications: z.boolean().nullable().optional(),
  pendingPaymentTerm: PendingPaymentTermSchema.optional(),
});

const RoleOptimizationRequestReasonSchema = z.enum([
  'companyDescriptionLacking',
  'hourlyRateNeedsClarification',
  'missionDescriptionLacking',
  'availabilityNeedsClarification',
  'roleDescriptionLacking',
  'videoMissing',
  'skillsNeedClarification',
  'videoNotWorking',
  'locationRequirementsNeedClarification',
]);

const RoleOptimizationRequestSchema = z.object({
  id: z.string(),
  roleId: z.string(),
  reasons: z.array(RoleOptimizationRequestReasonSchema),
  details: z.string().optional(),
  createdAt: z.date(),
  createdBy: z.string(),
});

const MissionAdminRoleSchema = MissionRoleSchema.extend({
  hourlyRate: z.number().optional(),
  marginVAT: z.number().optional(),
  utcOffsetRange: UtcOffsetRangeSchema.optional(),
  automatedStatusesDisabled: z.boolean().optional(),
  readyForReview: z.boolean().optional(),
  lastOptimizationRequest: RoleOptimizationRequestSchema.optional(),
  margin: z.number().nullable().optional(),
  isNiche: z.boolean().optional(),
  pendingPaymentTerm: PendingPaymentTermSchema.optional(),
});

export const billingPaymentDueSchema = z.enum(['Net0', 'Net15', 'Net30', 'Net45', 'Net60', 'Net90', 'Net120']);
export const BILLING_PAYMENT_DUE_OPTIONS = billingPaymentDueSchema.options;

const BillingPaymentTermsSchema = z.object({
  due: billingPaymentDueSchema,
});

const AddressInfoSchema = z.object({
  line1: z.string(),
  line2: z.string().optional(),
  city: z.string(),
  postalCode: z.string(),
  state: z.string().optional(),
  countryCode: z.string(),
});

const ClientBillingInfoSchema = z.object({
  contactName: z.string().optional(),
  contactEmail: z.string().email(),
  name: z.string(),
  tin: z.string().optional(),
  address: AddressInfoSchema.optional(),
});

const BillingAccountObjectSchema = z.object({
  id: z.string(),
  billingInfo: ClientBillingInfoSchema.optional(),
  paymentTerms: BillingPaymentTermsSchema.optional(),
});

const MissionPaymentCycleStatusSchema = z.enum(['Open', 'Closed']);

const PaymentCycleAdminSummarySchema = z.object({
  totalMinutes: z.number(),
  totalPayments: z.number(),
});

const InvoiceTypeSchema = z.enum(['MissionPaymentCycle', 'MissionRoleTimesheet', 'MissionRolePlatformFee', 'WorkspacePaymentCycle']);

const InvoiceStatusSchema = z.enum(['Created', 'Canceled', 'Processing', 'Paid', 'Static']);

const InvoiceCurrencySchema = z.enum(['USD']);

const InvoiceAddressSchema = z.object({
  name: z.string(),
  line1: z.string().optional(),
  line2: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
});

const BasicInvoiceObjectSchema = z.object({
  iid: z.string(),
  referenceNumber: z.number(),
  purchaseOrderNumber: z.string().optional(),
  approverName: z.string().optional(),
  type: InvoiceTypeSchema,
  status: InvoiceStatusSchema,
  hourlyRate: z.number().optional(),
  platformFee: z.number().optional(),
  platformMargin: z.number().optional(),
  totalAmount: z.number(),
  amountCharged: z.number(),
  chargeFee: z.number().optional(),
  totalVAT: z.number().optional(),
  discountAmount: z.number().optional(),
  discountReason: z.string().optional(),
  currency: InvoiceCurrencySchema,
  from: InvoiceAddressSchema,
  to: InvoiceAddressSchema,
  downloadURL: z.string().optional(),
  createdAt: DateISOStringSchema,
  issuedAt: DateISOStringSchema,
  paymentCycle: z.string().optional(),
  paymentTerms: z.string().optional(),
  paymentDueAt: DateISOStringSchema.optional(),
  paidAt: DateISOStringSchema.optional(),
  prepaid: z.boolean().optional(),
});

const TimesheetSummaryTypeSchema = z.enum(['Team', 'Builder']);

const TimesheetSummaryFeedbackActionSchema = z.enum(['Liked', 'Disliked']);

const TimesheetSummaryFeedbackObjectSchema = z.object({
  tsfid: z.string(),
  missionId: MissionIdSchema,
  userId: z.string(),
  paymentCycleId: z.string().optional(),
  timesheetId: z.string().optional(),
  type: TimesheetSummaryTypeSchema,
  action: TimesheetSummaryFeedbackActionSchema,
  response: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

const BasicMissionPaymentCycleAdminObjectSchema = z.object({
  yid: z.string(),
  status: MissionPaymentCycleStatusSchema,
  startDate: DateISOStringSchema,
  endDate: DateISOStringSchema,
  summary: PaymentCycleAdminSummarySchema,
  invoiceStatus: z.string().optional(),
  teamSummary: z.string().optional(),
  teamSummaryHtml: z.string().optional(),
  teamSummaryGeneratedAt: z.date().optional(),
  teamSummaryFeedbackFromAuthUser: z.array(TimesheetSummaryFeedbackObjectSchema).optional(),
});

const TimesheetIdSchema = z.string();

const TimesheetStatusSchema = z.enum(['Open', 'Submitted']);

const TimesheetRecordTypeSchema = z.enum([
  'Data & Analysis',
  'Design',
  'Documentation',
  'Engineering',
  'Growth',
  'Management',
  'Marketing',
  'Meetings',
  'Operational',
  'Other',
  'Planning',
  'Research',
  'Strategy',
  'Testing',
  'Holiday',
  'Sick Day',
  'Time off',
]);

const TimesheetRecordSchema = z.object({
  key: z.string(),
  date: DateISOStringSchema,
  minutes: z.number(),
  details: z.string(),
  type: TimesheetRecordTypeSchema.optional(),
  initiativeIds: z.array(z.string()).optional(),
  isRetainerBased: z.boolean().optional(),
});

const TimesheetObjectSchema = z.object({
  sid: TimesheetIdSchema,
  rid: z.string(), // MissionRoleId
  status: TimesheetStatusSchema,
  records: z.array(TimesheetRecordSchema),
  invoice: BasicInvoiceObjectSchema.optional(),
  demoLink: z.string().optional(),
  summary: z.string().optional(),
  summaryHtml: z.string().optional(),
  usedMachineTextForSummary: z.boolean().optional(),
});

const MissionPaymentCycleAdminObjectSchema = z.object({
  yid: z.string(),
  status: MissionPaymentCycleStatusSchema,
  startDate: DateISOStringSchema,
  endDate: DateISOStringSchema,
  summary: PaymentCycleAdminSummarySchema,
  invoiceStatus: z.string().optional(),
  teamSummary: z.string().optional(),
  teamSummaryHtml: z.string().optional(),
  teamSummaryGeneratedAt: z.date().optional(),
  teamSummaryFeedbackFromAuthUser: z.array(TimesheetSummaryFeedbackObjectSchema).optional(),
  timesheets: z.array(TimesheetObjectSchema).optional(),
  invoice: BasicInvoiceObjectSchema.optional(),
});

const PaymentCyclesAdminSchema = z.object({
  items: z.array(BasicMissionPaymentCycleAdminObjectSchema),
  next: z.string().nullable(),
  current: MissionPaymentCycleAdminObjectSchema,
});

const MissionCompanyRequestSchema = z.object({
  companyName: z.string(),
  websiteURL: z.string().url(),
  fullName: z.string(),
  role: z.string(),
  email: z.string().email(),
  phoneNumber: z.string(),
  description: z.string(),
  notes: z.string().optional(),
});

const MissionManagerSchema = z.object({
  accessMode: MissionManagerAccessModeSchema,
  excludeFromInvoiceEmails: z.boolean().optional(),
  excludeFromTeamPulseEmails: z.boolean().optional(),
  excludeFromBuilderFeedbackEmails: z.boolean().optional(),
  excludeFromMissionUpdatesEmails: z.boolean().optional(),
  username: UserUsernameSchema,
  user: BasicUserObjectSchema.extend({
    email: z.string().email(),
    uid: z.string().optional(),
    profilePictureURL: z.string().url().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
  }).optional(),
});

export type MissionManager = z.infer<typeof MissionManagerSchema>;

const MissionInvoicingSchema = z.object({
  purchaseOrderNumber: z.string().optional(),
});

const MissionConnectionsSchema = z.object({
  connectionsApplied: z.array(BasicUserObjectSchema),
  connectionsAssigned: z.array(BasicUserObjectSchema),
});

const MinimalMissionObjectSchema = z.object({
  mid: MissionIdSchema,
  title: z.string(),
});

const BasicMissionObjectSchema = MinimalMissionObjectSchema.extend({
  description: z.string(),
  status: MissionStatusSchema,
  applyStatus: missionApplyStatusSchema,
  logoURL: z.string().url().optional(),
  thumbnailColor: MissionThumbnailColorSchema.optional(),
  cardColor: MissionCardColorSchema.optional(),
  missionURL: z.string().url().optional(),
  missionSpecId: z.string().optional(),
  website: z.string().url().optional(),
  selectedStatus: MissionStatusSchema.optional(),
  internalMission: z.boolean().optional(),
  accountId: AccountIdSchema.optional(),
});

const MissionCardObjectSchema = BasicMissionObjectSchema.extend({
  shortCompanyDescription: z.string().optional(),
  companyStory: z.string().optional(),
  companyName: z.string().optional(),
  expectedDurationMonths: z.number().optional(),
  attachedLinks: z.array(MissionLinkSchema).nullable().optional(),
  videoURL: z.string().url().optional(),
  roles: z.array(BasicMissionRoleSchema),
  hidden: z.boolean(),
  promotedTags: z.array(z.string()),
  creator: BasicUserObjectSchema.nullable(),
  publisher: BasicUserObjectSchema.optional(),
  publishedAt: DateISOStringSchema.optional(),
  startedAt: DateISOStringSchema.optional(),
  updatedAt: DateISOStringSchema.optional(),
  endedAt: DateISOStringSchema.optional(),
  industries: z.array(TalentIndustrySummarySchema),
  owner: BasicUserObjectSchema.optional(),
  bdOwners: z.array(SenderUserObjectSchema).optional(),
  missionConnections: MissionConnectionsSchema.optional(),
});

const MissionPartialObjectSchema = MissionCardObjectSchema.extend({
  createdAt: DateISOStringSchema,
  roles: z.array(MissionRoleSchema),
  managers: z.array(MissionManagerSchema).optional(),
  internalDescription: z.string().optional(),
});

// Final MissionAdminObject Schema
export const MissionAdminObjectSchema = MissionPartialObjectSchema.extend({
  companyRequest: MissionCompanyRequestSchema.optional(),
  roles: z.array(MissionAdminRoleSchema),
  managers: z.array(MissionManagerSchema),
  billingPeriod: MissionBillingPeriodSchema,
  automaticInvoicingPeriod: MissionInvoicingPeriodSchema.optional(),
  clientMargin: z.number(),
  rolesMargin: z.number(),
  skipContracts: z.boolean().optional(),
  accountId: AccountIdSchema.optional(),
  billingAccount: BillingAccountObjectSchema.optional(),
  paymentCycles: PaymentCyclesAdminSchema.optional(),
  invoiceEmailGreeting: z.string().optional(),
  invoicing: MissionInvoicingSchema.optional(),
  mainManagerUsername: z.string().optional(),
  owner: BasicUserObjectSchema.extend({
    email: z.string().email(),
    uid: z.string().optional(),
    profilePictureURL: z.string().url().optional(),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
  }).optional(),
  paymentTerms: BillingPaymentTermsSchema.optional(),
  hubspotDealId: z.string().optional(),
  automatedStatusesDisabled: z.boolean().optional(),
});

export type MissionAdminObject = z.infer<typeof MissionAdminObjectSchema>;

// Update mission

// Define base types first
const UserIdSchema = z.string();
const MissionRoleIdSchema = z.string();
const RoleCategoryIdSchema = z.string();
const TalentSkillIdSchema = z.string();
const TalentIndustryIdSchema = z.string();

const ClientRoleQuestionDataSchema = ClientRoleQuestionSchema.extend({
  createdAt: DateISOStringSchema.optional(),
  updatedAt: DateISOStringSchema.optional(),
});

const MissionManagerEmailSettingsSchema = z.object({
  excludeFromInvoiceEmails: z.boolean().optional(),
  excludeFromTeamPulseEmails: z.boolean().optional(),
  excludeFromBuilderFeedbackEmails: z.boolean().optional(),
  excludeFromMissionUpdatesEmails: z.boolean().optional(),
});

const MissionManagerDataSchema = MissionManagerEmailSettingsSchema.extend({
  accessMode: MissionManagerAccessModeSchema,
  username: UserUsernameSchema,
});

// Define MissionRoleData schema
const MissionRoleDataSchema = z.object({
  rid: MissionRoleIdSchema.optional(),
  createdAt: DateISOStringSchema.optional(),
  requestedAt: DateISOStringSchema.optional(),
  submittedAt: DateISOStringSchema.optional(),
  publishedAt: DateISOStringSchema.optional(),
  createdBy: UserIdSchema.optional(),
  cid: RoleCategoryIdSchema.optional(),
  status: MissionRoleStatusSchema.optional(),
  headline: z.string(),
  headlineHtml: z.string().optional(),
  locations: z.array(z.string()).optional(),
  user: z
    .object({
      username: UserUsernameSchema.optional(),
      fullName: z.string().optional(),
    })
    .nullable()
    .optional(),
  hourlyRate: z.number().optional(),
  monthlyRate: z.number().optional(),
  margin: z.number().nullable().optional(),
  marginVAT: z.number().optional(),
  utcOffsetRange: UtcOffsetRangeSchema,
  availability: z.any().optional(), // This would need to be defined based on BasicMissionRole
  preferredSkills: z
    .array(
      z.object({
        talentSkillId: TalentSkillIdSchema,
      })
    )
    .optional(),
  requiredSkills: z
    .array(
      z.object({
        talentSkillId: TalentSkillIdSchema,
        rating: TalentSkillRatingSchema,
      })
    )
    .optional(),
  visibility: MissionRoleVisibilitySchema.optional(),
  builderRateMin: z.number().optional(),
  builderRateMax: z.number().optional(),
  showRateRangeToBuilders: z.boolean().optional(),
  customQuestions: z.array(ClientRoleQuestionDataSchema).optional(),
  automatedStatusesDisabled: z.boolean().optional(),
  builderMonthlyRateMin: z.number().optional(),
  builderMonthlyRateMax: z.number().optional(),
  isFullTimeRetainer: z.boolean().optional(),
  collectMonthlyRate: z.boolean().optional(),
  readyForReview: z.boolean().optional(),
  isNiche: z.boolean().optional(),
  budgetSettings: MissionRoleBudgetSettingsSchema,
});

// Define the main MissionData schema
export const updateMissionDataSchema = z.object({
  shortCompanyDescription: z.string().optional(),
  companyStory: z.string().optional(),
  title: z.string(),
  description: z.string(),
  status: MissionStatusSchema.optional(),
  applyStatus: missionApplyStatusSchema.optional(),
  internalDescription: z.string().optional(),
  expectedDurationMonths: z.number().optional(),
  attachedLinks: z.array(MissionLinkSchema).nullable().optional(),
  logoURL: z.string().optional(),
  videoURL: z.string().optional(),
  roles: z.array(MissionRoleDataSchema),
  hidden: z.boolean().optional(),
  skipContracts: z.boolean().optional(),
  promotedTags: z.array(z.string()).optional(),
  billingPeriod: MissionBillingPeriodSchema.optional(),
  automaticInvoicingPeriod: MissionInvoicingPeriodSchema.nullable().optional(),
  clientMargin: z.number().optional(),
  rolesMargin: z.number().optional(),
  publisher: UserUsernameSchema.optional(),
  managers: z.array(MissionManagerDataSchema).optional(),
  invoiceEmailGreeting: z.string().optional(),
  invoicing: MissionInvoicingSchema.optional(),
  mainManagerUsername: z.string().optional(),
  talentIndustryIds: z.array(TalentIndustryIdSchema).optional(),
  website: z.string().optional(),
  missionSpecId: z.string().optional(),
  owner: UserUsernameSchema.optional(),
  bdOwners: z.array(UserIdSchema).optional(),
  internalMission: z.boolean().optional(),
  accountId: AccountIdSchema.optional(),
  billingInfo: ClientBillingInfoSchema.optional(),
  paymentTerms: BillingPaymentTermsSchema.optional(),
  hubspotDealId: z.string().optional(),
});

export type UpdateMissionData = z.infer<typeof updateMissionDataSchema>;
