import { err } from 'neverthrow';

export type CodedError<T> = { code: T; isUnexpectedError: false } | { code: T; isUnexpectedError: true; originalError: Error };
type CodedErrorOptions = { isUnexpectedError: false } | { isUnexpectedError: true; originalError?: Error };

export class Errors {
  public static createError = <T>(errorCode: T, options: CodedErrorOptions = { isUnexpectedError: false }) => {
    if (options.isUnexpectedError) {
      return err<never, CodedError<T>>({
        code: errorCode,
        isUnexpectedError: true,
        originalError: options.originalError ?? new Error(String(errorCode)),
      });
    }

    return err<never, CodedError<T>>({
      code: errorCode,
      isUnexpectedError: false,
    });
  };
}
