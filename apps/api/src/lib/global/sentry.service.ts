import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import * as Sentry from '@sentry/node';

import { ConfigService } from '@config/config.service';

export type SentryErrorContext = Record<string, unknown>;

@Injectable()
export class SentryService {
  constructor(configService: ConfigService) {
    const env = configService.get('ENV');

    Sentry.init({
      dsn: configService.get('SENTRY_DSN'),
      environment: env,
      enabled: env === 'production',
      tracesSampleRate: 1.0,
    });
  }

  captureError(error: Error, context: SentryErrorContext = {}) {
    Sentry.captureException(error, {
      extra: context,
    });
  }

  logAndCaptureError(message: string, error: Error, sentryContext: SentryErrorContext = {}): void {
    Logger.error(message, error);
    this.captureError(error, sentryContext);
  }
}
