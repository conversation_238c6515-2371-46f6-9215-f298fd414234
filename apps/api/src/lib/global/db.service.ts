import { ATeamPrismaClient } from '@a_team/prisma';
import { Injectable, OnModuleDestroy, OnModuleInit } from '@nestjs/common';

import { ConfigService } from '@config/config.service';

export abstract class CoreDbService extends ATeamPrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor(connectionUrl: string) {
    super(connectionUrl);
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }
}

@Injectable()
export class DbService extends CoreDbService implements OnModuleInit, OnModuleDestroy {
  constructor(configService: ConfigService) {
    super(configService.get('MONGODB_URL'));
  }

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }
}
