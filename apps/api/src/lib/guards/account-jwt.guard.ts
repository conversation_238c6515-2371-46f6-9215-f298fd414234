import { Cache } from '@nestjs/cache-manager';
import { Injectable } from '@nestjs/common';
import { ExecutionContext, Logger } from '@nestjs/common';

import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';

import { AuthenticatedRequest, JwtGuard } from './jwt.guard';

export type AccountData = {
  id: string;
};

export type AuthenticatedAccountRequest = AuthenticatedRequest & { account: AccountData };

@Injectable()
export class AccountJwtGuard extends JwtGuard {
  private readonly ACCOUNT_JWT_SECRET: string;

  constructor(configService: ConfigService, commonUsersRepository: CommonUsersRepository, cacheManager: Cache) {
    super(configService, commonUsersRepository, cacheManager);

    this.ACCOUNT_JWT_SECRET = configService.get('ACCOUNT_JWT_SECRET');
  }

  async canActivate(context: ExecutionContext) {
    const superCanActivate = await super.canActivate(context);

    if (!superCanActivate) {
      return false;
    }

    const request = context.switchToHttp().getRequest<AuthenticatedAccountRequest>();
    const accountTokenHeader = request.headers['x-account-token'] as string;

    if (!accountTokenHeader) {
      Logger.error('[lib/guards/account-jwt.guard/canActivate] - No account token found in the request');
      return false;
    }

    const accountIdAsTokenResult = this.safeJwtVerify(accountTokenHeader, this.ACCOUNT_JWT_SECRET);

    if (accountIdAsTokenResult.isErr()) {
      Logger.error('[lib/guards/account-jwt.guard/canActivate] - Error verifying account token', accountIdAsTokenResult.error);
      return false;
    }

    request.account = { id: accountIdAsTokenResult.value as unknown as string };
    return true;
  }
}
