import { CanActivate, ExecutionContext, RawBodyRequest } from '@nestjs/common';
import * as crypto from 'crypto';
import { Request } from 'express';
import { Observable } from 'rxjs';

export abstract class WebhookSignatureGuard implements CanActivate {
  private readonly SHARED_KEY: string;

  constructor(key: string) {
    this.SHARED_KEY = key;
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest<RawBodyRequest<Request>>();

    const body = request.rawBody ?? '';
    const query = request.query;

    const hmac = crypto.createHmac('sha256', this.SHARED_KEY);
    hmac.update(body);

    const signature = hmac.digest('hex');

    return signature === query.signature;
  }
}
