import { ExecutionContext, Injectable, Logger } from '@nestjs/common';

import { AuthenticatedRequest, JwtGuard, JwtPayload } from './jwt.guard';

export type AdminJwtPayload = JwtPayload & {
  isAdmin: boolean;
};

export type AuthenticatedAdminRequest = AuthenticatedRequest;

@Injectable()
export class AdminJwtGuard extends JwtGuard {
  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<AuthenticatedAdminRequest>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      Logger.error('[lib/guards/admin-jwt.guard/canActivate] - No admin token found in the request');
      return false;
    }

    const payloadResult = this.safeJwtVerify(token, this.JWT_SECRET);

    if (payloadResult.isErr()) {
      Logger.error('[lib/guards/admin-jwt.guard/canActivate] - Error verifying admin token', payloadResult.error);
      return false;
    }

    const payload = payloadResult.value as unknown as AdminJwtPayload;

    const userId = payload.uid;

    if (!payload.isAdmin) {
      Logger.error(`[lib/guards/admin-jwt.guard/canActivate] - User with id: ${userId} is not an admin`);
      return false;
    }

    const tokenValue = payload.tokv;

    request.user = { id: userId };

    const isJwtRevoked = await this.getAndCacheJwtRevokedStatus(userId, tokenValue);

    if (isJwtRevoked) {
      Logger.error('[lib/guards/admin-jwt.guard/canActivate] - Admin token is revoked');
      return false;
    }

    return true;
  }
}
