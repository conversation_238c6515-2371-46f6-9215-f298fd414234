import { Modu<PERSON> } from '@nestjs/common';

import { CommonUsersModule } from '@common/users/users.module';

import { AccountJwtGuard } from './account-jwt.guard';
import { AdminJwtGuard } from './admin-jwt.guard';
import { ApiKeyAuthGuard } from './api-key.guard';
import { JwtGuard } from './jwt.guard';
import { PandadocWebhookGuard } from './pandadoc-webhook.guard';

@Module({
  imports: [CommonUsersModule],
  providers: [JwtGuard, AdminJwtGuard, AccountJwtGuard, ApiKeyAuthGuard, PandadocWebhookGuard],
  exports: [JwtGuard, AdminJwtGuard, AccountJwtGuard, ApiKeyAuthGuard, PandadocWebhookGuard],
})
export class GuardsModule {}
