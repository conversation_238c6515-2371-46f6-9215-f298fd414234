import { Cache } from '@nestjs/cache-manager';
import { CanActivate, Injectable } from '@nestjs/common';
import { ExecutionContext, Logger } from '@nestjs/common';
import { Request } from 'express';
import * as jwt from 'jsonwebtoken';
import { Result } from 'neverthrow';

import { CommonUsersRepository } from '@common/users/users.repository';
import { ConfigService } from '@config/config.service';

export type JwtPayload = {
  uid: string;
  tokv: number;
};

export type AuthenticatedUser = {
  id: string;
};

export type AuthenticatedRequest = Request & { user: AuthenticatedUser };

@Injectable()
export class JwtGuard implements CanActivate {
  protected readonly JWT_REVOKED_CACHE_TTL_MS: number = 10000;

  protected readonly JWT_SECRET: string;

  protected readonly safeJwtVerify = Result.fromThrowable(jwt.verify, (error) => (error instanceof Error ? error : new Error(String(error))));

  /**
   * Helper function useed for getting the jwt revoked cache key.
   *
   * @param  {string} userId - The user id
   * @returns string - The jwt revoked cache key
   */
  private getJwtRevokedCacheKey = (userId: string): string => `jwt-revoked-${userId}`;

  /**
   * Helper function used for getting the jwt revoked value from cache.
   *
   * @param  {string} userId - The user id
   * @returns Promise - Result delegated back as the jwt revoked value or undefined
   */
  private getJwtRevokedFromCache = async (userId: string): Promise<boolean | undefined> =>
    await this.cacheManager.get<boolean>(this.getJwtRevokedCacheKey(userId));

  /**
   * Helper function used for setting the jwt revoked value in the cache.
   *
   * @param  {string} userId - The user id
   * @param  {boolean} status - The value to be set
   */
  private setJwtRevokedCache = async (userId: string, status: boolean) =>
    await this.cacheManager.set(this.getJwtRevokedCacheKey(userId), status, this.JWT_REVOKED_CACHE_TTL_MS);

  constructor(
    configService: ConfigService,
    private readonly commonUserRepository: CommonUsersRepository,
    private readonly cacheManager: Cache
  ) {
    this.JWT_SECRET = configService.get('JWT_SECRET');
  }

  /**
   * Helper function used for getting the jwt revoked value for a specific token & user.
   * Returns `true` if the jwt token is revoked, otherwise `false`. Function reads from the
   * cache or caches the new computed value for a pre-defined period.
   *
   * @param  {string} userId - The user id
   * @param  {number} tokenVersion - The token version
   * @returns Promise - Result delegated back as the boolean indicating if the jwt is revoked.
   */
  protected getAndCacheJwtRevokedStatus = async (userId: string, tokenVersion: number): Promise<boolean> => {
    const cachedRevokedValue = await this.getJwtRevokedFromCache(userId);

    if (cachedRevokedValue) {
      return cachedRevokedValue;
    }

    const userTokenVersion = await this.commonUserRepository.findUserTokenVersion(userId);

    if (!userTokenVersion) {
      await this.setJwtRevokedCache(userId, false);
      return false;
    }

    const isJwtRevoked = tokenVersion !== userTokenVersion;
    await this.setJwtRevokedCache(userId, isJwtRevoked);

    return isJwtRevoked;
  };

  /**
   * Helper function sued for extracting the token from the auth header.
   *
   * @param  {Request} request - The request
   * @returns string - The token found or undefined
   */
  protected extractTokenFromHeader = (request: Request): string | undefined => {
    const authHeader = request.headers.authorization;

    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.slice(7, authHeader.length);
    }

    return undefined;
  };

  async canActivate(context: ExecutionContext) {
    const request = context.switchToHttp().getRequest<AuthenticatedRequest>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      Logger.error('[lib/guards/jwt.guard/canActivate] - No token found in the request');
      return false;
    }

    const payloadResult = this.safeJwtVerify(token, this.JWT_SECRET);

    if (payloadResult.isErr()) {
      Logger.error('[lib/guards/jwt.guard/canActivate] - Error verifying token', payloadResult.error);
      return false;
    }

    const payload = payloadResult.value as unknown as JwtPayload;

    const userId = payload.uid;
    const tokenValue = payload.tokv;

    request.user = { id: userId };

    const isJwtRevoked = await this.getAndCacheJwtRevokedStatus(userId, tokenValue);

    if (isJwtRevoked) {
      Logger.error('[lib/guards/jwt.guard/canActivate] - Token is revoked');
      return false;
    }

    return true;
  }
}
