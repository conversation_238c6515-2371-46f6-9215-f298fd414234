import { CanActivate, ExecutionContext, Injectable, RawBodyRequest } from '@nestjs/common';
import { Request } from 'express';
import { Observable } from 'rxjs';

import { ConfigService } from '@config/config.service';

@Injectable()
export class ApiKeyAuthGuard implements CanActivate {
  private readonly apiKeys: string[];

  constructor(configService: ConfigService) {
    this.apiKeys = configService.get('API_KEYS');
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest<RawBodyRequest<Request>>();
    const apiKeyHeader = request.headers['x-api-key'];

    return typeof apiKeyHeader === 'string' && this.apiKeys.includes(apiKeyHeader);
  }
}
