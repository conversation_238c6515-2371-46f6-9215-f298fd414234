import tailwindcssTypography from '@tailwindcss/typography';
import type { Config } from 'tailwindcss';
import tailwindcssAnimate from 'tailwindcss-animate';

const config: Config = {
  darkMode: ['class'],
  content: ['./src/**/*.tsx'],
  presets: [
    {
      theme: {},
      plugins: [tailwindcssAnimate],
    },
  ],
  plugins: [tailwindcssAnimate, tailwindcssTypography],
  theme: {
    screens: {
      xs: '375px',
      sm: '768px',
      md: '1200px',
      lg: '1600px',
      xl: '2000px',
    },
    extend: {
      fontFamily: {
        inter: ['var(--font-inter)'],
        jakarta: ['var(--font-plus-jakarta-sans)'],
      },
      borderRadius: {
        xs: 'calc(var(--radius) - 4px)',
        sm: 'calc(var(--radius) - 2px)',
        md: 'var(--radius)',
        lg: 'calc(var(--radius) + 4px)',
        xl: 'calc(var(--radius) + 8px)',
        '2xl': 'calc(var(--radius) + 16px)',
        '3xl': 'calc(var(--radius) + 24px)',
        '4xl': 'calc(var(--radius) + 32px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
        purple: {
          100: '#FDFAFF',
          200: '#F9F2FF',
          300: '#F2E3FF',
          400: '#CB94FF',
          500: '#A64AFF',
          600: '#8B17FF',
          700: '#7000E3',
          800: '#5E00BA',
          900: '#460087',
        },
        gray: {
          100: '#FFFFFF',
          200: '#FAFCFF',
          300: '#F5F7FA',
          400: '#D9DADB',
          500: '#909091',
          600: '#686869',
          700: '#414142',
          800: '#2B2B2B',
          900: '#0A0A0A',
        },
        orange: {
          100: '#FFFCFA',
          200: '#FFF8F2',
          300: '#FFF1E3',
          400: '#FFC894',
          500: '#FFA959',
          600: '#FF8C21',
          700: '#FF7A00',
          800: '#EB7100',
          900: '#944700',
        },
        green: {
          100: '#F9FFF5',
          200: '#ECFFE0',
          300: '#DAFFC4',
          400: '#B7FC8E',
          500: '#94EC61',
          600: '#6DDB2C',
          700: '#62C728',
          800: '#4C991F',
          900: '#326614',
        },
        red: {
          100: '#FFF7F8',
          200: '#FFE5E8',
          300: '#FFC4CA',
          400: '#FF9FA7',
          500: '#FF7B86',
          600: '#FF505F',
          700: '#F63041',
          800: '#D60012',
          900: '#98000D',
        },
        pink: {
          100: '#FFFAFD',
          200: '#FFE8F7',
          300: '#FFC8EC',
          400: '#FFA3E0',
          500: '#FF71CE',
          600: '#FF4AC1',
          700: '#FF0FAD',
          800: '#D8008E',
          900: '#9C0067',
        },
        blue: {
          100: '#F7FCFF',
          200: '#DEF3FF',
          300: '#BAE6FF',
          400: '#86D3FF',
          500: '#58C2FF',
          600: '#31AEF6',
          700: '#009EFA',
          800: '#0086D4',
          900: '#005A8D',
        },
      },
      backgroundImage: {
        'builder-card': 'url(./proposals/images/builder-card-bg.svg)',
      },
      width: {
        '30': '120px',
      },
      height: {
        '30': '120px',
      },
      size: {
        '30': '120px',
      },
    },
  },
};

export default config;
