import { Share2Icon } from 'lucide-react';
import { toast } from 'sonner';

import { useGetMe } from '@modules/auth/rq/queries';

import { useIsMobile } from '@lib/hooks/use-is-mobile';

import { Button } from '@shared-components';

import { useRejectProposal } from '../rq/mutations';
import { useGetProposal } from '../rq/queries';
import ApproveShareProposalButton from './approve-share-proposal';
import { useProposalContext } from './context';
import { EditModeToggle } from './edit-mode-toggle';
import { PublicModeToggle } from './public-mode-toggle';

type ProposalActionsProps = {
  proposalId: string;
};

const ApproveProposalButton = ({ proposalId }: ProposalActionsProps) => {
  return (
    <ApproveShareProposalButton proposalId={proposalId}>
      <Button variant="success" name="Approve Proposal">
        Approve proposal
      </Button>
    </ApproveShareProposalButton>
  );
};

const RejectProposalButton = ({ proposalId }: ProposalActionsProps) => {
  const { mutate: rejectProposal, isPending } = useRejectProposal();

  const handleRejectProposal = () => {
    rejectProposal(proposalId, {
      onSuccess: () => toast.success('Proposal was rejected'),
      onError: () => toast.error('Failed to reject proposal'),
    });
  };

  return (
    <Button variant="destructive" name="Reject Proposal" onClick={handleRejectProposal} loading={isPending}>
      Hide
    </Button>
  );
};

const ShareProposalButton = ({ proposalId }: ProposalActionsProps) => {
  return (
    <ApproveShareProposalButton proposalId={proposalId}>
      <Button
        variant="outline"
        size="icon"
        name="Share Proposal"
        className="flex size-8 items-center gap-2 rounded-md border border-purple-400 p-2 sm:size-12 sm:rounded-xl sm:p-4"
      >
        <Share2Icon className="size-4" />
      </Button>
    </ApproveShareProposalButton>
  );
};
export const ProposalActions = ({ proposalId }: ProposalActionsProps) => {
  const { data: proposal } = useGetProposal(proposalId);
  const { data: me } = useGetMe();
  const isMobile = useIsMobile();
  const { inEditMode } = useProposalContext();

  const isAdmin = me?.isAdmin;
  const status = proposal?.status;

  if (!isAdmin) {
    return null;
  }

  const showApproveAndRejectButtons = status === 'pending' && !inEditMode;
  const showOtherAdminActions = status !== 'pending';

  return (
    <>
      <div className="fixed left-0 right-0 top-0 z-10 flex items-center justify-between gap-2 bg-white py-3 pr-4 shadow-inner sm:static sm:justify-start sm:bg-transparent sm:px-4 sm:shadow-none">
        {showApproveAndRejectButtons && (
          <>
            <ApproveProposalButton proposalId={proposalId} />
            <RejectProposalButton proposalId={proposalId} />
          </>
        )}
        {showOtherAdminActions && <EditModeToggle />}
        {showOtherAdminActions && !isMobile && <PublicModeToggle proposalId={proposalId} />}
        {showOtherAdminActions && <ShareProposalButton proposalId={proposalId} />}
      </div>
      {showOtherAdminActions && isMobile && (
        <div className="fixed bottom-0 left-0 right-0 z-10 flex items-center justify-center bg-white p-3">
          <EditModeToggle />
          <PublicModeToggle proposalId={proposalId} />
        </div>
      )}
    </>
  );
};

export default ProposalActions;
