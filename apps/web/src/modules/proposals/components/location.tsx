import { useMemo, useState } from 'react';

import { CONTINENTS, COUNTRIES, COUNTRIES_CODE_NAME_MAP, GROUPED_COUNTRIES } from '@lib/constants/countries';

import { Button } from '@shared-components';

const MAX_LOCATIONS_TO_SHOW = 3;

/**
 * Helper function to get the locations from the country codes.
 * If all countries from the continent are included, it will include the continent name.
 * Otherwise, it will include the remaining country names.
 *
 * @param    {string[]} countryCodes - The country codes to get the locations for
 * @returns  {string[]} - The locations result
 */
const getLocations = (countryCodes: string[]) => {
  const continents: string[] = [];
  let remainingCountryCodes: string[] = [...countryCodes];

  Object.entries(GROUPED_COUNTRIES).forEach(([continent, countries]) => {
    const allContinentCountriesIncluded = countries.every((country) => countryCodes.includes(country.code));
    if (allContinentCountriesIncluded) {
      continents.push(continent);
      const continentCountryCodes = countries.map((country) => country.code);
      remainingCountryCodes = remainingCountryCodes.filter((code) => !continentCountryCodes.includes(code));
    }
  });

  return [
    ...continents.map((c) => CONTINENTS[c as keyof typeof CONTINENTS]),
    ...remainingCountryCodes.map((c) => COUNTRIES_CODE_NAME_MAP[c as keyof typeof COUNTRIES_CODE_NAME_MAP]),
  ];
};

const Location = ({ locationCodes }: { locationCodes: string[] }) => {
  const [showMore, setShowMore] = useState(false);

  const locations = useMemo(() => getLocations(locationCodes), [locationCodes]);

  if (locationCodes.length === 0 || locationCodes.length === COUNTRIES.length) {
    return 'Worldwide';
  }

  const locationsToShow = showMore ? locations : locations.slice(0, MAX_LOCATIONS_TO_SHOW);

  if (locations.length > MAX_LOCATIONS_TO_SHOW) {
    return (
      <>
        {locationsToShow.join(', ')}{' '}
        <Button variant="link" className="h-auto p-0 text-base text-purple-600 hover:no-underline" onClick={() => setShowMore(!showMore)}>
          {showMore ? 'Close' : `+${locations.length - MAX_LOCATIONS_TO_SHOW} more`}
        </Button>
      </>
    );
  }

  return locations.join(', ');
};

export default Location;
