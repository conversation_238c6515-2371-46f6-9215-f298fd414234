import { useEffect } from 'react';

import RoleTabs from '@modules/proposals/components/role-tabs';
import { TabRole } from '@modules/proposals/helpers/types';

import { useProposalContext } from './context';
import ProposalActions from './proposal-actions';

type Props = {
  proposalId: string;
  roles: TabRole[];
};

const ProposalHeader = (props: Props) => {
  const { proposalId, roles } = props;

  const { selectedRoleId, setSelectedRoleId } = useProposalContext();

  useEffect(() => {
    if (roles.length > 0) {
      setSelectedRoleId(roles[0].id);
    }
  }, [roles]);

  const handleRoleClick = (id: string) => {
    setSelectedRoleId(id);
  };

  return (
    <div className="mb-11 flex flex-col px-4 sm:mb-10 sm:flex-row sm:items-start sm:justify-between sm:gap-4 sm:px-20">
      <div className="order-1 my-6 sm:order-2 sm:my-0">
        <ProposalActions proposalId={proposalId} />
      </div>
      <div className="order-2 sm:order-1">
        <h2 className="mb-6 max-w-[600px] text-3xl font-extrabold leading-9 sm:text-[40px] sm:leading-[48px]">
          We've curated builders who match your requirements.
        </h2>
        <RoleTabs roles={roles} selectedRoleId={selectedRoleId} onSelectRole={handleRoleClick} />
      </div>
    </div>
  );
};

export default ProposalHeader;
