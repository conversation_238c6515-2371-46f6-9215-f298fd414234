import React, { createContext, ReactNode, useContext, useState } from 'react';

type ProposalContextType = {
  proposalId: string;
  missionId: string;
  accountId: string;
  selectedRoleId?: string;
  setSelectedRoleId: (id: string) => void;
  inEditMode: boolean;
  setInEditMode: (inEditMode: boolean) => void;
};

const ProposalContext = createContext<ProposalContextType>({} as ProposalContextType);

export const ProposalsContextProvider: React.FC<{ proposalId: string; missionId: string; accountId: string; children: ReactNode }> = ({
  proposalId,
  missionId,
  accountId,
  children,
}) => {
  const [selectedRoleId, setSelectedRoleId] = useState<string>();
  const [inEditMode, setInEditMode] = useState(false);

  return (
    <ProposalContext.Provider value={{ proposalId, missionId, accountId, selectedRoleId, setSelectedRoleId, inEditMode, setInEditMode }}>
      {children}
    </ProposalContext.Provider>
  );
};

export const useProposalContext = (): ProposalContextType => {
  const context = useContext(ProposalContext);

  if (!context) {
    throw new Error('useProposalContext must be used within a ProposalsContextProvider');
  }

  return context;
};
