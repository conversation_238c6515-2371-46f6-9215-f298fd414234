import { zodResolver } from '@hookform/resolvers/zod';
import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { useInviteMissionCollaborator } from '@modules/contracts/rq/mutations';

import {
  ResponsiveDialog,
  ResponsiveDialogDescription,
  ResponsiveDialogContent,
  ResponsiveDialogTitle,
  ResponsiveDialogFooter,
  ResponsiveDialogTrigger,
  FormField,
  Input,
  FormControl,
  FormItem,
  FormMessage,
  FormLabel,
  SelectItem,
  SelectContent,
  Select,
  SelectTrigger,
  SelectValue,
  Checkbox,
} from '@shared-components';
import { Button } from '@shared-components';

const inviteMissionCollaboratorFormSchema = z.object({
  fullName: z
    .string()
    .regex(/^\w+( \w+)+$/, 'Please enter a valid full name')
    .min(1, 'Full name is required'),
  email: z.string().email('Please enter a valid email address'),
  role: z.enum(['missionadmin', 'missionmember'], { message: 'Role is required' }),
  skipEmailInvite: z.boolean(),
});

type InviteMissionCollaboratorForm = z.infer<typeof inviteMissionCollaboratorFormSchema>;

type InviteMissionCollaboratorButtonProps = {
  missionId: string;
  children: React.ReactNode;
};

const roleTypeOptions = [
  { value: 'missionadmin', label: 'Mission admin', description: 'Can edit, delete, sign agreements, see invoices and invite new team members.' },
  { value: 'missionmember', label: 'Mission member', description: 'Can view the mission without rates and invite new team members.' },
];

const InviteMissionCollaboratorButton = ({ missionId, children }: InviteMissionCollaboratorButtonProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const form = useForm<InviteMissionCollaboratorForm>({
    resolver: zodResolver(inviteMissionCollaboratorFormSchema),
    defaultValues: {
      skipEmailInvite: false,
    },
  });

  const { mutate: inviteMissionCollaborator, isPending } = useInviteMissionCollaborator();

  const handleInviteMissionCollaborator = () => {
    inviteMissionCollaborator(
      {
        params: {
          missionId,
        },
        body: {
          fullName: form.getValues('fullName'),
          email: form.getValues('email'),
          role: form.getValues('role'),
          skipEmail: form.getValues('skipEmailInvite'),
        },
      },
      {
        onSuccess: () => {
          setIsDialogOpen(false);
          form.reset();
          toast.success('Collaborator was added');
        },
        onError: () => toast.error('Failed to add collaborator'),
      }
    );
  };

  return (
    <ResponsiveDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <ResponsiveDialogTrigger asChild>{children}</ResponsiveDialogTrigger>
      <ResponsiveDialogContent className="max-h-[calc(100vh-64px)] overflow-auto p-6">
        <FormProvider {...form}>
          <form onSubmit={form.handleSubmit(handleInviteMissionCollaborator)}>
            <div className="mb-6">
              <ResponsiveDialogTitle className="text-2xl font-extrabold">Invite new mission collaborator</ResponsiveDialogTitle>
              <ResponsiveDialogDescription className="mt-1 text-sm">
                You can invite people you work with to collaborate on your mission.
              </ResponsiveDialogDescription>
            </div>
            <div className="flex flex-col gap-6">
              <div className="flex gap-2">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Full name</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Jane Smith..." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel required>Email address</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="<EMAIL>..." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem key={field.value} className="w-full">
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormLabel required>Role</FormLabel>
                      <FormControl>
                        <SelectTrigger className="h-10 w-full border border-gray-400">
                          <SelectValue placeholder="Select role">{roleTypeOptions.find((roleType) => roleType.value === field.value)?.label}</SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="w-full">
                        {roleTypeOptions.map((roleType) => (
                          <SelectItem key={roleType.value} value={roleType.value}>
                            <div className="flex flex-col items-start gap-2">
                              <p className="text-sm font-medium">{roleType.label}</p>
                              <p className="text-sm text-gray-500">{roleType.description}</p>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="skipEmailInvite"
                render={({ field }) => (
                  <FormItem className="mb-2 flex flex-row items-center gap-2 space-y-0">
                    <FormControl>
                      <Checkbox checked={field.value as boolean} onCheckedChange={field.onChange} />
                    </FormControl>
                    <FormLabel className="text-sm font-medium">Skip email invite</FormLabel>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <ResponsiveDialogFooter className="mt-6 flex justify-between">
              <Button type="button" variant="link" name="Invite New Mission Collaborator Back" onClick={() => setIsDialogOpen(false)}>
                Back
              </Button>
              <Button name="Invite Mission Collaborator" loading={isPending}>
                Invite mission collaborator
              </Button>
            </ResponsiveDialogFooter>
          </form>
        </FormProvider>
      </ResponsiveDialogContent>
    </ResponsiveDialog>
  );
};

export default InviteMissionCollaboratorButton;
