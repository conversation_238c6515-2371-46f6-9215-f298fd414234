import { ProposalBuilder } from '@packages/contracts';
import { CheckIcon } from 'lucide-react';
import { useEffect, useState } from 'react';

import { useGetAccountTeamAdvisorByAccountId } from '@modules/accounts/rq/queries';
import { useGetMe } from '@modules/auth/rq/queries';

import { SIGN_IN_URL, CREATE_ACCOUNT_URL, INTERVIEW_SCHEDULE_TABLE_URL } from '@lib/constants/external-urls';

import {
  Button,
  ButtonProps,
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  ResponsiveDialog,
  ResponsiveDialogContent,
  ResponsiveDialogDescription,
  ResponsiveDialogFooter,
  ResponsiveDialogTitle,
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
} from '@shared-components';

import CalComIframe from './calcom-iframe';

type BookInterviewUserData = ProposalBuilder;

type InterviewType = 'interview' | 'discover-interview';

type BookInterviewButtonProps = ButtonProps & {
  user: BookInterviewUserData;
  interviewType: InterviewType;
  roleAndMissionTitle?: string;
  proposalId: string;
  bookingExists: boolean;
  accountId: string;
  onClose?: () => void;
};

export const BookInterviewButton = ({ user, interviewType, roleAndMissionTitle, proposalId, bookingExists, accountId, onClose }: BookInterviewButtonProps) => {
  const [publicGateDialogOpen, setPublicGateDialogOpen] = useState(false);
  const [bookInterviewDialogOpen, setBookInterviewDialogOpen] = useState(false);
  const [talkToTeamAdvisor, setTalkToTeamAdvisor] = useState(false);

  const { data: teamAdvisor } = useGetAccountTeamAdvisorByAccountId(accountId);
  const { data: me } = useGetMe();

  useEffect(() => {
    if (onClose && !bookInterviewDialogOpen) {
      onClose();
    }
  }, [onClose, bookInterviewDialogOpen]);

  const calLink = `/${talkToTeamAdvisor && teamAdvisor ? teamAdvisor.username : user.username}/${interviewType}`;

  return (
    <>
      <div className="flex flex-row gap-0">
        <Button
          className="relative order-1 hover:bg-purple-600 sm:order-2"
          onClick={() => (me ? setBookInterviewDialogOpen(true) : setPublicGateDialogOpen(true))}
        >
          Book an interview
        </Button>
        {bookingExists && (
          <TooltipProvider delayDuration={0}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  asChild
                  className="order-2 -ml-2 flex items-center gap-1 border-purple-600 text-purple-600 hover:text-purple-700 sm:order-1 sm:-mr-2 sm:ml-0"
                >
                  <a href={INTERVIEW_SCHEDULE_TABLE_URL} target="_blank">
                    <CheckIcon className="size-3" /> View booking
                  </a>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Click to see call details, view the video conference URL, reschedule, or cancel.</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
      <p className="mt-2 text-xs font-normal leading-5 text-gray-500 sm:hidden">
        Click “Booked” to see call details, view the video conference URL, reschedule, or cancel.
      </p>
      {me && teamAdvisor && (
        <Dialog open={bookInterviewDialogOpen} onOpenChange={(open) => setBookInterviewDialogOpen(open)}>
          <DialogContent className="max-h-screen overflow-y-auto sm:max-w-[1200px]">
            <DialogHeader>
              <DialogTitle>Book an interview with {talkToTeamAdvisor ? teamAdvisor.firstName : user.firstName}</DialogTitle>
            </DialogHeader>
            <CalComIframe
              key={calLink}
              calLink={calLink}
              config={{
                layout: 'month_view',
                name: `${me.firstName} ${me.lastName}`,
                email: me.email,
                emailFrom: `A.Team <${teamAdvisor.email}>`,
                emailCC: `${teamAdvisor.email}`,
                proposalId,
                roleAndMissionTitle: roleAndMissionTitle ?? '-',
              }}
            />
            <DialogFooter className="justify-start sm:justify-center">
              {!talkToTeamAdvisor && (
                <div className="flex items-center gap-1 text-sm">
                  None of these times work?
                  <Button variant="link" className="p-0" onClick={() => setTalkToTeamAdvisor(true)}>
                    Talk to {teamAdvisor.firstName}
                  </Button>
                </div>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
      {!me && (
        <ResponsiveDialog open={publicGateDialogOpen} onOpenChange={(open) => setPublicGateDialogOpen(open)}>
          <ResponsiveDialogContent>
            <div className="p-6">
              <ResponsiveDialogTitle className="mb-1">Book an interview with {user.firstName}</ResponsiveDialogTitle>
              <ResponsiveDialogDescription>
                If you want to book an interview with a selected builder, please sign in or create an account.
              </ResponsiveDialogDescription>
            </div>
            <ResponsiveDialogFooter className="flex flex-row justify-end gap-2 border-t border-t-gray-300 px-6 py-4">
              <Button variant="secondary" asChild>
                <a href={SIGN_IN_URL} target="_blank">
                  Sign in
                </a>
              </Button>
              <Button asChild>
                <a href={CREATE_ACCOUNT_URL} target="_blank">
                  Create an account
                </a>
              </Button>
            </ResponsiveDialogFooter>
          </ResponsiveDialogContent>
        </ResponsiveDialog>
      )}
    </>
  );
};
