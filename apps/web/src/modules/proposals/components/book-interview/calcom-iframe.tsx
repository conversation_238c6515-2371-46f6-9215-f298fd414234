import Cal, { getCal<PERSON><PERSON> } from '@calcom/embed-react';
import { FC, useEffect } from 'react';

import { config } from '@config/index';

type InterviewBookingStatus = 'ACCEPTED' | 'PENDING' | 'CANCELLED';

type InterviewBooking = {
  id: number;
  userId: number;
  description: string;
  uid: string;
  startTime: string;
  endTime: string;
  status: InterviewBookingStatus;
  recurringEventId: string | null;
};

type CalComIframeProps = {
  calLink: string;
  onConfirm?: (booking: InterviewBooking) => void;
  config?: Record<string, string>;
  onIframeLoad?: () => void;
};

const CalComIframe: FC<CalComIframeProps> = ({ calLink, onConfirm, onIframeLoad, config: calcomConfig }) => {
  const calComURL = config.NEXT_PUBLIC_CALCOM_URL;

  useEffect(() => {
    let confirmed = false;
    let isSubscribed = true;

    void (async () => {
      const cal = await getCalApi();

      cal('ui', {
        theme: 'light',
        styles: { branding: { brandColor: '#7000E3' } },
        hideEventTypeDetails: false,
        layout: 'month_view',
      });

      cal('on', {
        action: 'bookingSuccessful',
        callback: (e) => {
          if (!isSubscribed) {
            return;
          }

          const { data, type } = e.detail as { type: string; data: { booking: InterviewBooking } };
          const booking = data.booking;

          if (onConfirm && !confirmed && type === 'bookingSuccessful' && (booking.status === 'ACCEPTED' || booking.status === 'PENDING')) {
            onConfirm(booking);
            confirmed = true;
          }
        },
      });

      if (onIframeLoad) {
        cal('on', {
          action: '__windowLoadComplete',
          callback: onIframeLoad,
        });
      }
    })();

    return () => {
      isSubscribed = false;
    };
  }, []);

  return (
    <Cal
      calLink={calLink}
      config={calcomConfig}
      style={{ width: '100%', height: '100%', overflow: 'scroll' }}
      calOrigin={calComURL}
      embedJsUrl={`${calComURL}/embed/embed.js`}
    />
  );
};

export default CalComIframe;
