import { cn } from '@lib/utils';

import { Label, Separator, Switch } from '@shared-components';

import { useProposalContext } from './context';

export const EditModeToggle = ({ className }: { className?: string }) => {
  const { inEditMode, setInEditMode } = useProposalContext();

  return (
    <div className={cn('flex items-center gap-2 rounded-[20px] px-4 py-3 sm:border sm:border-purple-400', className)}>
      <Switch checked={inEditMode} onCheckedChange={setInEditMode} id="proposal-edit-mode" />
      <Label htmlFor="proposal-edit-mode" className="whitespace-nowrap font-medium text-black">
        Edit Mode
      </Label>
      <Separator orientation="vertical" className="h-2" />
      <span className="whitespace-nowrap text-sm text-gray-600">Admin only</span>
    </div>
  );
};
