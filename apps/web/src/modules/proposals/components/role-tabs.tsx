import RoleTab from '@modules/proposals/components/role-tab';
import { TabRole } from '@modules/proposals/helpers/types';

type Props = {
  roles: TabRole[];
  selectedRoleId?: string;
  onSelectRole: (id: string) => void;
};

const RoleTabs = (props: Props) => {
  const { roles, selectedRoleId, onSelectRole } = props;
  return (
    <div className="flex flex-wrap items-start gap-8">
      {roles.map((role) => (
        <RoleTab key={role.id} role={role} selected={selectedRoleId === role.id} onSelectRole={onSelectRole} />
      ))}
    </div>
  );
};

export default RoleTabs;
