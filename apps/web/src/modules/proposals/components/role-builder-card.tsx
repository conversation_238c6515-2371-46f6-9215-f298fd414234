import { ProposalBuilder } from '@packages/contracts';
import { GlobeIcon, ZapIcon } from 'lucide-react';

import RoleBuilderCardSection, { RoleBuilderCardSectionSkeleton } from '@modules/proposals/components/role-builder-card-section';

import { CLIENT_APP_BASE_URL } from '@lib/constants/external-urls';
import { formatCurrency } from '@lib/helpers/currency';

import { Avatar, AvatarFallback, AvatarImage, Badge, LogoIcon, Separator, Skeleton } from '@shared-components';

import GithubIcon from './icons/github-icon';
import LinkedInIcon from './icons/linkedin-icon';
import ResumeIcon from './icons/resume-icon';

import { useUpdateProposalCandidateSection } from '../rq/mutations';
import { useRefetchBuildersForProposal } from '../rq/queries';
import { BookInterviewButton } from './book-interview/book-interview-button';
import { BuilderRecommendationsModal } from './builder-recommendations-modal';
import { useProposalContext } from './context';
import { MonthlyRateBreakdown } from './monthly-rate-breakdown';
import { PortfolioLink } from './portfolio-link';

const MAX_SKILLS_TO_SHOW = 5;

export const RoleBuilderCardSkeleton = () => {
  return (
    <div className="rounded-xl bg-gradient-to-b from-purple-100 to-white">
      <div>
        <div className="overflow-hidden rounded-t-xl bg-gradient-to-b from-gray-200 to-white">
          <div className="bg-builder-card flex gap-4 bg-right-top bg-no-repeat p-10 pt-6">
            <Skeleton className="size-28 rounded-lg" />
            <div className="flex flex-col justify-between">
              <div>
                <Skeleton className="mb-2 h-6 w-36" />
                <div className="flex max-w-lg flex-wrap gap-2 text-sm text-black">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-20" />
                  </div>
                </div>
              </div>
              <div className="flex gap-4 text-sm font-medium text-black">
                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-100 bg-white p-10">
          <div className="flex flex-wrap justify-between gap-8">
            <RoleBuilderCardSectionSkeleton />
            <RoleBuilderCardSectionSkeleton />
          </div>
          <div className="mt-8 max-w-lg border-t border-gray-100 pt-8">
            <RoleBuilderCardSectionSkeleton />
          </div>
        </div>
      </div>
    </div>
  );
};

type RoleBuilderCardProps = {
  builder: ProposalBuilder;
};

const RoleBuilderCard: React.FC<RoleBuilderCardProps> = ({ builder }) => {
  const { proposalId, accountId } = useProposalContext();

  const { mutate: updateProposalCandidateSection } = useUpdateProposalCandidateSection(proposalId, builder.id);
  const refetchBuildersForProposal = useRefetchBuildersForProposal(proposalId);

  const builderMetaData = [
    {
      text: `${builder.location?.city && `${builder.location?.city}, `}${builder.location?.country}`,
      isShown: !!builder.location,
    },
    {
      text: `${builder.yearsOfExperience} years exp`,
      isShown: !!builder.yearsOfExperience,
    },
    {
      text: `Available ${builder.availableHoursPerWeek}hr per week`,
      isShown: !!builder.availableHoursPerWeek,
    },
    {
      text: `${builder.hoursWorked} hours billed`,
      isShown: !!builder.hoursWorked,
    },
    {
      text: `${formatCurrency(builder.clientHourlyRate ?? 0)} per hour`,
      isShown: !!builder.clientHourlyRate,
    },
    {
      text: (
        <>
          {formatCurrency(builder.clientMonthlyRate ?? 0)} per month <MonthlyRateBreakdown monthlyRate={builder.clientMonthlyRate ?? 0} />
        </>
      ),
      isShown: !!builder.clientMonthlyRate,
    },
  ];

  const builderLinks = [
    {
      text: 'Profile',
      // TODO: Update to new URL once available
      url: `${CLIENT_APP_BASE_URL}/builders/${builder.id}`,
      icon: <LogoIcon className="size-4" />,
    },
    {
      text: 'Resume',
      url: builder.cvUrl,
      icon: <ResumeIcon className="size-4" />,
    },
    {
      text: 'GitHub',
      url: builder.websites?.githubUrl,
      icon: <GithubIcon className="size-4" />,
    },
    {
      text: 'LinkedIn',
      url: builder.websites?.linkedInUrl,
      icon: <LinkedInIcon className="size-4" />,
    },
    {
      text: 'Website',
      url: builder.websites?.personalWebsite,
      icon: <GlobeIcon className="size-4" />,
    },
  ];

  const displayedMetadata = builderMetaData.filter((data) => data.isShown);
  const displayedLinks = builderLinks.filter((link) => link.url);

  if (!builder) {
    return null;
  }

  const { cardSections, portfolio } = builder;

  return (
    <div>
      <div>
        <div className="overflow-hidden rounded-t-xl bg-gradient-to-b from-purple-200 to-white">
          <div className="bg-builder-card flex flex-col gap-4 bg-right-top bg-no-repeat p-4 sm:flex-row sm:p-10 sm:pt-6">
            <Avatar className="sm:size-30 size-14 rounded-3xl">
              <AvatarImage src={builder.pictureURL ?? undefined} />
              <AvatarFallback className="sm:size-30 size-14 rounded-lg bg-gray-100">
                <ZapIcon className="size-6 text-gray-600" />
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col justify-between">
              <div>
                <div className="text-lg font-medium">
                  {builder.firstName} {builder.lastName}
                </div>

                <div className="flex max-w-lg flex-wrap gap-2 text-sm text-black">
                  {displayedMetadata.map((data, i) => (
                    <div className="flex items-center gap-2 whitespace-nowrap [&:nth-child(3)]:basis-1/3" key={i}>
                      {data.text} {i !== 2 && i !== displayedMetadata.length - 1 && <span className="inline-block h-2 w-px bg-gray-400" />}
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-4 pt-3 text-sm font-medium text-black">
                {displayedLinks.map((link, i) => (
                  <a href={link.url!} target="_blank" key={i} className="flex cursor-pointer items-center gap-2">
                    {link.icon} {link.text} {i !== displayedLinks.length - 1 && <span className="ml-2 inline-block h-2 w-px bg-gray-400" />}
                  </a>
                ))}
                {portfolio && (
                  <>
                    <span className="inline-block h-2 w-px bg-gray-400" />
                    <PortfolioLink portfolio={portfolio} />
                  </>
                )}
              </div>
            </div>
            <div className="mt-2 sm:ml-auto sm:mt-0">
              <BookInterviewButton
                user={builder}
                interviewType="interview"
                proposalId={proposalId}
                bookingExists={builder.everHadClientInterview ?? false}
                accountId={accountId}
                onClose={refetchBuildersForProposal}
              />
            </div>
          </div>
        </div>
        <div className="rounded-lg border border-gray-400/40 bg-white p-4 sm:p-10">
          <div className="grid grid-cols-1 items-start sm:grid-cols-2 sm:gap-8">
            {cardSections?.requirements && (
              <RoleBuilderCardSection
                title={cardSections.requirements.title ?? undefined}
                text={cardSections.requirements.text ?? undefined}
                onUpdate={(text) => updateProposalCandidateSection({ section: 'requirements', text, title: cardSections.requirements.title })}
              >
                <div className="mt-5 flex flex-wrap gap-2">
                  {builder.skills.slice(0, MAX_SKILLS_TO_SHOW).map((skill) => (
                    <Badge key={skill.id} variant="secondary" className="whitespace-nowrap bg-gray-300 px-2.5 py-0.5 text-sm font-normal">
                      {skill.name}
                    </Badge>
                  ))}
                </div>
              </RoleBuilderCardSection>
            )}
            <Separator className="my-4 bg-gray-400/40 sm:hidden" />
            <div className="flex flex-col items-start justify-start gap-2 text-sm">
              {cardSections?.experience && (
                <RoleBuilderCardSection
                  title={cardSections.experience.title ?? undefined}
                  text={cardSections.experience.text ?? undefined}
                  onUpdate={(text) => updateProposalCandidateSection({ section: 'experience', text, title: cardSections.experience.title })}
                />
              )}
              <BuilderRecommendationsModal userId={builder.id} recommendationsCount={builder.builderRecommendationsCount} />
            </div>
          </div>
          {builder.customQuestion?.questionText && builder.customQuestion?.replyText && (
            <>
              <Separator className="my-4 bg-gray-400/40 sm:my-8" />
              <div className="max-w-lg">
                <RoleBuilderCardSection title={builder.customQuestion.questionText} text={builder.customQuestion.replyText} />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default RoleBuilderCard;
