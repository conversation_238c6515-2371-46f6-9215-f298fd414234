import { ProposalBuilderPortfolioDto } from '@packages/contracts';
import { GlobeIcon, LockIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { useGetMe } from '@modules/auth/rq/queries';

import { Button, DrawerTooltip, Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@shared-components';

export const PortfolioLink = ({ portfolio }: { portfolio: ProposalBuilderPortfolioDto }) => {
  const [isCopied, setIsCopied] = useState(false);
  const { data: me } = useGetMe();

  const { url, hasPassword, password } = portfolio;

  const onPasswordCopy = () => {
    if (password) {
      void navigator.clipboard.writeText(password);
      toast.success('Copied to the clipboard');
      setIsCopied(true);
    }
  };

  const copyButton = (
    <Button
      name="Proposal Portfolio Copy Password"
      variant="secondary"
      className="h-6 rounded-sm bg-gray-400 px-2 text-xs font-normal hover:bg-gray-400 [&_svg]:size-3"
      onClick={onPasswordCopy}
    >
      <LockIcon />
      {isCopied ? 'Copied' : 'Copy pass'}
    </Button>
  );

  return (
    <div className="flex items-center gap-2">
      <a href={url} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-sm font-medium">
        <GlobeIcon className="size-4" />
        Portfolio
      </a>
      {password && (
        <TooltipProvider delayDuration={0}>
          <Tooltip>
            <TooltipTrigger asChild>{copyButton}</TooltipTrigger>
            <TooltipContent>Copy to the clipboard</TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
      {hasPassword && !me && <DrawerTooltip trigger={copyButton}>Sign in to access the portfolio password.</DrawerTooltip>}
    </div>
  );
};
