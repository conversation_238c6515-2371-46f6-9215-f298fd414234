import { addDays, formatDate } from 'date-fns';
import { InfoIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';

import { cn } from '@lib/utils';

import { DrawerTooltip, Label, Switch } from '@shared-components';

import { useSetProposalPublicUntil } from '../rq/mutations';
import { useGetProposal } from '../rq/queries';

export const PublicModeToggle = ({ proposalId, className }: { proposalId: string; className?: string }) => {
  const { data: proposal } = useGetProposal(proposalId);
  const { mutate: setProposalPublicUntil } = useSetProposalPublicUntil();

  const [publicUntil, setPublicUntil] = useState<Date | null>(null);
  const dateIn30Days = addDays(new Date(), 30);

  useEffect(() => {
    if (proposal) {
      setPublicUntil(proposal.publicUntil ?? null);
    }
  }, [proposal]);

  const handleToggle = () => {
    const newPublicUntilValue = publicUntil ? null : dateIn30Days;
    setProposalPublicUntil(
      { proposalId, publicUntil: newPublicUntilValue },
      {
        onSuccess: () => {
          setPublicUntil(newPublicUntilValue);

          if (newPublicUntilValue) {
            void navigator.clipboard.writeText(window.location.href);
            toast.success('Proposal URL has been copied to the clipboard');
          } else {
            toast.success('Proposal has been switched to private mode');
          }
        },
        onError: () => {
          toast.error('Failed to set proposal public until date');
        },
      }
    );
  };

  return (
    <div className={cn('flex items-center gap-2 rounded-[20px] px-4 py-3 sm:border sm:border-purple-400', className)}>
      <Switch checked={!!publicUntil} onCheckedChange={handleToggle} id="proposal-public-until" />
      <Label htmlFor="proposal-public-until" className="whitespace-nowrap font-medium text-black">
        Public until {formatDate(publicUntil ?? dateIn30Days, 'MMM d, yyyy')}
      </Label>
      <DrawerTooltip trigger={<InfoIcon className="size-4" />}>
        <p>Anyone with the link will be able to view this proposal for 30 days until {formatDate(publicUntil ?? dateIn30Days, 'MMM d, yyyy')}.</p>
      </DrawerTooltip>
    </div>
  );
};
