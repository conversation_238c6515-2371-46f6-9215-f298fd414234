const LinkedInIcon: React.FC<React.SVGProps<SVGAElement>> = ({ className }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" className={className}>
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M14.4 8.95717V13.6H11.6565V9.26811C11.6565 8.17991 11.2597 7.43726 10.2667 7.43726C9.50861 7.43726 9.05749 7.93776 8.85902 8.4219C8.78665 8.59498 8.76802 8.83588 8.76802 9.07816V13.6H6.02364C6.02364 13.6 6.0606 6.26322 6.02364 5.50308H8.76775V6.65078C8.76219 6.65937 8.75495 6.66862 8.74972 6.67694H8.76775V6.65078C9.13233 6.09987 9.78344 5.31286 11.2408 5.31286C13.0464 5.31283 14.4 6.47011 14.4 8.95717ZM3.15291 1.59998C2.21406 1.59998 1.59998 2.20415 1.59998 2.99865C1.59998 3.77585 2.19629 4.39836 3.11648 4.39836H3.13481C4.09184 4.39836 4.68703 3.77597 4.68703 2.99865C4.669 2.20415 4.09184 1.59998 3.15291 1.59998ZM1.76297 13.6H4.5063V5.50308H1.76297V13.6Z"
        fill="black"
      />
    </svg>
  );
};

export default LinkedInIcon;
