const GithubIcon: React.FC<React.SVGProps<SVGAElement>> = ({ className }) => {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path
        d="M9.99921 14.6673V12.0007C10.092 11.1655 9.8525 10.3274 9.33254 9.66732C11.3325 9.66732 13.3325 8.33398 13.3325 6.00065C13.3859 5.16732 13.1525 4.34732 12.6659 3.66732C12.8525 2.90065 12.8525 2.10065 12.6659 1.33398C12.6659 1.33398 11.9992 1.33398 10.6659 2.33398C8.90588 2.00065 7.09254 2.00065 5.33254 2.33398C3.99921 1.33398 3.33254 1.33398 3.33254 1.33398C3.13254 2.10065 3.13254 2.90065 3.33254 3.66732C2.84713 4.34457 2.61153 5.16918 2.66588 6.00065C2.66588 8.33398 4.66588 9.66732 6.66588 9.66732C6.40588 9.99398 6.21254 10.3673 6.09921 10.7673C5.98588 11.1673 5.95254 11.5873 5.99921 12.0007V14.6673"
        stroke="black"
        stroke-width="1.4"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.00065 11.9993C2.99398 13.3327 2.66732 10.666 1.33398 10.666"
        stroke="black"
        stroke-width="1.4"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default GithubIcon;
