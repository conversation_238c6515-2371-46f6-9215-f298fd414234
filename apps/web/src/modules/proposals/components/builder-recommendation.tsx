import { RecommendationItemData } from '@packages/contracts';
import { format } from 'date-fns';
import { DotIcon } from 'lucide-react';
import React, { useMemo } from 'react';

import { Avatar, AvatarFallback, AvatarImage, Separator, Skeleton } from '@shared-components';

type BuilderRecommendationProps = {
  review: RecommendationItemData;
};

export const BuilderRecommendationSkeleton = () => {
  return (
    <div className="flex items-start gap-6 p-4">
      <div>
        <Skeleton className="size-12 rounded-xl" />
      </div>
      <div className="w-full">
        <Skeleton className="mb-1 h-7 w-64" />
        <div className="mb-4 flex items-center gap-2">
          <Skeleton className="h-4 w-32" />
          <Separator className="h-2 w-px" />
          <Skeleton className="h-4 w-24" />
          <Separator className="h-2 w-px" />
          <Skeleton className="h-4 w-36" />
        </div>
        <Skeleton className="h-20 w-full" />
      </div>
    </div>
  );
};

export const BuilderRecommendation: React.FC<BuilderRecommendationProps> = ({ review }) => {
  if (review.source === 'linkedin') {
    return (
      <div className="flex flex-col items-start gap-6 p-4 sm:flex-row">
        <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center overflow-hidden rounded-lg bg-gray-200">
          <Avatar className="size-12 rounded-xl">
            <AvatarImage src={review.companyLogo} />
            <AvatarFallback>{review.companyName}</AvatarFallback>
          </Avatar>
        </div>
        <div className="flex-1">
          <h2 className="text-xl font-medium text-gray-900">
            <strong className="mr-1">in</strong> LinkedIn recommendation from {review.recommenderName}
          </h2>
          <p className="mt-0.5 flex items-center gap-1 text-sm text-gray-500">
            {review.title} {review.companyName && `at ${review.companyName ?? review.occupation}`} <DotIcon className="text-gray-500" size={16} /> Published on{' '}
            {format(review.createdAt, 'PPP')}
          </p>
          <p className="mt-3 text-base leading-relaxed text-gray-500">{review.recommendationText}</p>
        </div>
      </div>
    );
  }

  // Client review
  const details = useMemo(() => {
    const data: string[] = [];
    if (review.role?.name) {
      data.push(`Worked as a ${review.role.name}`);
    }
    if (review.company?.industry) {
      data.push(review.company.industry);
    }
    if (review.role?.startDate && review.role?.endDate) {
      data.push(`${format(review.role.startDate, 'MMM yyyy')} - ${format(review.role.endDate, 'MMM yyyy')}`);
    }
    return data;
  }, [review]);

  return (
    <div className="flex flex-col items-start gap-6 p-4 sm:flex-row">
      <div>
        <Avatar className="size-12 rounded-xl">
          <AvatarImage src={review.company?.logo} />
          <AvatarFallback>{review.company?.name}</AvatarFallback>
        </Avatar>
      </div>
      <div>
        <div className="mb-1 text-xl font-medium text-gray-900">Recommendation from {review.company?.name}</div>
        <div className="mb-4 flex flex-wrap items-center gap-1">
          {details.map((detail, index) => (
            <React.Fragment key={detail}>
              <div className="whitespace-nowrap text-sm text-gray-500">{detail}</div>
              {index < details.length - 1 && <DotIcon className="text-gray-500" size={16} />}
            </React.Fragment>
          ))}
        </div>
        <p className="text-base/7 text-gray-500">{review.review}</p>
      </div>
    </div>
  );
};

export default BuilderRecommendation;
