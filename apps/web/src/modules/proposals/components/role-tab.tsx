import { Zap } from 'lucide-react';

import { TabRole } from '@modules/proposals/helpers/types';

import { pluralize } from '@lib/helpers/pluralize';
import { cn } from '@lib/utils';

import { Avatar, AvatarFallback, AvatarImage } from '@shared-components';

type Props = {
  role: TabRole;
  selected?: boolean;
  onSelectRole: (id: string) => void;
  maxBuildersDisplayed?: number;
};

const RoleTab = (props: Props) => {
  const { role, selected, onSelectRole, maxBuildersDisplayed = 2 } = props;
  const displayedBuilders = role.builders.slice(0, maxBuildersDisplayed);
  const remainingBuilders = role.builders.length - maxBuildersDisplayed;
  return (
    <div onClick={() => onSelectRole(role.id)} className="max-w-30 flex h-[82px] cursor-pointer flex-col items-start justify-between">
      <div
        className={cn('mb-2 flex rounded-xl p-1.5', selected ? 'border-2 border-purple-600' : 'border border-purple-400', remainingBuilders > 0 ? 'pr-0' : '')}
      >
        {displayedBuilders.map((builder) => (
          <div key={builder.id}>
            <Avatar className="relative -left-2 size-10 rounded-lg border-2 border-white first:left-0">
              <AvatarImage src={builder.pictureURL} />
              <AvatarFallback className="size-10 rounded-lg bg-gray-100">
                <Zap className="size-4 text-gray-600" />
              </AvatarFallback>
            </Avatar>
          </div>
        ))}
        {remainingBuilders > 0 && (
          <div className="relative -left-4 flex size-10 items-center justify-center rounded-lg border-2 border-white bg-white text-xs font-medium">
            + {remainingBuilders}{' '}
          </div>
        )}
      </div>
      <div className={cn('max-w-40 truncate text-center text-xs', selected ? 'font-semibold text-black' : 'mt-px font-normal text-gray-500')}>
        {pluralize(role.builders.length, role.title)}
      </div>
    </div>
  );
};

export default RoleTab;
