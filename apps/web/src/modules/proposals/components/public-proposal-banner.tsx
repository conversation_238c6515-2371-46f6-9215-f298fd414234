import { GetProposalResponseDto } from '@packages/contracts';
import { ArrowRightIcon, ZapIcon } from 'lucide-react';

import { SIGN_IN_URL, CREATE_ACCOUNT_URL, LEARN_MORE_URL } from '@lib/constants/external-urls';

import { Avatar, LogoIcon, AvatarFallback, AvatarImage, Button } from '@shared-components';

const ATEAM_PITCH_TEXT = 'A.Team is where growing companies discover, build & manage elite tech teams, on-demand.';
const MAX_BUILDERS_TO_SHOW = 4;

export const PublicProposalBanner = ({ proposal }: { proposal: GetProposalResponseDto }) => {
  const shownBuilders = proposal.roles
    .map((role) => role.builders)
    .flat()
    .slice(0, MAX_BUILDERS_TO_SHOW);

  const sharedByFullName = proposal.sharedBy ? `${proposal.sharedBy.firstName} ${proposal.sharedBy.lastName}` : 'Team advisor';

  return (
    <div className="fixed bottom-0 left-0 right-0 sm:mx-auto sm:my-5 sm:max-w-4xl">
      <div className="flex flex-col gap-4 rounded-t-xl bg-[linear-gradient(270deg,_#000_0%,_#26014C_46.87%)] p-6 text-white sm:px-6 sm:py-4">
        <div className="flex items-center gap-4">
          <div className="flex size-8 items-center justify-center rounded-sm bg-black sm:size-14 sm:rounded-xl">
            <LogoIcon className="size-4 text-white sm:size-6" singleColor />
          </div>
          <div className="hidden sm:block">
            <div className="text-base text-white">{sharedByFullName} shared a proposal with you.</div>
            <div className="text-sm text-gray-200">Join A.Team to book interviews, discover talent and build your team.</div>
          </div>
          <div className="ml-auto flex gap-2">
            <Button className="h-8 bg-purple-950 sm:h-10" name="Public Proposal Sign In" asChild>
              <a href={SIGN_IN_URL} target="_blank">
                Sign in
              </a>
            </Button>
            <Button className="h-8 bg-purple-700 sm:h-10" name="Public Proposal Create Account" asChild>
              <a href={CREATE_ACCOUNT_URL} target="_blank">
                Create an account
              </a>
            </Button>
          </div>
        </div>
        <div className="text-sm sm:hidden">{sharedByFullName} shared a proposal with you.</div>
      </div>
      <div className="flex flex-col gap-4 bg-black p-6 text-gray-300 sm:rounded-b-xl sm:px-6 sm:py-4">
        <div className="flex items-center justify-between">
          <div className="flex">
            {shownBuilders.map((builder) => (
              <Avatar key={builder.id} className="-ml-1 size-6 rounded-md border-2 border-black first:ml-0">
                <AvatarImage src={builder.pictureURL ?? undefined} />
                <AvatarFallback className="size-6 rounded-lg bg-gray-100">
                  <ZapIcon className="size-4 text-gray-600" />
                </AvatarFallback>
              </Avatar>
            ))}
          </div>
          <p className="hidden text-sm sm:block">{ATEAM_PITCH_TEXT}</p>
          <Button variant="link" className="flex items-center gap-1 pr-0 text-sm text-white hover:no-underline" name="Public Proposal Learn More" asChild>
            <a href={LEARN_MORE_URL} target="_blank">
              Learn more <ArrowRightIcon className="size-4" />
            </a>
          </Button>
        </div>
        <p className="text-sm sm:hidden">{ATEAM_PITCH_TEXT}</p>
      </div>
    </div>
  );
};
