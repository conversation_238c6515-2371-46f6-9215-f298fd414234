import { GetProposalResponseDto } from '@packages/contracts';
import { useMemo } from 'react';

import { useGetMe } from '@modules/auth/rq/queries';
import { useProposalContext } from '@modules/proposals/components/context';
import ProposalHeader from '@modules/proposals/components/header';
import RoleDetails, { RoleDetailsSkeleton } from '@modules/proposals/components/role-details';
import { transformProposalRoleToTabRole } from '@modules/proposals/helpers/transformer';
import { useGetProposalBuilders } from '@modules/proposals/rq/queries';

import { cn } from '@lib/utils';

import { PublicProposalBanner } from './public-proposal-banner';

type ProposalViewProps = {
  proposal: GetProposalResponseDto;
};

const ProposalView = (props: ProposalViewProps) => {
  const { proposal } = props;
  const { selectedRoleId } = useProposalContext();
  const { data: me, isPending: isPendingMe } = useGetMe();

  const roles = useMemo(() => {
    return proposal.roles.map(transformProposalRoleToTabRole);
  }, [proposal.roles]);

  const selectedRole = roles.find((role) => role?.id === selectedRoleId);
  const builderIds = selectedRole?.builders.map((builder) => builder.id);
  const { data: builders = [], isPending } = useGetProposalBuilders(proposal.id, builderIds ?? []);

  const publicView = !me && !isPendingMe;

  return (
    <div className={cn('z-10 mx-auto max-w-screen-lg bg-white px-0 pb-10 pt-4 sm:px-10 sm:py-10', publicView && 'pb-64 sm:pb-52')}>
      <ProposalHeader proposalId={proposal.id} roles={roles} />
      {isPending && <RoleDetailsSkeleton />}
      {!isPending && selectedRole && <RoleDetails role={selectedRole} builders={builders} teamAdvisor={proposal.teamAdvisor} />}
      {publicView && <PublicProposalBanner proposal={proposal} />}
    </div>
  );
};

export default ProposalView;
