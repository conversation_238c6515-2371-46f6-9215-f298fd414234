import { ProposalBuilder } from '@packages/contracts';

import RoleBuilderCard, { RoleBuilderCardSkeleton } from '@modules/proposals/components/role-builder-card';

import { pluralize } from '@lib/helpers/pluralize';

import { Skeleton } from '@shared-components';

export const RoleBuilderCardListSkeleton = () => {
  return (
    <div>
      <Skeleton className="mb-4 text-2xl font-bold" />
      <div className="flex flex-col gap-4">
        <>
          <RoleBuilderCardSkeleton />
          <RoleBuilderCardSkeleton />
          <RoleBuilderCardSkeleton />
          <RoleBuilderCardSkeleton />
        </>
      </div>
    </div>
  );
};

type Props = {
  title: string;
  builders: ProposalBuilder[];
};

const RoleBuilderCardList = (props: Props) => {
  const { builders, title } = props;
  return (
    <div>
      <h2 className="mb-4 text-2xl font-bold">{pluralize(builders.length, title)}</h2>
      <div className="flex flex-col gap-4">
        {builders.map((builder) => (
          <RoleBuilderCard key={builder.id} builder={builder} />
        ))}
      </div>
    </div>
  );
};

export default RoleBuilderCardList;
