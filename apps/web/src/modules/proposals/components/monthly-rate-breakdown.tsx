import { InfoIcon } from 'lucide-react';

import { formatCurrency } from '@lib/helpers/currency';
import { cn } from '@lib/utils';

import { DrawerTooltip, Separator } from '@shared-components';

type MonthlyRateBreakdownProps = {
  monthlyRate: number;
};

const HOURS_PER_WEEK = 40;
const AVERAGE_WEEKS_IN_A_MONTH = 4.33;

const LabelValue = ({ label, value, className }: { label: string; value: string; className?: string }) => {
  return (
    <div className={cn('gap-y-0.75 flex justify-between gap-x-10 text-xs font-normal', className)}>
      <span>{label}</span>
      <span>{value}</span>
    </div>
  );
};

export const MonthlyRateBreakdown = ({ monthlyRate }: MonthlyRateBreakdownProps) => {
  return (
    <DrawerTooltip
      trigger={<InfoIcon size={16} className="text-gray-600" />}
      className="bg-white p-6 text-black sm:max-w-72 sm:bg-gray-700 sm:px-3 sm:py-2 sm:text-white"
    >
      <div className="mb-2 text-sm font-medium">Monthly rate calculation</div>
      <LabelValue label="Hourly rate" value={`${formatCurrency(monthlyRate / HOURS_PER_WEEK / AVERAGE_WEEKS_IN_A_MONTH)}/h`} />
      <LabelValue label="Hours per week" value={`x ${HOURS_PER_WEEK}`} />
      <LabelValue label="Average weeks in a month" value={`x ${AVERAGE_WEEKS_IN_A_MONTH}`} />
      <Separator className="my-2 bg-gray-300 sm:bg-gray-600" />
      <LabelValue label="Monthly rate" value={`${formatCurrency(monthlyRate)}/mo`} className="font-medium" />
    </DrawerTooltip>
  );
};
