import { LongText, Skeleton } from '@shared-components';

import EditableText from './editable-text';

export const RoleBuilderCardSectionSkeleton = () => {
  return (
    <div className="flex flex-1 flex-col gap-2">
      <Skeleton className="mb-2 h-6 w-44" />
      <Skeleton className="mb-2 h-20 w-full" />
    </div>
  );
};

type Props = {
  title?: string;
  text?: string;
  children?: React.ReactNode;
  onUpdate?: (text: string) => void;
};

const RoleBuilderCardSection = (props: Props) => {
  const { title, children, text, onUpdate } = props;

  return (
    <div className="flex w-full flex-1 flex-col gap-2">
      {title && <div className="text-xs uppercase text-gray-500">{title}</div>}
      <div className="sm:w-[80%]">
        {text &&
          (onUpdate ? <EditableText initialText={text} onUpdate={onUpdate} maxLinesToShow={3} /> : <LongText text={text} lines={3} className="text-sm" />)}
      </div>
      {children}
    </div>
  );
};

export default RoleBuilderCardSection;
