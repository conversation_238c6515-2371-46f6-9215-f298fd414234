import { useState } from 'react';
import { toast } from 'sonner';

import { useAdminGetAccountCollaboratorsByAccountId, useAdminGetAccountTeamAdvisorByAccountId } from '@modules/accounts/rq/queries';

import {
  ResponsiveDialog,
  ResponsiveDialogDescription,
  ResponsiveDialogContent,
  ResponsiveDialogTitle,
  ResponsiveDialogFooter,
  ResponsiveDialogTrigger,
  Checkbox,
} from '@shared-components';
import { Button } from '@shared-components';

import { useApproveProposal } from '../rq/mutations';
import { useProposalContext } from './context';
import InviteMissionCollaboratorButton from './invite-mission-collaborator';

type ApproveShareProposalButtonProps = {
  proposalId: string;
  children: React.ReactNode;
};

const ApproveShareProposalButton = ({ proposalId, children }: ApproveShareProposalButtonProps) => {
  const { accountId, missionId } = useProposalContext();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedCollaboratorsIds, setSelectedCollaboratorsIds] = useState<string[]>([]);

  const { data: teamAdvisor } = useAdminGetAccountTeamAdvisorByAccountId(accountId);
  const { data: allCollaborators = [] } = useAdminGetAccountCollaboratorsByAccountId(accountId);
  const collaborators = allCollaborators.filter(({ id }) => id !== teamAdvisor?.id);

  const { mutate: approveProposal, isPending } = useApproveProposal();

  const handleApproveProposal = () => {
    approveProposal(
      { proposalId, collaboratorIds: [] },
      {
        onSuccess: () => toast.success('Proposal was approved'),
        onError: () => toast.error('Failed to approve proposal'),
        onSettled: () => setIsDialogOpen(false),
      }
    );
  };

  const onCollaboratorCheckboxSwitch = (collaboratorId: string) => {
    if (selectedCollaboratorsIds.includes(collaboratorId)) {
      setSelectedCollaboratorsIds((prev) => prev.filter((id) => id !== collaboratorId));
    } else {
      setSelectedCollaboratorsIds((prev) => [...prev, collaboratorId]);
    }
  };

  return (
    <ResponsiveDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <ResponsiveDialogTrigger asChild>{children}</ResponsiveDialogTrigger>
      <ResponsiveDialogContent className="max-h-[calc(100vh-64px)] overflow-auto p-6">
        <div className="mb-6">
          <ResponsiveDialogTitle className="text-2xl font-extrabold">Send team proposal email</ResponsiveDialogTitle>
          <ResponsiveDialogDescription className="mt-1 text-sm">
            Select the recipients that should receive an email for this proposal.
          </ResponsiveDialogDescription>
        </div>
        <div className="flex flex-col gap-6">
          <div>
            <div className="mb-3 text-xs font-medium uppercase">Team advisor</div>
            <div className="flex items-center gap-2 rounded-lg border border-gray-500 p-4">
              {teamAdvisor && (
                <>
                  <span className="text-sm font-medium">
                    {teamAdvisor.firstName} {teamAdvisor.lastName}
                  </span>
                  <span className="text-sm text-gray-500">({teamAdvisor.email})</span>
                </>
              )}
            </div>
          </div>
          <div>
            <div className="mb-3 text-xs font-medium uppercase">Mission collaborators ({collaborators.length})</div>
            <div className="flex flex-col gap-2">
              {collaborators.map((collaborator) => (
                <label key={collaborator.id} className="flex items-start gap-2 rounded-lg border border-gray-500 px-4 py-3">
                  <Checkbox
                    checked={selectedCollaboratorsIds.includes(collaborator.id)}
                    onCheckedChange={() => onCollaboratorCheckboxSwitch(collaborator.id)}
                  />
                  <div className="flex flex-col">
                    <span className="text-sm font-medium">
                      {collaborator.firstName} {collaborator.lastName}
                    </span>
                    <span className="text-sm text-gray-500">({collaborator.email})</span>
                  </div>
                </label>
              ))}
            </div>
          </div>
        </div>
        <ResponsiveDialogFooter className="mt-6 flex justify-between">
          <InviteMissionCollaboratorButton missionId={missionId}>
            <Button variant="link" name="Invite New Mission Collaborator">
              Invite new mission collaborator
            </Button>
          </InviteMissionCollaboratorButton>
          <Button name="Share Proposal" onClick={handleApproveProposal} loading={isPending}>
            Share proposal {selectedCollaboratorsIds.length > 0 ? `(${selectedCollaboratorsIds.length})` : ''}
          </Button>
        </ResponsiveDialogFooter>
      </ResponsiveDialogContent>
    </ResponsiveDialog>
  );
};

export default ApproveShareProposalButton;
