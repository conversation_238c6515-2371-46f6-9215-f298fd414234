import { ProposalRole } from '@packages/contracts';

import { TabRole } from './types';

export const transformProposalRoleToTabRole = (role: ProposalRole): TabRole => {
  return {
    id: role.id,
    builders: role.builders,
    title: role.title,
    aboutBuildersSection: role.aboutBuildersSection,
    availability: role.weeklyAvailability,
    locations: role.locations,
    skills: role.requiredSkills.map((skill) => skill.name),
    description: role.description,
  };
};
