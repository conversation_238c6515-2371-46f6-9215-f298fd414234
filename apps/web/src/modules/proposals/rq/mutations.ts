import { UpdateProposalCandidateSectionBody } from '@packages/contracts';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useUpdateProposalCandidateSection = (proposalId: string, userId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: queryKeys.proposals.getProposalBuilders(proposalId) });
    },
    onError: () => {
      toast.error('Failed to update proposal section');
    },
    mutationFn: async (body: UpdateProposalCandidateSectionBody) => {
      const response = await api.adminProposals.updateProposalCandidateSection.mutate({
        params: { proposalId, userId },
        body,
        headers: {},
      });

      if (response.status !== 200) {
        throw new Error('Failed to update proposal section');
      }

      return response.body;
    },
  });
};

export const useApproveProposal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ proposalId, collaboratorIds }: { proposalId: string; collaboratorIds: string[] }) =>
      api.adminProposals.approveProposal.mutate({
        params: { proposalId },
        body: { collaboratorIds },
        headers: {},
      }),
    onSuccess: (_, { proposalId }) => {
      void queryClient.invalidateQueries({ queryKey: queryKeys.proposals.getProposal(proposalId) });
    },
  });
};

export const useRejectProposal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (proposalId: string) =>
      api.adminProposals.rejectProposal.mutate({
        params: { proposalId },
        body: {},
        headers: {},
      }),
    onSuccess: (_, proposalId) => {
      void queryClient.invalidateQueries({ queryKey: queryKeys.proposals.getProposal(proposalId) });
    },
  });
};

export const useSetProposalPublicUntil = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({ proposalId, publicUntil }: { proposalId: string; publicUntil: Date | null }) =>
      api.adminProposals.setProposalPublicUntil.mutate({
        params: { proposalId },
        body: { publicUntil },
        headers: {},
      }),
    onSuccess: (_, { proposalId }) => {
      void queryClient.invalidateQueries({ queryKey: queryKeys.proposals.getProposal(proposalId) });
    },
  });
};
