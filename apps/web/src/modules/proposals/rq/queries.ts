import { useQueryClient } from '@tanstack/react-query';

import api from '@config/api';

import queryKeys from '@lib/query-keys';

const DEFAULT_CLIENT_REVIEWS_PAGE_SIZE = 10;

export const useGetProposal = (id: string) => {
  return api.proposals.getProposal.useQuery({
    queryKey: queryKeys.proposals.getProposal(id),
    queryData: { params: { proposalId: id } },
    select: (data) => data.body,
  });
};

export const useGetProposalBuilders = (proposalId: string, ids: string[]) => {
  const builderIds = ids.join(',');
  return api.proposals.getProposalBuilders.useQuery({
    queryKey: queryKeys.proposals.getProposalBuilders(proposalId, builderIds),
    queryData: { params: { proposalId }, query: { builderIds } },
    select: (data) => data.body,
  });
};

export const useRefetchBuildersForProposal = (proposalId: string) => {
  const queryClient = useQueryClient();
  return () => {
    queryClient.invalidateQueries({ queryKey: ['proposal', 'builders', proposalId] });
  };
};

export const useGetBuilderRecommendations = (proposalId: string, userId: string, enabled = true, pageSize: number = DEFAULT_CLIENT_REVIEWS_PAGE_SIZE) => {
  return api.proposals.getBuilderRecommendations.useInfiniteQuery({
    enabled,
    queryKey: queryKeys.proposals.getBuilderRecommendations(proposalId, userId),
    queryData: ({ pageParam }) => ({
      query: {
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      },
      params: { proposalId, userId },
    }),
    initialPageParam: { page: 0, pageSize },
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.body.recommendations.length >= pageSize ? { pageSize, page: allPages.length } : undefined;
    },
    refetchOnWindowFocus: false,
  });
};
