'use client';

import { useRouter } from 'next/navigation';

import { pluralize } from '@lib/helpers/pluralize';

import {
  Badge,
  Button,
  Skeleton,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Tooltip,
  TooltipContent,
  <PERSON>ltipProvider,
  TooltipTrigger,
} from '@shared-components';

import { CONTRACT_TYPE_NAME_MAP } from '../helpers/constants';

import { useGetContracts } from '../rq/queries';
import DeleteButton from './delete-button';
import DownloadButton from './download-button';
import EditContractSignedAt from './edit-contract-signed-at';
import EditContractTitle from './edit-contract-title';
import EditContractType from './edit-contract-type';
import SignContractButton from './sign-contract-button';
import UploadContract from './upload-contract';

const LoadingState = () => {
  return Array(10)
    .fill(null)
    .map((_, index) => (
      <TableRow key={index}>
        <TableCell>
          <Skeleton className="h-10 w-full" />
        </TableCell>
        <TableCell>
          <Skeleton className="h-10 w-full" />
        </TableCell>
        <TableCell>
          <Skeleton className="h-10 w-full" />
        </TableCell>
        <TableCell>
          <Skeleton className="h-10 w-full" />
        </TableCell>
        <TableCell>
          <Skeleton className="h-10 w-full" />
        </TableCell>
      </TableRow>
    ));
};

const ContractsTable = () => {
  const { data, isFetching, fetchNextPage } = useGetContracts();
  const router = useRouter();

  const contracts = data?.pages.flatMap((page) => page.body.data ?? []) ?? [];
  const totalCount = data?.pages[0]?.body?.count ?? 0;

  return (
    <div className="mx-6 my-10 flex flex-col items-start gap-4">
      <div className="flex w-full items-center justify-between">
        <h4 className="text-2xl font-extrabold leading-7 tracking-tight text-gray-900">{!isFetching && `${totalCount} ${pluralize(totalCount, 'item')}`}</h4>
        <UploadContract />
      </div>
      <div className="w-full rounded-2xl border border-solid border-gray-200 p-6">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-100">
              <TableHead className="rounded-tl-sm">
                <div className="w-full">Contract name</div>
              </TableHead>
              <TableHead className="hidden w-[140px] sm:table-cell">Status</TableHead>
              <TableHead className="hidden w-[180px] sm:table-cell">Signed date</TableHead>
              <TableHead className="hidden w-[240px] sm:table-cell">Type</TableHead>
              <TableHead className="hidden w-[300px] rounded-tr-sm sm:table-cell"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {contracts.map(({ documentTitle, id, isSigned, isDocumentSent, userCanSign, signedAt, type }) => (
              <TableRow key={id} className="group/row">
                <TableCell>
                  <EditContractTitle contractId={id} title={documentTitle ?? CONTRACT_TYPE_NAME_MAP[type]} />
                </TableCell>
                <TableCell className="hidden sm:table-cell">
                  <Badge variant={isSigned ? 'secondary' : 'destructive'}>{isSigned ? 'Signed' : 'Not signed'}</Badge>
                </TableCell>
                <TableCell className="hidden sm:table-cell">{signedAt ? <EditContractSignedAt contractId={id} signedAt={signedAt} /> : ''}</TableCell>
                <TableCell className="hidden sm:table-cell">
                  <EditContractType contractId={id} type={type} />
                </TableCell>
                <TableCell className="hidden sm:table-cell">
                  {isSigned || !userCanSign ? (
                    <>
                      <Button variant="ghost" className="text-gray-500" onClick={() => router.push(`/contracts/${id}`)}>
                        View
                      </Button>
                      <DownloadButton contractId={id} variant="ghost" className="text-gray-500">
                        Download
                      </DownloadButton>
                      <DeleteButton contractId={id} variant="ghost" className="text-gray-500">
                        Delete
                      </DeleteButton>
                    </>
                  ) : (
                    <TooltipProvider delayDuration={0}>
                      <Tooltip>
                        <TooltipTrigger>
                          <SignContractButton contractId={id} variant="ghost" className="text-purple-600" disabled={!isDocumentSent}>
                            Sign contract
                          </SignContractButton>
                        </TooltipTrigger>
                        {!isDocumentSent && <TooltipContent>Contract is not distributed through Pandadoc yet</TooltipContent>}
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </TableCell>
              </TableRow>
            ))}
            {isFetching && <LoadingState />}
          </TableBody>
        </Table>
      </div>
      {(totalCount === undefined || contracts.length < totalCount) && (
        <Button variant="secondary" onClick={() => void fetchNextPage()} disabled={isFetching}>
          Load more
        </Button>
      )}
    </div>
  );
};

export default ContractsTable;
