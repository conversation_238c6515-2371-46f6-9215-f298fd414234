'use client';

import { Download, X } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button, Dialog, DialogContent, DialogHeader, DialogTitle } from '@shared-components';

import { CONTRACT_TYPE_NAME_MAP } from '../helpers/constants';

import { useGetContract } from '../rq/queries';
import DownloadButton from './download-button';

const ContractView = ({ id }: { id: string }) => {
  const { data: contract } = useGetContract(id);
  const router = useRouter();

  if (!contract) {
    return null;
  }

  const documentTitle = contract.documentTitle ?? CONTRACT_TYPE_NAME_MAP[contract.type];

  return (
    <Dialog open>
      <DialogContent className="max-w-screen h-screen grid-rows-[72px_auto] p-0 [&>button]:hidden">
        <DialogHeader className="max-h-18 px-6 py-8 sm:px-10">
          <DialogTitle className="text-left">{documentTitle}</DialogTitle>
          <Button variant="ghost" className="absolute right-4 top-4" onClick={() => router.push('/contracts')}>
            <X />
          </Button>
        </DialogHeader>
        <div className="flex flex-col items-start gap-2 bg-gray-100 px-0 py-4 sm:flex-row sm:gap-10 sm:px-28 sm:py-10">
          <iframe src={contract.downloadURL} className="min-h-full w-full" />
          <div className="order-first min-w-full bg-gray-50 p-6 sm:order-last sm:min-w-[400px] sm:rounded-xl">
            <div className="text-lg">{documentTitle}</div>
            <DownloadButton contractId={id} variant="secondary" className="mt-6 flex">
              <Download /> Download
            </DownloadButton>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ContractView;
