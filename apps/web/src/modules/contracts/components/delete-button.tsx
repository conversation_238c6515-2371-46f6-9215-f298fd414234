import { LoaderCircle } from 'lucide-react';
import { toast } from 'sonner';

import { Button, ButtonProps } from '@shared-components';

import { useDeleteContract } from '../rq/mutations';

type Props = ButtonProps & { contractId: string };

const DeleteButton = ({ contractId, children, ...buttonProps }: Props) => {
  const { mutate: deleteContract, isPending } = useDeleteContract();

  const onClick = async () => {
    deleteContract(
      {
        params: { id: contractId },
      },
      {
        onSuccess: () => toast.success('Contract deleted successfully'),
        onError: () => toast.error('Error deleting contract'),
      }
    );
  };

  return (
    <Button onClick={() => void onClick()} {...buttonProps}>
      {isPending ? <LoaderCircle className="mx-auto animate-spin" /> : children}
    </Button>
  );
};

export default DeleteButton;
