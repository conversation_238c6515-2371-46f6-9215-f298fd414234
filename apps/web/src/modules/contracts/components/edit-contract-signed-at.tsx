import { format } from 'date-fns';
import { PencilIcon, SaveIcon, SquareXIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button, DatePicker } from '@shared-components';

import { useUpdateContract } from '../rq/mutations';

type Props = { contractId: string; signedAt: Date };

const EditContractSignedAt = ({ contractId, signedAt }: Props) => {
  const { mutate: updateContract, isPending } = useUpdateContract();

  const [isEditing, setIsEditing] = useState(false);
  const [newSignedAt, setNewSignedAt] = useState<Date | undefined>(signedAt);

  const onSave = () => {
    updateContract(
      {
        params: { id: contractId },
        body: { signedAt: newSignedAt },
      },
      {
        onSuccess: () => {
          toast.success('Date updated successfully');
          setIsEditing(false);
        },
        onError: () => toast.error('Error updating contract sign date'),
      }
    );
  };

  const onCancel = () => {
    setIsEditing(false);
    setNewSignedAt(signedAt);
  };

  return (
    <span className="group flex items-center">
      {isEditing ? (
        <div className="flex">
          <DatePicker date={newSignedAt} onDateChange={setNewSignedAt} calendarProps={{ toDate: new Date() }} />
          <Button variant="ghost" className="my-0 ml-2 text-gray-600" size="icon" onClick={onSave} loading={isPending}>
            <SaveIcon />
          </Button>
          <Button variant="ghost" className="text-red-600" size="icon" onClick={onCancel}>
            <SquareXIcon />
          </Button>
        </div>
      ) : (
        <>
          {format(signedAt, 'MMM d, y')}
          <Button variant="ghost" className="invisible ml-2 mt-0 text-gray-600 group-hover:visible" size="icon" onClick={() => setIsEditing(true)}>
            <PencilIcon />
          </Button>
        </>
      )}
    </span>
  );
};

export default EditContractSignedAt;
