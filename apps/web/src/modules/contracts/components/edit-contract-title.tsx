import { PencilIcon, SaveIcon, SquareXIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button, Input } from '@shared-components';

import { useUpdateContract } from '../rq/mutations';

type Props = { contractId: string; title: string };

const EditContractTitle = ({ contractId, title }: Props) => {
  const { mutate: updateContract, isPending } = useUpdateContract();

  const [isEditing, setIsEditing] = useState(false);
  const [newTitle, setNewTitle] = useState(title);

  const onSave = () => {
    updateContract(
      {
        params: { id: contractId },
        body: { documentTitle: newTitle },
      },
      {
        onSuccess: () => {
          toast.success('Title updated successfully');
          setIsEditing(false);
        },
        onError: () => toast.error('Error updating contract title'),
      }
    );
  };

  const onCancel = () => {
    setIsEditing(false);
    setNewTitle(title);
  };

  return (
    <span className="group flex items-center">
      {isEditing ? (
        <>
          <Input onChange={(e) => setNewTitle(e.target.value)} className="inline-block max-w-[280px]" placeholder="Contract name" value={newTitle} />
          <Button variant="ghost" className="ml-2 text-gray-600" size="icon" onClick={onSave} loading={isPending} disabled={!newTitle}>
            <SaveIcon />
          </Button>
          <Button variant="ghost" className="text-red-600" size="icon" onClick={onCancel}>
            <SquareXIcon />
          </Button>
        </>
      ) : (
        <>
          {title}
          <Button variant="ghost" className="invisible ml-2 text-gray-600 group-hover:visible" size="icon" onClick={() => setIsEditing(true)}>
            <PencilIcon />
          </Button>
        </>
      )}
    </span>
  );
};

export default EditContractTitle;
