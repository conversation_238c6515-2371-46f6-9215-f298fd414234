import { ContractType } from '@packages/contracts';
import { OutputFileEntry, UploadCtxProvider } from '@uploadcare/react-uploader';
import '@uploadcare/react-uploader/core.css';
import { FileUploaderRegular } from '@uploadcare/react-uploader/next';
import { Upload } from 'lucide-react';
import { useRef, useState } from 'react';
import { toast } from 'sonner';

import { config } from '@config/index';

import { cn } from '@lib/utils';

import { Button, Dialog, DialogContent, DialogHeader, DialogTitle, ToggleGroup, ToggleGroupItem } from '@shared-components';

import { CONTRACT_TYPE_NAME_MAP, USED_CONTRACT_TYPES } from '../helpers/constants';

import { useCreateContracts } from '../rq/mutations';

type FileData = {
  id: string;
  fileName: string;
  url: string;
  type?: ContractType;
};

const UploadContract = () => {
  const uploaderRef = useRef<InstanceType<UploadCtxProvider> | null>(null);
  const [currentFiles, setCurrentFiles] = useState<OutputFileEntry[]>([]);
  const [submittedFiles, setSubmittedFiles] = useState<FileData[]>([]);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);

  const showWidget = () => {
    if (uploaderRef.current) {
      const api = uploaderRef.current.getAPI();
      api.setCurrentActivity('upload-list');
      api.setModalState(true);
    }
  };

  const onDoneClick = () => {
    setSubmittedFiles(
      currentFiles.map((entry) => ({
        id: entry.uuid ?? '',
        fileName: (entry.file as File).name,
        url: entry.cdnUrl ?? '',
      }))
    );

    setSaveDialogOpen(true);
  };

  const saveContractType = (index: number, type: ContractType) => {
    const newArray = [...submittedFiles];
    newArray[index] = { ...newArray[index], type };
    setSubmittedFiles(newArray);
  };

  const saveAndCloseDisabled = submittedFiles.some((fileData) => !fileData.type);

  const { mutate: saveContractsMutation } = useCreateContracts();

  const saveContracts = () => {
    saveContractsMutation(
      { body: submittedFiles as (FileData & { type: ContractType })[] },
      {
        onSuccess: () => {
          setSaveDialogOpen(false);
          setSubmittedFiles([]);
          toast.success('Contracts saved successfully');
        },
      }
    );
  };

  return (
    <>
      <FileUploaderRegular
        apiRef={uploaderRef}
        pubkey={config.NEXT_PUBLIC_UPLOADCARE_API_KEY}
        multiple
        accept=".pdf"
        headless
        onChange={(data) => setCurrentFiles(data.allEntries)}
        onDoneClick={onDoneClick}
      />
      <Button variant="secondary" onClick={showWidget}>
        <Upload /> Upload contract
      </Button>
      <Dialog open={saveDialogOpen}>
        <DialogContent className="min-w-full lg:min-w-[800px]">
          <DialogHeader>
            <DialogTitle className="text-2xl font-extrabold leading-7 tracking-tight text-gray-900">Assign contract types</DialogTitle>
          </DialogHeader>
          <div className="mt-2 w-full rounded-2xl border border-solid border-gray-200 p-6">
            {submittedFiles.map((fileData, index) => (
              <div
                key={fileData.id}
                className={cn(
                  'flex flex-col items-stretch justify-between gap-1 py-5 sm:flex-row sm:items-center',
                  index !== submittedFiles.length - 1 && 'border-b-solid border-b border-b-gray-400'
                )}
              >
                <span className="text-xs">{fileData.fileName}</span>
                <ToggleGroup
                  type="single"
                  variant="outline"
                  className="flex-col sm:flex-row"
                  onValueChange={(value) => saveContractType(index, value as ContractType)}
                >
                  {USED_CONTRACT_TYPES.map((contractType) => (
                    <ToggleGroupItem value={contractType} className="w-full text-xs sm:w-auto">
                      {CONTRACT_TYPE_NAME_MAP[contractType]}
                    </ToggleGroupItem>
                  ))}
                </ToggleGroup>
              </div>
            ))}
            <Button className="mx-auto mt-6 block" disabled={saveAndCloseDisabled} onClick={saveContracts}>
              Save and close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadContract;
