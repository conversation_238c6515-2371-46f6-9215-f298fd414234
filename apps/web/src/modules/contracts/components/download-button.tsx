import { LoaderCircle } from 'lucide-react';
import { toast } from 'sonner';

import { Button, ButtonProps } from '@shared-components';

import { useGetContract } from '../rq/queries';

type Props = ButtonProps & { contractId: string };

const DownloadButton = ({ contractId, children, ...buttonProps }: Props) => {
  const { refetch, isRefetching } = useGetContract(contractId, false);

  const onClick = async () => {
    const contract = await refetch();

    if (!contract.data) {
      toast.error('Failed to load a contract');
      return;
    }

    const downloadURL = contract.data?.downloadURL;

    if (!downloadURL) {
      toast.error('Failed to load a contract');
      return;
    }

    window.open(downloadURL, '_blank');
  };

  return (
    <Button onClick={() => void onClick()} {...buttonProps}>
      {isRefetching ? <LoaderCircle className="mx-auto animate-spin" /> : children}
    </Button>
  );
};

export default DownloadButton;
