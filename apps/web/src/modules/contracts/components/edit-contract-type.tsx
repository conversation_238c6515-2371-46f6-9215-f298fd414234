import { ContractType } from '@packages/contracts';
import { PencilIcon, SaveIcon, SquareXIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared-components';

import { CONTRACT_TYPE_NAME_MAP, USED_CONTRACT_TYPES } from '../helpers/constants';

import { useUpdateContract } from '../rq/mutations';

type Props = { contractId: string; type: ContractType };

const EditContractType = ({ contractId, type }: Props) => {
  const { mutate: updateContract, isPending } = useUpdateContract();

  const [isEditing, setIsEditing] = useState(false);
  const [newType, setNewType] = useState(type);

  const onSave = () => {
    updateContract(
      {
        params: { id: contractId },
        body: { type: newType },
      },
      {
        onSuccess: () => {
          toast.success('Type updated successfully');
          setIsEditing(false);
        },
        onError: () => toast.error('Error updating contract type'),
      }
    );
  };

  const onCancel = () => {
    setIsEditing(false);
    setNewType(type);
  };

  return (
    <span className="group flex items-center">
      {isEditing ? (
        <>
          <Select value={newType} onValueChange={(value) => setNewType(value as ContractType)}>
            <SelectTrigger className="-mx-2 w-full px-2 text-xs">
              <SelectValue placeholder="Select new type..." />
            </SelectTrigger>
            <SelectContent>
              {USED_CONTRACT_TYPES.map((type) => (
                <SelectItem key={type} value={type} className="text-xs">
                  {CONTRACT_TYPE_NAME_MAP[type]}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="ghost" className="ml-4 text-gray-600" size="icon" onClick={onSave} loading={isPending} disabled={!newType}>
            <SaveIcon />
          </Button>
          <Button variant="ghost" className="text-red-600" size="icon" onClick={onCancel}>
            <SquareXIcon />
          </Button>
        </>
      ) : (
        <>
          {CONTRACT_TYPE_NAME_MAP[type]}
          <Button variant="ghost" className="invisible ml-2 text-gray-600 group-hover:visible" size="icon" onClick={() => setIsEditing(true)}>
            <PencilIcon />
          </Button>
        </>
      )}
    </span>
  );
};

export default EditContractType;
