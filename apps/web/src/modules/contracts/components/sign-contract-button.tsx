import { LoaderCircle } from 'lucide-react';
import { toast } from 'sonner';

import { Button, ButtonProps } from '@shared-components';

import { useGetContract } from '../rq/queries';

type Props = ButtonProps & { contractId: string };

const SignContractButton = ({ contractId, children, ...buttonProps }: Props) => {
  const { refetch, isRefetching } = useGetContract(contractId, false);

  const onClick = async () => {
    const contract = await refetch();

    if (!contract.data) {
      toast.error('Failed to load a contract');
      return;
    }

    const signingURL = contract.data?.signingURL;

    if (!signingURL) {
      toast.error('No signing url fetched');
      return;
    }

    window.open(signingURL, '_blank');
  };

  return (
    <Button onClick={() => void onClick()} {...buttonProps}>
      {isRefetching ? <LoaderCircle className="mx-auto animate-spin" /> : children}
    </Button>
  );
};

export default SignContractButton;
