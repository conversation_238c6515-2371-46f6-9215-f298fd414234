import { ContractType } from '@packages/contracts';

export const CONTRACT_TYPE_NAME_MAP: Record<ContractType, string> = {
  MissionAgreement: 'Builder Agreement',
  TermsOfService: 'Terms of Service',
  ClientContract: 'Client contract',
  ServiceOrder: 'Service Order',
  ScopeOfWork: 'Scope of Work',
  MasterServicesAgreement: 'Master Services',
};

export const USED_CONTRACT_TYPES: ContractType[] = ['ServiceOrder', 'ScopeOfWork', 'MasterServicesAgreement'] as const;
