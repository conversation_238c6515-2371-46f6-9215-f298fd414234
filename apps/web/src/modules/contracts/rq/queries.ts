import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useGetContract = (id: string, enabled = true) => {
  return api.contracts.getContract.useQuery({
    enabled,
    queryKey: queryKeys.contracts.getContract(id),
    queryData: {
      params: { id },
    },
    select: (data) => data.body,
  });
};

const DEFAULT_PAGE_SIZE = 10;

export const useGetContracts = (pageSize: number = DEFAULT_PAGE_SIZE) => {
  return api.contracts.getContracts.useInfiniteQuery({
    queryKey: queryKeys.contracts.getContracts,
    queryData: ({ pageParam }) => ({
      query: {
        page: pageParam.page,
        pageSize: pageParam.pageSize,
      },
    }),
    initialPageParam: { page: 0, pageSize },
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.body.data.length >= pageSize ? { pageSize, page: allPages.length } : undefined;
    },
    refetchOnWindowFocus: true,
  });
};
