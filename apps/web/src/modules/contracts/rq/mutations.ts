import { useQueryClient } from '@tanstack/react-query';

import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useCreateContracts = () => {
  const queryClient = useQueryClient();

  return api.contracts.createCustomContracts.useMutation({
    onSuccess: async () => {
      await queryClient.refetchQueries({ queryKey: queryKeys.contracts.getContracts });
    },
  });
};

export const useUpdateContract = () => {
  const queryClient = useQueryClient();

  return api.contracts.updateContract.useMutation({
    onSuccess: async () => {
      await queryClient.refetchQueries({ queryKey: queryKeys.contracts.getContracts });
    },
  });
};

export const useDeleteContract = () => {
  const queryClient = useQueryClient();

  return api.contracts.deleteContract.useMutation({
    onSuccess: async () => {
      await queryClient.refetchQueries({ queryKey: queryKeys.contracts.getContracts });
    },
  });
};

export const useInviteMissionCollaborator = () => {
  return api.adminAccounts.inviteMissionCollaborator.useMutation();
};
