'use client';

import { SignInWithPasswordSchemaDto } from '@packages/contracts';
import { useQueryClient } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';

import { useSignInWithPassword } from '@modules/auth/rq/mutations';

import { setLocalAuthTokens } from '@lib/auth';
import { getToastErrorMsg } from '@lib/helpers/toast-messages';
import queryKeys from '@lib/query-keys';

import { Form } from '@shared-components';

type SigninFormProps = {
  initialValues?: SignInWithPasswordSchemaDto;
};

const SigninForm = ({ initialValues }: SigninFormProps) => {
  const { mutateAsync: signIn, isPending } = useSignInWithPassword();
  const router = useRouter();
  const queryClient = useQueryClient();

  const onSubmit = async (data: SignInWithPasswordSchemaDto) => {
    try {
      const {
        body: { token, refreshToken },
      } = await signIn({ body: data });

      setLocalAuthTokens(token, refreshToken);
      router.push('/');
      void queryClient.invalidateQueries({ queryKey: queryKeys.auth.getMe });
    } catch (error: unknown) {
      toast.error(getToastErrorMsg(error));
    }
  };

  const form = useForm<SignInWithPasswordSchemaDto>({
    defaultValues: {
      ...initialValues,
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    void form.handleSubmit(onSubmit)(e);
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="flex w-full flex-col">
        <input type="text" {...form.register('email')} placeholder="Email..." className="mb-2.5 border p-2" />
        <input type="password" {...form.register('password')} placeholder="Password..." className="mb-2.5 border p-2" />
        <button type="submit" className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:bg-gray-400" disabled={!!isPending}>
          {isPending ? 'Submitting...' : 'Submit'}
        </button>
      </form>
    </Form>
  );
};

export default SigninForm;
