import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useGetAccountTeamAdvisorByAccountId = (accountId: string) => {
  return api.accounts.getAccountTeamAdvisor.useQuery({
    queryKey: queryKeys.accounts.getAccountAdvisorByAccountId(accountId),
    queryData: { params: { accountId } },
    select: (data) => data.body,
  });
};

export const useAdminGetAccountTeamAdvisorByAccountId = (accountId: string) => {
  return api.adminAccounts.getAccountTeamAdvisorByAccountId.useQuery({
    queryKey: queryKeys.accounts.getAdminAccountAdvisorByAccountId(accountId),
    queryData: { params: { accountId } },
    select: (data) => data.body,
  });
};

export const useAdminGetAccountCollaboratorsByAccountId = (accountId: string) => {
  return api.adminAccounts.getAccountCollaboratorsByAccountId.useQuery({
    queryKey: queryKeys.accounts.getAdminAccountCollaboratorsByAccountId(accountId),
    queryData: { params: { accountId } },
    select: (data) => data.body,
  });
};
