import { useCallback, useRef, useState } from 'react';

export const useRecorder = (stream: MediaStream | null) => {
  const [isRecording, setIsRecording] = useState(false);
  const [duration, setDuration] = useState(0);
  const recorderRef = useRef<MediaRecorder | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const getSupportedMimeType = useCallback(() => {
    const types = ['video/webm', 'video/webm;codecs=vp8,opus', 'video/mp4', 'video/mp4;codecs=h264,aac'];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return undefined;
  }, []);

  const startRecording = useCallback(() => {
    if (!stream) {
      return;
    }

    const mimeType = getSupportedMimeType();

    if (!mimeType) {
      return;
    }

    const mediaRecorder = new MediaRecorder(stream, { mimeType });
    recorderRef.current = mediaRecorder;
    chunksRef.current = [];

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        chunksRef.current.push(event.data);
      }
    };

    mediaRecorder.start(1000);
    setIsRecording(true);
    setDuration(0);

    timerRef.current = setInterval(() => {
      setDuration((prev) => prev + 1);
    }, 1000);
  }, [stream, getSupportedMimeType]);

  const stopRecording = useCallback(() => {
    if (recorderRef.current && recorderRef.current.state !== 'inactive') {
      recorderRef.current.stop();
      setIsRecording(false);
    }
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  }, []);

  const getRecordedBlob = useCallback(() => {
    const mimeType = getSupportedMimeType();

    return new Blob(chunksRef.current, { type: mimeType });
  }, [getSupportedMimeType]);

  return { isRecording, duration, startRecording, stopRecording, getRecordedBlob };
};
