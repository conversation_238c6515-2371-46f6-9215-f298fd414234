import { MISSION_EDITABLE_STATUSES, MissionStatus } from '@packages/contracts';
import { useParams } from 'next/navigation';

import { useGetMission } from '@modules/missions/rq/queries';

export const useMissionStatus = (): MissionStatus => {
  const { id } = useParams<{ id?: string }>();
  if (!id) {
    return 'Spec';
  }

  const { data: mission } = useGetMission(id);
  return mission?.status ?? 'Spec';
};

export const useIsMissionReadOnly = (): boolean => {
  const missionStatus = useMissionStatus();

  return !MISSION_EDITABLE_STATUSES.includes(missionStatus);
};
