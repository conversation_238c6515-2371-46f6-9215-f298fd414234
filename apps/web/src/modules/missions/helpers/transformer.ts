import { DraftMissionDto, GetMissionPrefillResponse, GetMissionResponse, MissionPlannedStart } from '@packages/contracts';

import { getTimeCommitment, getTimeOverlap } from '@modules/missions/helpers/time';

import { generateObjectId } from './ids';

export const transformMissionToFormValues = (response: GetMissionResponse): DraftMissionDto => {
  return {
    title: response.title,
    logoURL: response.logoURL ?? null,
    videoURL: response.videoURL ?? null,
    description: response.description ?? '',
    companyStory: response.companyStory ?? '',
    plannedStart: (response.plannedStart as MissionPlannedStart) ?? undefined,
    timezone: response.timezone ?? undefined,
    timeOverlap: getTimeOverlap(response.overlapMinutes),
    roles: response.roles.map((role) => ({
      id: role.id,
      status: role.status,
      categoryId: role.categoryId,
      headline: role.headline ?? undefined,
      timeCommitment: getTimeCommitment(role.availability?.weeklyHoursAvailable),
      budgetType: role.isFullTimeRetainer ? 'monthly' : 'hourly',
      budget: role.budget ?? undefined,
      markupPercentage: role.markup ? role.markup * 100 : undefined,
      screeningQuestion: role.customQuestions?.[0]?.text,
      requiredSkills: role.requiredSkills.map((skill) => skill.talentSkillId),
      preferredSkills: role.preferredSkills.map((skill) => skill.talentSkillId),
      locations: role.locations ?? [],
      assignedUser: role.assignedUser,
    })),
  };
};

export const transformMissionPrefillToFormValues = (response: GetMissionPrefillResponse | undefined): DraftMissionDto | undefined => {
  if (!response) {
    return undefined;
  }

  return {
    title: response.missionName ?? '',
    description: response.missionDescription ?? '',
    companyStory: response.companyDescription ?? '',
    plannedStart: (response.plannedStart as MissionPlannedStart) ?? undefined,
    timezone: response.timezone ?? undefined,
    logoURL: null,
    videoURL: null,
    roles: (response.openRoles ?? []).map((role) => ({
      id: generateObjectId(),
      status: 'Open',
      categoryId: role.categoryId,
      headline: role.headline ?? undefined,
      requiredSkills: role.requiredSkills ?? [],
      preferredSkills: role.preferredSkills ?? [],
      screeningQuestion: role.screeningQuestion ?? undefined,
    })),
  };
};
