import { MISSION_ROLE_TIME_COMMITMENT_VALUES, MISSION_TIME_OVERLAP_VALUES } from '@packages/contracts';

type TimeOverlapValue = (typeof MISSION_TIME_OVERLAP_VALUES)[number];
type TimeCommitmentValue = (typeof MISSION_ROLE_TIME_COMMITMENT_VALUES)[number];

export const getTimeOverlap = (overlapMinutes: number | null): TimeOverlapValue | undefined => {
  if (!overlapMinutes) {
    return undefined;
  }

  if (overlapMinutes < 120) {
    return 'no_overlap';
  } else if (overlapMinutes < 240) {
    return '2h';
  } else if (overlapMinutes < 360) {
    return '4h';
  } else if (overlapMinutes < 480) {
    return '6h';
  } else {
    return '8h';
  }
};

export const getTimeCommitment = (hours: number | undefined): TimeCommitmentValue | undefined => {
  if (!hours || hours <= 0 || hours > 40) {
    return undefined;
  }

  if (hours <= 5) {
    return '5h';
  } else if (hours <= 10) {
    return '10h';
  } else if (hours <= 20) {
    return '20h';
  } else if (hours <= 30) {
    return '30h';
  } else if (hours <= 40) {
    return '40h';
  }
};

export const MISSION_TIME_COMMITMENT_MESSAGE: Record<TimeCommitmentValue, string> = {
  '5h': 'Flexible hours, 5h - 10h',
  '10h': 'Flexible hours, 10h - 20h',
  '20h': 'Flexible hours, 20h - 30h',
  '30h': 'Flexible hours, 30h - 40h',
  '40h': 'Full time, 40h',
};

export const MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE = 'Time commitment';

export const formatDuration = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};
