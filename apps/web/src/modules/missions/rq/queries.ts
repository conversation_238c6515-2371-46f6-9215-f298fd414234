import { QueryRateGuidaceSchemaDto } from '@packages/contracts';

import api from '@config/api';

import queryKeys from '@lib/query-keys';

export const useGetMission = (id: string) => {
  return api.missions.getMission.useQuery({
    queryKey: queryKeys.mission.getMission(id),
    queryData: { params: { id } },
    select: (data) => data.body,
    retry: false,
  });
};

export const useGetMissionPrefill = () => {
  return api.missionsPrefill.getMissionPrefill.useQuery({
    queryKey: queryKeys.mission.getMissionPrefill(),
    select: (data) => data.body,
    retry: false,
  });
};

export const useGetRoleCategories = () => {
  return api.missions.getRoleCategories.useQuery({
    queryKey: queryKeys.mission.getRoleCategories(),
    select: (data) => data.body,
  });
};

export const useGetTalentSkills = () => {
  return api.missions.getTalentSkills.useQuery({
    queryKey: queryKeys.mission.getTalentSkills(),
    select: (data) => data.body,
  });
};

export const useGetRoleGuidance = (query: QueryRateGuidaceSchemaDto) => {
  return api.missions.getRoleRateGuidance.useQuery({
    queryKey: queryKeys.mission.getRoleGuidance(query),
    queryData: { query },
    select: (data) => data.body,
    retry: false,
  });
};

export const useGetHubspotDeal = (dealId: string) => {
  return api.missions.getHubspotDeal.useQuery({
    queryKey: queryKeys.mission.getHubspotDeal(dealId),
    queryData: { params: { dealId } },
    enabled: Boolean(dealId.trim()),
    select: (data) => data.body,
    retry: false,
  });
};
