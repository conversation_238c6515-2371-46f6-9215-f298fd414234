import api from '@config/api';
import { onMissionCreated } from '@modules/missions/helpers/iframe-actions';

export const useCreateMission = () => {
  return api.missions.createMission.useMutation({
    onSuccess: (data) => {
      onMissionCreated(data.body.mid);
    },
  });
};

export const useUpdateMission = () => {
  return api.missions.updateMission.useMutation();
};

export const useConfirmMission = () => {
  return api.missions.confirmMission.useMutation();
};

export const usePublishMission = () => {
  return api.missions.publishMission.useMutation();
};

export const useRequestNewRole = () => {
  return api.missions.requestNewRole.useMutation();
};

export const useApproveNewRole = () => {
  return api.missions.approveRoleRequest.useMutation();
};

export const useRejectNewRole = () => {
  return api.missions.rejectRoleRequest.useMutation();
};

export const useRequestRoleRemoval = () => {
  return api.missions.requestRoleRemoval.useMutation();
};
