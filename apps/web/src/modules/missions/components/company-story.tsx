import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Tiptap } from '@shared-components';

const CompanyStory = () => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsMissionReadOnly();

  return (
    <FormField
      control={control}
      name="companyStory"
      render={({ field }) => (
        <FormItem className="focus-within:border-primary mb-4 min-h-36 rounded-xl border p-6 hover:cursor-text">
          <FormLabel className="text-sm font-medium">Company description</FormLabel>
          <FormControl>
            <Tiptap content={field.value} onChange={field.onChange} placeholder="Briefly introduce your company..." readOnly={readOnly} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default CompanyStory;
