import { useFormContext } from 'react-hook-form';

import MissionLogo from '@modules/missions/components/logo';
import { onAddCollaborators } from '@modules/missions/helpers/iframe-actions';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import { Button, FormControl, FormField, FormItem, FormLabel, FormMessage, Input } from '@shared-components';

const MissionHeader = () => {
  const { control } = useFormContext();
  const readOnly = useIsMissionReadOnly();

  return (
    <div className="from-gray-0 flex flex-col items-start justify-between gap-4 bg-gradient-to-b to-gray-100 px-4 py-4 sm:flex-row sm:items-end sm:bg-none sm:px-0 sm:py-0">
      <div className="flex w-full flex-1 flex-col gap-4 sm:flex-row sm:items-center">
        <MissionLogo />
        <FormField
          control={control}
          name="title"
          render={({ field }) => (
            <FormItem className="flex-1 space-y-1">
              <FormLabel className="font-jakarta text-xs font-medium sm:text-sm">Project name</FormLabel>
              <FormControl>
                <Input
                  {...field}
                  className="font-jakarta h-auto border-none bg-inherit p-0 text-2xl font-extrabold placeholder:text-gray-400 focus-visible:ring-0 focus-visible:ring-offset-0"
                  placeholder="Name your project"
                  aria-label="Project name"
                  readOnly={readOnly}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <Button variant="secondary" className="h-8 rounded-lg px-3" type="button" onClick={onAddCollaborators}>
        Add collaborators
      </Button>
    </div>
  );
};

export default MissionHeader;
