import { DraftMissionDto, MISSION_TIME_OVERLAP_OPTIONS } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import { FormControl, FormField, FormItem, FormMessage, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared-components';

const SelectOverlap = () => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsMissionReadOnly();

  return (
    <FormField
      control={control}
      name="timeOverlap"
      render={({ field }) => (
        <FormItem className="flex flex-1 flex-col sm:flex-initial sm:items-start">
          <Select value={field.value} onValueChange={field.onChange} disabled={readOnly}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Time overlap" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {MISSION_TIME_OVERLAP_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SelectOverlap;
