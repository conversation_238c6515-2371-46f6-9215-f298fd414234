import { useParams } from 'next/navigation';
import { useFormContext } from 'react-hook-form';

import { useMissionContext } from '@modules/missions/components/context';
import MissionPublish from '@modules/missions/components/publish';
import { useMissionStatus } from '@modules/missions/hooks/use-mission-status';

import { Badge, Button } from '@shared-components';

import MissionActionsMenu from './actions-menu';

const MissionActions = () => {
  const { formState } = useFormContext();
  const { id } = useParams<{ id?: string }>();
  const { isConfirming, setIsConfirming } = useMissionContext();
  const missionStatus = useMissionStatus();

  return (
    <div className="mb-4 flex items-center justify-between gap-4 px-4 sm:mb-10 sm:px-0">
      <Badge className="bg-gray-500 font-normal text-white">Not published</Badge>
      <div className="flex gap-2">
        {missionStatus === 'Spec' && (
          <Button
            type="submit"
            loading={formState.isSubmitting && isConfirming}
            onClick={() => {
              setIsConfirming(true);
            }}
          >
            <span className="inline sm:hidden">Publish</span>
            <span className="hidden sm:inline">Publish project</span>
          </Button>
        )}
        <Button
          variant="secondary"
          className="hidden sm:block"
          type="submit"
          loading={formState.isSubmitting && !isConfirming}
          onClick={() => {
            setIsConfirming(false);
          }}
        >
          Save
        </Button>
        {missionStatus === 'Formation' && <MissionPublish />}
        {id && <MissionActionsMenu />}
      </div>
    </div>
  );
};

export default MissionActions;
