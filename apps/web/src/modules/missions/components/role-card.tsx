import { BaseRoleDto } from '@packages/contracts';
import { Zap } from 'lucide-react';

import { useGetRoleCategories } from '@modules/missions/rq/queries';

import { Avatar, AvatarFallback, AvatarImage } from '@shared-components';

type RoleCardProps = {
  role: BaseRoleDto;
  description: React.ReactNode;
  actionButton: React.ReactNode;
};

const RoleCard = ({ role, description, actionButton }: RoleCardProps) => {
  const { data: roleCategories = [] } = useGetRoleCategories();
  const category = roleCategories.find((category) => role.categoryId === category.id);

  return (
    <div className="flex items-end justify-between gap-4 rounded-xl border p-4 sm:items-center sm:p-6">
      <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center">
        <Avatar className="size-10 rounded-xl sm:size-16">
          <AvatarImage src={role.assignedUser?.pictureURL ?? undefined} />
          <AvatarFallback className="size-10 rounded-xl bg-gray-200 sm:size-16">
            <Zap className="size-4 text-gray-600" />
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col gap-1">
          {category && <h3 className="text-lg font-semibold">{category.title}</h3>}
          <p className="text-xs font-medium sm:text-sm">{description}</p>
        </div>
      </div>
      {actionButton}
    </div>
  );
};

export default RoleCard;
