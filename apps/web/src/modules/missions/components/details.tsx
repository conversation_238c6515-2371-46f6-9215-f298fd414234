import { Separator } from '@shared-components';

import CompanyStory from './company-story';
import Description from './description';
import SelectOverlap from './select-overlap';
import SelectPlannedStart from './select-planned-start';
import SelectTimezone from './select-timezone';

const MissionDetails = () => {
  return (
    <div className="my-10">
      <h2 className="mb-4 text-2xl font-extrabold">Project details</h2>
      <div className="mb-4 flex flex-wrap items-center gap-2 gap-y-4 sm:gap-4">
        <div className="flex w-full items-center gap-2 sm:w-fit sm:gap-4">
          <span className="text-sm">Planned start</span>
          <SelectPlannedStart />
        </div>
        <Separator className="hidden h-3 bg-gray-400 sm:block" orientation="vertical" />
        <SelectOverlap />
        <span className="text-sm">with</span>
        <SelectTimezone />
      </div>
      <CompanyStory />
      <Description />
    </div>
  );
};

export default MissionDetails;
