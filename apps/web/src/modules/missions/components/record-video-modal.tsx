import { UploadCtxProvider } from '@uploadcare/react-uploader';
import '@uploadcare/react-uploader/core.css';
import { AlertCircle } from 'lucide-react';
import { useCallback, useEffect } from 'react';

import { formatDuration } from '@modules/missions/helpers/time';
import { useCamera } from '@modules/missions/hooks/use-camera';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';
import { useRecorder } from '@modules/missions/hooks/use-recorder';

import { cn } from '@lib/utils';

import {
  Alert,
  AlertDescription,
  AlertTitle,
  Button,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@shared-components';

type RecordVideoProps = {
  uploaderRef: React.MutableRefObject<InstanceType<UploadCtxProvider> | null>;
};

const RecordVideoModal = ({ uploaderRef }: RecordVideoProps) => {
  const { videoRef, isCameraReady, isStarting, startCamera, stopCamera, error } = useCamera();
  const { isRecording, duration, startRecording, stopRecording, getRecordedBlob } = useRecorder(videoRef.current?.srcObject as MediaStream | null);
  const readOnly = useIsMissionReadOnly();

  const onOpenChange = useCallback(
    (isOpen: boolean) => {
      const handleCamera = async () => {
        if (isOpen) {
          await startCamera();
        } else {
          if (isRecording) {
            stopRecording();
          }
          stopCamera();
        }
      };
      void handleCamera();
    },
    [isRecording, startCamera, stopCamera, stopRecording]
  );

  const handleStopRecording = useCallback(() => {
    stopRecording();
    const blob = getRecordedBlob();
    const file = new File([blob], 'recorded-video.webm', { type: 'video/webm' });

    if (uploaderRef.current) {
      const api = uploaderRef.current.getAPI();
      api.addFileFromObject(file);
      api.uploadAll();
      api.setCurrentActivity('upload-list');
      api.setModalState(true);
    }
  }, [getRecordedBlob, stopRecording, uploaderRef]);

  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  return (
    <Dialog onOpenChange={onOpenChange}>
      <DialogTrigger asChild disabled={readOnly}>
        <Button variant="outline" className="h-8 rounded-lg border-gray-900 bg-inherit px-3">
          Record
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Record video</DialogTitle>
          <DialogDescription>Record a short video to describe your project</DialogDescription>
        </DialogHeader>
        {error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : (
          <div className="flex items-center justify-center gap-2 sm:justify-start">
            <span className={`h-3 w-3 rounded-full ${isRecording ? 'bg-red-500' : 'bg-gray-500'}`} />
            <span className="text-sm">{isStarting ? 'Starting camera' : isRecording ? `Recording: ${formatDuration(duration)}` : 'Ready to record'}</span>
          </div>
        )}
        <video ref={videoRef} autoPlay muted className={cn('w-full rounded-lg', { hidden: error })} />
        <DialogFooter className="gap-2 sm:gap-0">
          {isRecording ? (
            <DialogClose asChild>
              <Button onClick={handleStopRecording}>Stop Recording</Button>
            </DialogClose>
          ) : (
            <Button onClick={startRecording} disabled={!isCameraReady}>
              Start Recording
            </Button>
          )}
          <DialogClose asChild>
            <Button variant="secondary">Cancel</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RecordVideoModal;
