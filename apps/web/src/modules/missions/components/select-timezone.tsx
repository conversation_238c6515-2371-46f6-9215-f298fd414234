'use client';

import { DraftMissionDto } from '@packages/contracts';
import { Check, ChevronDown } from 'lucide-react';
import * as React from 'react';
import { useFormContext } from 'react-hook-form';

import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import timezones from '@lib/constants/timezones';
import { cn } from '@lib/utils';

import {
  Button,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

const SelectTimezone = () => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsMissionReadOnly();
  const [open, setOpen] = React.useState(false);

  return (
    <FormField
      control={control}
      name="timezone"
      render={({ field }) => (
        <FormItem className="flex flex-1 flex-col sm:items-start">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild disabled={readOnly}>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    'flex h-8 justify-between rounded-lg px-3 py-1.5 text-sm font-normal',
                    field.value ? 'border-white bg-gray-200 text-gray-900' : 'border-dashed border-gray-400 text-gray-600'
                  )}
                >
                  {field.value ?? 'Timezone'}
                  <ChevronDown className={cn('p-0.5', field.value ? 'text-gray-900' : 'text-gray-500')} />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent className="p-0" align="start">
              <Command>
                <CommandInput placeholder="Search timezone..." />
                <CommandList>
                  <CommandEmpty>No timezone found.</CommandEmpty>
                  <CommandGroup>
                    {timezones.map((timezone) => (
                      <CommandItem
                        key={timezone.tzCode}
                        value={timezone.label}
                        onSelect={() => {
                          field.onChange(timezone.tzCode);
                          setOpen(false);
                        }}
                      >
                        <Check className={field.value === timezone.tzCode ? 'opacity-100' : 'opacity-0'} />
                        {timezone.label}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SelectTimezone;
