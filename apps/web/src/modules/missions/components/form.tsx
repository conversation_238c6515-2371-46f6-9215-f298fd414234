'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { ConfirmMissionDto, confirmMissionSchema, DraftMissionDto, draftMissionSchema } from '@packages/contracts';
import { useParams, useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';

import MissionActions from '@modules/missions/components/actions';
import MissionContent from '@modules/missions/components/content';
import { useMissionContext } from '@modules/missions/components/context';
import MissionHeader from '@modules/missions/components/header';
import RoleDrawer from '@modules/missions/components/role/drawer';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';
import { useConfirmMission, useCreateMission, useUpdateMission } from '@modules/missions/rq/mutations';

import { Form, Separator } from '@shared-components';

type MissionFormProps = {
  initialValues?: DraftMissionDto;
};

const MissionForm = ({ initialValues }: MissionFormProps) => {
  const router = useRouter();
  const { id } = useParams<{ id?: string }>();
  const { isConfirming } = useMissionContext();
  const readOnly = useIsMissionReadOnly();

  const { mutateAsync: createMission } = useCreateMission();
  const { mutateAsync: updateMission } = useUpdateMission();
  const { mutateAsync: confirmMission } = useConfirmMission();

  const form = useForm<DraftMissionDto>({
    resolver: zodResolver(isConfirming ? confirmMissionSchema : draftMissionSchema),
    defaultValues: {
      title: '',
      description: '',
      companyStory: '',
      logoURL: null,
      videoURL: null,
      roles: [],
      ...initialValues,
    },
  });

  const onSubmit = async (values: DraftMissionDto | ConfirmMissionDto) => {
    if (id) {
      if (isConfirming) {
        await confirmMission({ params: { id }, body: values as ConfirmMissionDto });
      } else {
        await updateMission({ params: { id }, body: values as DraftMissionDto });
      }

      return;
    }

    const mission = await createMission({ body: values });
    router.push(`/mission/${mission.body.mid}`);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    void form.handleSubmit(onSubmit)(e);
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit} className="container mx-auto max-w-4xl px-0 pb-10 pt-4 sm:px-10">
        {!readOnly && <MissionActions />}
        <Separator className="bg-gray-400 sm:hidden" />
        <MissionHeader />
        <Separator className="mt-6 hidden bg-gray-400 sm:block" />
        <MissionContent />
        <RoleDrawer />
      </form>
    </Form>
  );
};

export default MissionForm;
