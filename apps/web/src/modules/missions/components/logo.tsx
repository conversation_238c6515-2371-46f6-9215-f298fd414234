import { DraftMissionDto } from '@packages/contracts';
import { UploadCtxProvider } from '@uploadcare/react-uploader';
import '@uploadcare/react-uploader/core.css';
import { FileUploaderRegular } from '@uploadcare/react-uploader/next';
import { Pencil, Zap } from 'lucide-react';
import { useRef } from 'react';
import { useFormContext } from 'react-hook-form';

import { config } from '@config/index';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import { cn } from '@lib/utils';

import { Avatar, AvatarFallback, AvatarImage, FormControl, FormField, FormItem } from '@shared-components';

const MissionLogo = () => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsMissionReadOnly();
  const uploaderRef = useRef<InstanceType<UploadCtxProvider> | null>(null);

  const handleClick = () => {
    if (!readOnly && uploaderRef.current) {
      const api = uploaderRef.current.getAPI();
      api.setCurrentActivity('start-from');
      api.setModalState(true);
    }
  };

  return (
    <FormField
      control={control}
      name="logoURL"
      render={({ field }) => (
        <FormItem className="space-y-0">
          <div className={cn('relative w-fit cursor-pointer', readOnly && 'cursor-default')} onClick={handleClick}>
            <Avatar className="h-16 w-16 rounded-xl">
              <AvatarImage src={field.value!} />
              <AvatarFallback className="rounded-xl bg-indigo-600">
                <Zap className="h-6 w-6 text-white" />
              </AvatarFallback>
            </Avatar>
            {!readOnly && (
              <div className="absolute bottom-0 right-0 rounded-md border-2 border-white bg-gray-100 p-1">
                <Pencil className="h-3 w-3 text-gray-600" />
              </div>
            )}
          </div>
          <FormControl>
            <FileUploaderRegular
              imgOnly
              headless
              multiple={false}
              useCloudImageEditor
              sourceList="local"
              pubkey={config.NEXT_PUBLIC_UPLOADCARE_API_KEY}
              apiRef={uploaderRef}
              onDoneClick={(event) => {
                if (event.successEntries.length > 0) {
                  field.onChange(event.successEntries[0].cdnUrl);
                }
              }}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default MissionLogo;
