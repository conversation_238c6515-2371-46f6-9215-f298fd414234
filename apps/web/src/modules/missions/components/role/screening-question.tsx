import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { useIsRoleReadOnly } from '@modules/missions/hooks/use-role-status';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Tiptap } from '@shared-components';

type ScreeningQuestionProps = {
  roleIndex: number;
};

const ScreeningQuestion = ({ roleIndex }: ScreeningQuestionProps) => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsRoleReadOnly();

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.screeningQuestion`}
      render={({ field }) => (
        <FormItem className="focus-within:border-primary min-h-32 rounded-xl border p-6 hover:cursor-text">
          <FormLabel className="text-sm font-medium text-gray-900">Screening question (Optional)</FormLabel>
          <FormControl>
            <Tiptap content={field.value} onChange={field.onChange} readOnly={readOnly} placeholder="What is your experience with startup companies?" />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default ScreeningQuestion;
