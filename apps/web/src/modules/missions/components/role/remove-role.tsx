import {
  MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS,
  MISSION_ROLE_REMOVAL_COMMENT_MIN_CHARS,
  MISSION_ROLE_REMOVAL_REASONS,
  MissionRoleRemovalReason,
  RequestRoleRemovalSchemaDto,
} from '@packages/contracts';
import { useParams } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useMissionContext } from '@modules/missions/components/context';
import { useRequestRoleRemoval } from '@modules/missions/rq/mutations';

import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@shared-components';

const RemoveRole = () => {
  const { id } = useParams<{ id: string }>();
  const [open, setOpen] = useState<boolean>(false);
  const [reason, setReason] = useState<MissionRoleRemovalReason | undefined>();
  const [comment, setComment] = useState<string>('');
  const { selectedRoleId } = useMissionContext();

  const { mutate: requestRoleRemoval } = useRequestRoleRemoval();

  const onSubmit = () => {
    if (!id || !selectedRoleId || !reason) {
      return;
    }

    const body: RequestRoleRemovalSchemaDto = { reason };
    if (comment.trim()) {
      body.comment = comment;
    }

    requestRoleRemoval(
      { params: { id, roleId: selectedRoleId }, body },
      {
        onSuccess: () => {
          setOpen(false);
          toast.success('Role removal requested successfully');
        },
      }
    );
    setReason(undefined);
    setComment('');
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button
        variant="secondary"
        onClick={() => {
          setOpen(true);
        }}
      >
        Remove role
      </Button>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Please provide a reason for removing.</DialogTitle>
          <DialogDescription>
            If you remove this role, we'll remove it from the platform immediately and our members won't be able to see it and apply to it anymore.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-2">
          <Label>
            REASON FOR REMOVING<span className="text-red-500">*</span>
          </Label>
          <Select value={reason} onValueChange={(value) => setReason(value as MissionRoleRemovalReason)}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a reason..." />
            </SelectTrigger>
            <SelectContent>
              {MISSION_ROLE_REMOVAL_REASONS.map((item) => (
                <SelectItem key={item} value={item}>
                  {item}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>COMMENT</Label>
          <Textarea value={comment} onChange={(e) => setComment(e.target.value)} placeholder="Enter your comment here" />
          <div className="text-muted-foreground flex justify-between text-xs">
            <span>Enter at least 20 characters.</span>
            <span>
              {comment.length} / {MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS} max
            </span>
          </div>
        </div>
        <DialogFooter className="mt-4">
          <Button
            type="button"
            onClick={onSubmit}
            disabled={
              !reason ||
              (comment.length > 0 && comment.length < MISSION_ROLE_REMOVAL_COMMENT_MIN_CHARS) ||
              comment.length > MISSION_ROLE_REMOVAL_COMMENT_MAX_CHARS
            }
          >
            Request to remove
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RemoveRole;
