import { DraftMissionDto } from '@packages/contracts';
import { Zap } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { useGetMe } from '@modules/auth/rq/queries';
import { useMissionContext } from '@modules/missions/components/context';
import RoleDrawerTitle from '@modules/missions/components/role/drawer-title';
import RoleGuidance from '@modules/missions/components/role/guidance';
import InputBudget from '@modules/missions/components/role/input-budget';
import InputMarkup from '@modules/missions/components/role/input-markup';
import RoleDescription from '@modules/missions/components/role/role-description';
import ScreeningQuestion from '@modules/missions/components/role/screening-question';
import SelectBudgetType from '@modules/missions/components/role/select-budget-type';
import SelectCommitment from '@modules/missions/components/role/select-commitment';
import SelectLocation from '@modules/missions/components/role/select-location';
import SelectSkills from '@modules/missions/components/role/select-skills';
import { MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE, MISSION_TIME_COMMITMENT_MESSAGE } from '@modules/missions/helpers/time';
import { Role } from '@modules/missions/helpers/types';
import { useIsRoleReadOnly } from '@modules/missions/hooks/use-role-status';
import { useGetRoleCategories } from '@modules/missions/rq/queries';

import { Avatar, AvatarFallback, AvatarImage, Separator } from '@shared-components';

const Tag = ({ label }: { label: string }) => <span className="rounded-lg bg-gray-300 px-3 py-1.5 text-sm">{label}</span>;

const renderTimeCommitment = (readOnly: boolean, role: Role, roleIndex: number) => {
  if (readOnly) {
    return <Tag label={role.timeCommitment ? MISSION_TIME_COMMITMENT_MESSAGE[role.timeCommitment] : MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE} />;
  }
  return <SelectCommitment roleIndex={roleIndex} />;
};

const renderEngagement = (readOnly: boolean, role: Role, roleIndex: number) => {
  if (readOnly) {
    return (
      <div className="flex items-center gap-2">
        <span className="text-sm">{`${role.budgetType === 'monthly' ? 'Monthly engagement' : 'Hourly engagement'}`}</span>
        <Tag label={`$${role.budget} per ${role.budgetType === 'monthly' ? 'month' : 'hour'}`} />
      </div>
    );
  }
  return (
    <div className="flex items-center gap-2">
      <span className={`text-sm ${role.timeCommitment ? 'opacity-100' : 'opacity-50'}`}>Role budget</span>
      <SelectBudgetType roleIndex={roleIndex} />
      <InputBudget roleIndex={roleIndex} />
    </div>
  );
};

const renderRequiredSkills = (readOnly: boolean, role: Role, roleIndex: number) => {
  if (readOnly) {
    return <Tag label={`${role.requiredSkills?.length} required skills`} />;
  }
  return <SelectSkills roleIndex={roleIndex} type="required" />;
};

const renderPreferredSkills = (readOnly: boolean, role: Role, roleIndex: number) => {
  if (readOnly) {
    return <Tag label={`${role.preferredSkills?.length} preferred skills`} />;
  }
  return <SelectSkills roleIndex={roleIndex} type="preferred" />;
};

const renderLocation = (readOnly: boolean, role: Role, roleIndex: number) => {
  if (readOnly) {
    return <Tag label={`${role.locations?.length} locations`} />;
  }
  return <SelectLocation roleIndex={roleIndex} />;
};

const RoleDetails = () => {
  const { data: me } = useGetMe();
  const { selectedRoleId } = useMissionContext();
  const { data: roleCategories = [] } = useGetRoleCategories();
  const { watch } = useFormContext<DraftMissionDto>();
  const readOnly = useIsRoleReadOnly();

  const roles = watch('roles');
  const roleIndex = roles.findIndex((field) => field.id === selectedRoleId);
  const role = roles[roleIndex];

  return (
    <>
      <RoleDrawerTitle title={roleCategories.find((category) => role.categoryId === category.id)?.title ?? ''} />
      {role.assignedUser && (
        <div className="to-gray-0 mt-4 flex gap-4 rounded-xl bg-gradient-to-r from-gray-900 p-6">
          <Avatar className="size-12 rounded-md">
            <AvatarImage src={role.assignedUser.pictureURL ?? undefined} />
            <AvatarFallback className="rounded-md bg-indigo-600">
              <Zap className="text-white" />
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="text-lg font-medium">{`${role.assignedUser.firstName} ${role.assignedUser.lastName}`}</span>
            <span className="text-sm text-gray-500">Active in this role</span>
          </div>
        </div>
      )}
      <div className="mt-4 flex flex-col gap-4">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
          {renderTimeCommitment(readOnly, role, roleIndex)}
          <Separator className="hidden h-2 bg-gray-400 sm:block" orientation="vertical" />
          {renderEngagement(readOnly, role, roleIndex)}
        </div>
        <RoleGuidance role={role} />
        {!readOnly && me?.isAdmin && <InputMarkup roleIndex={roleIndex} />}
        <div className="relative">
          <RoleDescription roleIndex={roleIndex} />
          <div className="absolute bottom-6 left-6 flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-2">
            {renderRequiredSkills(readOnly, role, roleIndex)}
            {renderPreferredSkills(readOnly, role, roleIndex)}
            <Separator className="mx-2 hidden h-2 bg-gray-400 sm:block" orientation="vertical" />
            {renderLocation(readOnly, role, roleIndex)}
          </div>
        </div>
        <ScreeningQuestion roleIndex={roleIndex} />
      </div>
    </>
  );
};

export default RoleDetails;
