import { QueryRateGuidaceSchemaDto } from '@packages/contracts';

import { Role } from '@modules/missions/helpers/types';
import { useGetRoleGuidance } from '@modules/missions/rq/queries';

import { RangeSlider } from '@shared-components';

type RoleGuidanceProps = {
  role: Role;
};

const RoleGuidance = ({ role }: RoleGuidanceProps) => {
  const query: QueryRateGuidaceSchemaDto = {
    categoryId: role.categoryId,
  };

  if (role.markupPercentage) {
    query.markupPercentage = String(role.markupPercentage);
  }

  if (role.locations?.length) {
    query.countries = role.locations.join(',');
  }

  const { data: guidance } = useGetRoleGuidance(query);

  if (!guidance) {
    return null;
  }

  return (
    <div className="rounded-xl border border-purple-100 p-4">
      <p className="text-primary text-sm font-medium">
        Average budget for this type of role — ${guidance.lowerBound} to ${guidance.upperBound}
      </p>
      <p className="mt-0.5 text-xs">Setting a competitive rate can help attract a larger pool of highly skilled talent to your project.</p>
      <div className="mt-6 h-10">
        <RangeSlider disabled value={[guidance.lowerBound, guidance.upperBound]} min={guidance.lowerBound - 10} max={guidance.upperBound + 10} />
      </div>
    </div>
  );
};

export default RoleGuidance;
