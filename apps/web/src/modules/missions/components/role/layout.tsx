import { ConfirmRoleDto, DraftMissionDto } from '@packages/contracts';
import { X } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'sonner';

import { useGetMe } from '@modules/auth/rq/queries';
import { useMissionContext } from '@modules/missions/components/context';
import MissionRoleActionsMenu from '@modules/missions/components/role/actions-menu';
import RemoveRole from '@modules/missions/components/role/remove-role';
import { useMissionStatus } from '@modules/missions/hooks/use-mission-status';
import { useApproveNewRole, useCreateMission, useRejectNewRole, useRequestNewRole, useUpdateMission } from '@modules/missions/rq/mutations';

import { getToastErrorMsg } from '@lib/helpers/toast-messages';

import { But<PERSON>, Sheet, SheetClose, Sheet<PERSON>ontent, SheetFooter } from '@shared-components';

type RoleLayoutProps = {
  header: string;
  children: React.ReactNode;
};

const RoleLayout = ({ header, children }: RoleLayoutProps) => {
  const router = useRouter();
  const { id } = useParams<{ id?: string }>();
  const missionStatus = useMissionStatus();
  const { roleDrawerOpen, selectedRoleId, setRoleDrawerOpen, setSelectedRoleId, isAddingRole } = useMissionContext();
  const { formState, handleSubmit, setValue, watch } = useFormContext<DraftMissionDto>();
  const roles = watch('roles');
  const selectedRoleIndex = useMemo(() => roles.findIndex((role) => role.id === selectedRoleId), [roles, selectedRoleId]);
  const selectedRole = useMemo(() => roles[selectedRoleIndex], [roles, selectedRoleIndex]);

  const { data: me } = useGetMe();
  const { mutateAsync: createMission } = useCreateMission();
  const { mutateAsync: updateMission } = useUpdateMission();
  const { mutateAsync: requestNewRole, isPending: isRequestingNewRole } = useRequestNewRole();
  const { mutateAsync: approveNewRole, isPending: isApprovingNewRole } = useApproveNewRole();
  const { mutateAsync: rejectNewRole, isPending: isRejectingNewRole } = useRejectNewRole();

  const onDrawerClose = () => {
    if (isAddingRole) {
      setValue(
        'roles',
        roles.filter((role) => role.id !== selectedRoleId)
      );
    }
    setRoleDrawerOpen(false);
    setSelectedRoleId(null);
  };

  const onSaveRole = async (values: DraftMissionDto) => {
    if (id) {
      await updateMission({ params: { id }, body: values });
      setRoleDrawerOpen(false);
    } else {
      const mission = await createMission({ body: values });
      router.replace(`/mission/${mission.body.mid}`);
    }
  };

  const onRequestNewRole = async () => {
    const newRole = roles.find((role) => role.id === selectedRoleId);
    if (id && newRole) {
      await requestNewRole({ params: { id }, body: newRole as ConfirmRoleDto });
      setRoleDrawerOpen(false);
    }
  };

  const onApproveNewRole = async () => {
    if (id && selectedRoleId) {
      try {
        await approveNewRole({ params: { id, roleId: selectedRoleId } });
        setValue(`roles.${selectedRoleIndex}.status`, 'Open');
        setRoleDrawerOpen(false);
        toast.success('Role approved');
      } catch (error) {
        toast.error(getToastErrorMsg(error));
      }
    }
  };

  const onRejectNewRole = async () => {
    if (id && selectedRoleId) {
      try {
        await rejectNewRole({ params: { id, roleId: selectedRoleId } });
        setValue(`roles.${selectedRoleIndex}.status`, 'Canceled');
        setRoleDrawerOpen(false);
        toast.success('Role rejected');
      } catch (error) {
        toast.error(getToastErrorMsg(error));
      }
    }
  };

  const handleSaveRole = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    void handleSubmit(onSaveRole)(e);
  };

  const handleRequestNewRole = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    void onRequestNewRole();
  };

  const handleApproveNewRole = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    void onApproveNewRole();
  };

  const handleRejectNewRole = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    void onRejectNewRole();
  };

  return (
    <Sheet open={roleDrawerOpen} onOpenChange={onDrawerClose}>
      <SheetContent className="w-[95%] p-0 sm:max-w-[648px] [&>button:has(.lucide-x)]:hidden">
        <div className="flex items-center justify-between border-b border-b-gray-400 px-6 py-5">
          <span className="text-xs font-medium">{header}</span>
          <SheetClose>
            <X />
            <span className="sr-only">Close</span>
          </SheetClose>
        </div>
        <div className="p-6">
          {children}
          {selectedRoleId && missionStatus === 'Spec' && (
            <SheetFooter className="mt-6 flex-row">
              <div className="mr-auto">
                <MissionRoleActionsMenu />
              </div>
              <Button onClick={handleSaveRole} loading={formState.isSubmitting}>
                Save role
              </Button>
            </SheetFooter>
          )}
          {selectedRoleId && missionStatus === 'Formation' && (
            <SheetFooter className="mt-6 sm:justify-start">
              <RemoveRole />
            </SheetFooter>
          )}
          {selectedRoleId && missionStatus === 'Created' && isAddingRole && (
            <SheetFooter className="mt-6 sm:justify-end">
              <Button onClick={handleRequestNewRole} loading={isRequestingNewRole}>
                Request new role
              </Button>
            </SheetFooter>
          )}
          {selectedRole && selectedRole.status === 'Pending' && me?.isAdmin && (
            <SheetFooter className="mt-6 sm:justify-end">
              <Button onClick={handleRejectNewRole} loading={isRejectingNewRole} variant="destructive">
                Reject
              </Button>
              <Button onClick={handleApproveNewRole} loading={isApprovingNewRole}>
                Approve
              </Button>
            </SheetFooter>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default RoleLayout;
