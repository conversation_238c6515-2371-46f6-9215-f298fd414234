import { DraftMissionDto } from '@packages/contracts';
import { ChevronDown, Sparkles } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { useMissionContext } from '@modules/missions/components/context';
import RoleDrawerTitle from '@modules/missions/components/role/drawer-title';
import { generateObjectId } from '@modules/missions/helpers/ids';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';
import { useGetRoleCategories } from '@modules/missions/rq/queries';

import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList, Popover, PopoverContent, PopoverTrigger } from '@shared-components';

const SelectCategory = () => {
  const { setSelectedRoleId, setIsAddingRole } = useMissionContext();
  const isMissionReadOnly = useIsMissionReadOnly();
  const { data: roleCategories = [] } = useGetRoleCategories();
  const { control } = useFormContext<DraftMissionDto>();
  const { append } = useFieldArray({
    control,
    name: 'roles',
  });

  const handleAddRole = (categoryId: string) => {
    const roleId = generateObjectId();
    const status = isMissionReadOnly ? 'Pending' : 'Open';
    append({ id: roleId, status, categoryId, budgetType: 'monthly', locations: [] });
    setSelectedRoleId(roleId);
    setIsAddingRole(true);
  };

  return (
    <>
      <RoleDrawerTitle title="What role do you want to add?" />
      <div className="mt-4 flex flex-col gap-2">
        {roleCategories.slice(0, 3).map((category) => (
          <div
            key={category.id}
            className="hover:border-primary group flex cursor-pointer items-center gap-2 rounded-lg border p-4"
            onClick={() => handleAddRole(category.id)}
          >
            <Sparkles className="group-hover:text-primary size-3 fill-current text-gray-500" />
            <span className="group-hover:text-primary text-sm font-medium">{category.title}</span>
          </div>
        ))}
        <Popover modal>
          <PopoverTrigger className="flex cursor-pointer items-center justify-between rounded-lg border p-4">
            <span className="text-sm font-medium">Other roles</span>
            <ChevronDown className="size-3" />
          </PopoverTrigger>
          <PopoverContent className="p-0 sm:w-[600px]" align="start">
            <Command>
              <CommandInput placeholder="Search..." />
              <CommandList>
                <CommandEmpty>No role found.</CommandEmpty>
                <CommandGroup>
                  {roleCategories.map((category) => (
                    <CommandItem
                      key={category.id}
                      value={category.title}
                      className="data-[selected=true]:text-primary cursor-pointer py-2 data-[selected=true]:bg-purple-100"
                      onSelect={() => handleAddRole(category.id)}
                    >
                      {category.title}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
    </>
  );
};

export default SelectCategory;
