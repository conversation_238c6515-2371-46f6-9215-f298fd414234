'use client';

import { DraftMissionDto } from '@packages/contracts';
import { ChevronDown } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { useGetTalentSkills } from '@modules/missions/rq/queries';

import { cn } from '@lib/utils';

import {
  Button,
  Checkbox,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

type SelectSkillsProps = {
  roleIndex: number;
  type: 'required' | 'preferred';
  className?: string;
};

const SelectSkills = ({ roleIndex, type, className }: SelectSkillsProps) => {
  const { control } = useFormContext<DraftMissionDto>();
  const { data: talentSkills = [] } = useGetTalentSkills();
  const displayText = type === 'required' ? 'Required skills' : 'Nice to have';

  return (
    <FormField
      control={control}
      name={type === 'required' ? `roles.${roleIndex}.requiredSkills` : `roles.${roleIndex}.preferredSkills`}
      render={({ field }) => {
        const hasValue = Boolean(field.value && field.value.length > 0);
        return (
          <FormItem className={cn('flex flex-1 flex-col sm:items-start', className)}>
            <Popover modal>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    className={cn(
                      'flex h-8 justify-between rounded-lg px-3 py-1.5 text-sm font-normal',
                      hasValue ? 'border-white bg-gray-200 text-gray-900' : 'border-dashed border-gray-400 text-gray-500'
                    )}
                  >
                    {hasValue ? `${field.value?.length} ${displayText.toLowerCase()}` : `${displayText}`}
                    <ChevronDown className={cn('p-0.5', hasValue ? 'text-gray-900' : 'text-gray-500')} />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="p-0" align="start">
                <Command>
                  <CommandInput placeholder={`Search ${type} skills...`} />
                  <CommandList className="max-h-40">
                    <CommandEmpty>No skills found.</CommandEmpty>
                    <CommandGroup>
                      {talentSkills.map((skill) => (
                        <CommandItem
                          key={skill.id}
                          value={skill.name}
                          onSelect={() => {
                            field.onChange(field.value?.includes(skill.id) ? field.value.filter((id) => id !== skill.id) : [...(field.value ?? []), skill.id]);
                          }}
                        >
                          <Checkbox checked={field.value?.includes(skill.id)} />
                          {skill.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default SelectSkills;
