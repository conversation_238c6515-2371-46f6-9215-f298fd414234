import { DraftMissionDto } from '@packages/contracts';
import { MoreVertical } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { toast } from 'sonner';

import { useMissionContext } from '@modules/missions/components/context';
import { generateObjectId } from '@modules/missions/helpers/ids';

import { Button, DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@shared-components';

const MissionRoleActionsMenu = () => {
  const { selectedRoleId, setSelectedRoleId, setRoleDrawerOpen } = useMissionContext();
  const { control, watch, setValue } = useFormContext<DraftMissionDto>();
  const roles = watch('roles');
  const { append } = useFieldArray({
    control,
    name: 'roles',
  });

  const duplicateRole = () => {
    const selectedRole = roles.find((role) => role.id === selectedRoleId);
    if (selectedRole) {
      const roleId = generateObjectId();
      append({ ...selectedRole, id: roleId });
      setRoleDrawerOpen(false);
      toast.success('Role duplicated');
    }
  };

  const removeRole = () => {
    if (selectedRoleId) {
      setValue(
        'roles',
        roles.filter((role) => role.id !== selectedRoleId)
      );
      setSelectedRoleId(null);
      setRoleDrawerOpen(false);
      toast.success('Role removed');
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="secondary" size="icon" type="button">
          <MoreVertical className="h-4 w-4" />
          <span className="sr-only">More options</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuItem onClick={duplicateRole}>Duplicate role</DropdownMenuItem>
        <DropdownMenuItem onClick={removeRole}>Remove role</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default MissionRoleActionsMenu;
