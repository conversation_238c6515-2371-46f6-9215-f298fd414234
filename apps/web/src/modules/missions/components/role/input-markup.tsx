import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { handleFormNumberChange } from '@modules/missions/helpers/forms';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Input } from '@shared-components';

type InputMarkupProps = {
  roleIndex: number;
};

const InputMarkup = ({ roleIndex }: InputMarkupProps) => {
  const { control } = useFormContext<DraftMissionDto>();

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.markupPercentage`}
      render={({ field }) => (
        <FormItem className="to-gray-0 flex items-center gap-2 space-y-0 rounded-xl bg-gray-300 bg-gradient-to-r from-gray-100 px-6 py-4">
          <FormLabel className="text-sm font-normal">Role markup</FormLabel>
          <FormControl>
            <div className="relative">
              <Input
                {...field}
                value={field.value ?? ''}
                type="number"
                className="h-8 w-20 rounded-lg border-gray-400 pr-6 placeholder:text-gray-500"
                placeholder="15"
                onChange={(e) => handleFormNumberChange(e, field.onChange)}
              />
              <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm">%</span>
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default InputMarkup;
