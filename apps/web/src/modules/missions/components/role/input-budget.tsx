import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { handleFormNumberChange } from '@modules/missions/helpers/forms';

import { FormControl, FormField, FormItem, FormMessage, Input } from '@shared-components';

type InputBudgetProps = {
  roleIndex: number;
};

const InputBudget = ({ roleIndex }: InputBudgetProps) => {
  const { control, watch } = useFormContext<DraftMissionDto>();
  const roles = watch('roles');
  const role = roles[roleIndex];

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.budget`}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-sm">$</span>
              <Input
                {...field}
                value={field.value ?? ''}
                onChange={(e) => handleFormNumberChange(e, field.onChange)}
                type="number"
                className="h-8 w-28 rounded-lg border-gray-400 pl-8 placeholder:text-gray-500"
                placeholder="12000"
                disabled={!role.timeCommitment}
              />
            </div>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default InputBudget;
