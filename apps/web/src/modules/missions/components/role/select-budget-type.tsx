import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { FormControl, FormField, FormItem, FormMessage, Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@shared-components';

type SelectBudgetTypeProps = {
  roleIndex: number;
};

const SelectBudgetType = ({ roleIndex }: SelectBudgetTypeProps) => {
  const { control, watch } = useFormContext<DraftMissionDto>();
  const roles = watch('roles');
  const role = roles[roleIndex];

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.budgetType`}
      render={({ field }) => (
        <FormItem>
          <Select value={field.value} onValueChange={field.onChange} defaultValue="monthly" disabled={!role.timeCommitment}>
            <FormControl>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="hourly">Hourly</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SelectBudgetType;
