import { useMissionContext } from '@modules/missions/components/context';
import RoleDetails from '@modules/missions/components/role/details';
import RoleLayout from '@modules/missions/components/role/layout';
import SelectCategory from '@modules/missions/components/role/select-category';

const RoleDrawer = () => {
  const { selectedRoleId, isAddingRole } = useMissionContext();
  const header = !selectedRoleId || isAddingRole ? 'NEW ROLE' : 'ROLE DETAILS';

  return <RoleLayout header={header}>{selectedRoleId ? <RoleDetails /> : <SelectCategory />}</RoleLayout>;
};

export default RoleDrawer;
