import { SheetDescription, SheetHeader, SheetTitle } from '@shared-components';

type RoleDrawerTitleProps = {
  title: string;
};

const RoleDrawerTitle = ({ title }: RoleDrawerTitleProps) => {
  return (
    <SheetHeader className="text-left">
      <SheetTitle className="text-[22px] font-extrabold tracking-tighter">{title}</SheetTitle>
      <SheetDescription className="hidden">role panel</SheetDescription>
    </SheetHeader>
  );
};

export default RoleDrawerTitle;
