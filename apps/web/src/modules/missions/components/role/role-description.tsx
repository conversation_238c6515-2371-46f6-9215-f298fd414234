import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { useIsRoleReadOnly } from '@modules/missions/hooks/use-role-status';

import { FormControl, FormField, FormItem, FormLabel, FormMessage, Tiptap } from '@shared-components';

type RoleDescriptionProps = {
  roleIndex: number;
};

const RoleDescription = ({ roleIndex }: RoleDescriptionProps) => {
  const { control } = useFormContext<DraftMissionDto>();
  const readOnly = useIsRoleReadOnly();

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.headline`}
      render={({ field }) => (
        <FormItem className="focus-within:border-primary min-h-60 rounded-xl border p-6 hover:cursor-text">
          <FormLabel className="text-sm font-medium text-gray-900">Role description</FormLabel>
          <FormControl>
            <Tiptap content={field.value} onChange={field.onChange} readOnly={readOnly} placeholder="Experience working on Fintech products" />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default RoleDescription;
