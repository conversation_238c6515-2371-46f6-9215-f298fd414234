import { DraftMissionDto } from '@packages/contracts';
import { useFormContext } from 'react-hook-form';

import { MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE, MISSION_TIME_COMMITMENT_MESSAGE } from '@modules/missions/helpers/time';

import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '@shared-components';

type SelectCommitmentProps = {
  roleIndex: number;
};

const SelectCommitment = ({ roleIndex }: SelectCommitmentProps) => {
  const { control } = useFormContext<DraftMissionDto>();

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.timeCommitment`}
      render={({ field }) => (
        <FormItem>
          <Select value={field.value} onValueChange={field.onChange}>
            <FormControl>
              <SelectTrigger className="w-full">
                <SelectValue placeholder={MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE}>
                  {field.value ? MISSION_TIME_COMMITMENT_MESSAGE[field.value] : MISSION_TIME_COMMITMENT_DEFAULT_MESSAGE}
                </SelectValue>
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Full time</SelectLabel>
                <SelectItem value="40h">40h+ per week</SelectItem>
              </SelectGroup>
              <SelectGroup>
                <SelectLabel>Flexible hours</SelectLabel>
                <SelectItem value="5h">Minimum 5h per week</SelectItem>
                <SelectItem value="10h">Minimum 10h per week</SelectItem>
                <SelectItem value="20h">Minimum 20h per week</SelectItem>
                <SelectItem value="30h">Minimum 30h per week</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default SelectCommitment;
