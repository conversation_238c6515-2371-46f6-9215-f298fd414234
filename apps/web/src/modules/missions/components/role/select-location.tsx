'use client';

import { DraftMissionDto } from '@packages/contracts';
import { Check, ChevronDown } from 'lucide-react';
import { useState } from 'react';
import { useFormContext } from 'react-hook-form';

import { CONTINENTS, CONTINENT_CODES, Continent, CountryData, GROUPED_COUNTRIES, TOTAL_COUNTRIES } from '@lib/constants/countries';
import { cn } from '@lib/utils';

import {
  Button,
  Checkbox,
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@shared-components';

type SelectLocationProps = {
  roleIndex: number;
  className?: string;
};

const SelectLocation = ({ roleIndex, className }: SelectLocationProps) => {
  const [open, setOpen] = useState<boolean>(false);
  const { control } = useFormContext<DraftMissionDto>();
  const [expandedContinent, setExpandedContinent] = useState<string>();

  const handleContinentSelect = (continentCode: string) => {
    setExpandedContinent(expandedContinent === continentCode ? undefined : continentCode);
  };

  const handleCountrySelect = (country: CountryData, field: { value?: string[]; onChange: (value: string[]) => void }) => {
    field.onChange(field.value?.includes(country.code) ? field.value.filter((code) => code !== country.code) : [...(field.value ?? []), country.code]);
  };

  const handleContinentCheckboxClick = (
    e: React.MouseEvent<HTMLButtonElement>,
    continentCountries: CountryData[],
    field: { value?: string[]; onChange: (value: string[]) => void }
  ) => {
    e.stopPropagation();
    const countryCodes = continentCountries.map((country) => country.code);
    const isAllChecked = continentCountries.every((country) => field.value?.includes(country.code));

    if (isAllChecked) {
      field.onChange((field.value ?? []).filter((code) => !countryCodes.includes(code)));
    } else {
      field.onChange(Array.from(new Set([...(field.value ?? []), ...countryCodes])));
    }
  };

  return (
    <FormField
      control={control}
      name={`roles.${roleIndex}.locations`}
      render={({ field }) => {
        const isWorldwide = field.value?.length === 0 || field.value?.length === TOTAL_COUNTRIES;
        const displayText = open ? 'Location' : isWorldwide ? 'Worldwide' : `${field.value?.length} locations`;

        return (
          <FormItem className={className}>
            <Popover modal open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(
                      'flex h-8 justify-between rounded-lg px-3 py-1.5 text-sm font-normal text-gray-900',
                      open ? 'border-purple-600 bg-white' : 'border-white bg-gray-200'
                    )}
                  >
                    {displayText}
                    <ChevronDown className="p-0.5 text-gray-900" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="p-0" align="end">
                <Command>
                  <CommandList className="max-h-56">
                    <CommandEmpty>No locations found.</CommandEmpty>
                    {CONTINENT_CODES.map((continentCode: Continent) => {
                      const continentCountries = GROUPED_COUNTRIES[continentCode];

                      const isAllChecked = continentCountries.every((country) => field.value?.includes(country.code));
                      const isPartiallyChecked = continentCountries.some((country) => field.value?.includes(country.code));

                      return (
                        <CommandGroup key={continentCode} className="py-0">
                          <CommandItem value={continentCode} onSelect={() => handleContinentSelect(continentCode)}>
                            <Checkbox
                              checked={isAllChecked ? true : isPartiallyChecked ? 'indeterminate' : false}
                              onClick={(e) => handleContinentCheckboxClick(e, continentCountries, field)}
                            />
                            {CONTINENTS[continentCode]}
                            <ChevronDown className="ml-auto p-0.5 text-gray-800" />
                          </CommandItem>
                          {continentCode === expandedContinent &&
                            continentCountries.map((country) => (
                              <CommandItem key={country.code} value={country.name} onSelect={() => handleCountrySelect(country, field)} className="ml-2">
                                <span>{country.flag}</span>
                                <span>{country.name}</span>
                                <Check className={cn('ml-auto', field.value?.includes(country.code) ? 'opacity-100' : 'opacity-0')} />
                              </CommandItem>
                            ))}
                        </CommandGroup>
                      );
                    })}
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        );
      }}
    />
  );
};

export default SelectLocation;
