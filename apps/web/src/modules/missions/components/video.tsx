import { DraftMissionDto } from '@packages/contracts';
import { UploadCtxProvider } from '@uploadcare/react-uploader';
import '@uploadcare/react-uploader/core.css';
import { FileUploaderRegular } from '@uploadcare/react-uploader/next';
import { Video } from 'lucide-react';
import { useCallback, useRef } from 'react';
import { useFormContext } from 'react-hook-form';

import { config } from '@config/index';
import RecordVideo from '@modules/missions/components/record-video-modal';
import { useIsMissionReadOnly } from '@modules/missions/hooks/use-mission-status';

import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  Separator,
} from '@shared-components';

const MissionVideo = () => {
  const { control } = useFormContext<DraftMissionDto>();
  const uploaderRef = useRef<InstanceType<UploadCtxProvider> | null>(null);
  const readOnly = useIsMissionReadOnly();

  const handleUploadClick = useCallback(() => {
    if (uploaderRef.current) {
      const api = uploaderRef.current.getAPI();

      api.setCurrentActivity('start-from');
      api.setModalState(true);
    }
  }, [uploaderRef]);

  return (
    <FormField
      control={control}
      name="videoURL"
      render={({ field }) => (
        <FormItem>
          {field.value ? (
            <div className="mb-10 flex items-center gap-4">
              <Dialog>
                <DialogTrigger>
                  <video
                    muted
                    playsInline
                    className="h-14 w-auto cursor-pointer rounded-md transition-opacity hover:opacity-80"
                    controlsList="nodownload nofullscreen noremoteplayback"
                    disablePictureInPicture
                    disableRemotePlayback
                  >
                    <source src={field.value} type="video/mp4" />
                    <source src={field.value} type="video/webm" />
                  </video>
                </DialogTrigger>
                <DialogContent className="border-none p-0 sm:rounded-none">
                  <DialogHeader className="absolute">
                    <DialogTitle className="hidden">Project video</DialogTitle>
                    <DialogDescription className="hidden">Watch the video to learn more about the project</DialogDescription>
                  </DialogHeader>
                  <video controls autoPlay>
                    <source src={field.value} type="video/mp4" />
                    <source src={field.value} type="video/webm" />
                  </video>
                </DialogContent>
              </Dialog>
              <span className="text-sm">Project video</span>
              <Separator className="h-2 bg-gray-400" orientation="vertical" />
              <Button size="sm" variant="secondary" className="h-8 rounded-lg" type="button" onClick={() => field.onChange(null)} disabled={readOnly}>
                Remove
              </Button>
            </div>
          ) : (
            <div className="mb-10 flex flex-col justify-between gap-4 rounded-lg bg-purple-100 px-4 py-3 sm:flex-row sm:items-center">
              <div className="flex flex-col gap-1 sm:flex-row sm:items-center sm:gap-4">
                <Video className="h-6 w-6 text-purple-600" />
                <div>
                  <h3 className="font-inter text-xs font-medium sm:text-sm">Describe the project in a short video (Optional)</h3>
                  <p className="text-xs sm:text-xs">Projects with founder videos have 78% more engagement</p>
                </div>
              </div>
              <div className="flex gap-2">
                <RecordVideo uploaderRef={uploaderRef} />
                <Button
                  variant="outline"
                  type="button"
                  className="h-8 rounded-lg border-gray-900 bg-inherit px-3"
                  onClick={handleUploadClick}
                  disabled={readOnly}
                >
                  Upload
                </Button>
              </div>
            </div>
          )}
          <FormMessage />
          <FormControl>
            <FileUploaderRegular
              headless
              accept="video/*"
              multiple={false}
              useCloudImageEditor={false}
              sourceList="local"
              pubkey={config.NEXT_PUBLIC_UPLOADCARE_API_KEY}
              apiRef={uploaderRef}
              onDoneClick={(event) => {
                if (event.successEntries.length > 0) {
                  field.onChange(event.successEntries[0].cdnUrl);
                }
              }}
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
};

export default MissionVideo;
