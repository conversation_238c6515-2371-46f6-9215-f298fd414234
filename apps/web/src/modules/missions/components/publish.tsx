import { format } from 'date-fns';
import { LoaderCircle, Search } from 'lucide-react';
import { useParams } from 'next/navigation';
import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { usePublishMission } from '@modules/missions/rq/mutations';
import { useGetHubspotDeal } from '@modules/missions/rq/queries';

import { ApiError } from '@lib/types';

import { Button, Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, Input, Label } from '@shared-components';

const MissionPublish = () => {
  const { id } = useParams<{ id: string }>();
  const [dealId, setDealId] = useState<string>('');
  const [open, setOpen] = useState<boolean>(false);

  const { data: deal, isFetching } = useGetHubspotDeal(dealId);
  const { mutate: publishMission, isPending, error } = usePublishMission();

  const onPublish = useCallback(() => {
    publishMission({ params: { id }, body: { hubspotDealId: dealId } }, { onSuccess: onPublishSuccess });
  }, [dealId]);

  const onPublishSuccess = useCallback(() => {
    setDealId('');
    setOpen(false);
    toast.success(`Published mission with deal ID: ${dealId}`);
  }, [dealId]);

  return (
    <>
      <Button variant="secondary" onClick={() => setOpen(true)}>
        Publish
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign HubSpot Deal ID to this mission</DialogTitle>
            <DialogDescription>
              To find the Deal ID, visit the Hubspot link for this client, go to Deal, then copy the last 11 digits from the URL.
            </DialogDescription>
          </DialogHeader>
          <div className="relative">
            <span className="text-muted-foreground absolute left-3 top-1/2 -translate-y-1/2">
              {isFetching ? <LoaderCircle className="size-4 animate-spin" /> : <Search className="size-4" />}
            </span>
            <Input
              placeholder="12833648824"
              type="number"
              value={dealId}
              onChange={(e) => setDealId(e.target.value)}
              className="pl-10 [&::-webkit-inner-spin-button]:appearance-none"
            />
          </div>
          {deal?.id ? (
            <div className="grid gap-1">
              <div className="grid grid-cols-4 items-center">
                <Label>Deal name:</Label>
                <span className="col-span-3 text-xs">{deal.name}</span>
              </div>
              <div className="grid grid-cols-4 items-center">
                <Label>Create date:</Label>
                <span className="col-span-3 text-xs">{format(deal.createdAt, 'MMM d, yyyy')}</span>
              </div>
            </div>
          ) : (
            dealId && <div className="text-xs">No Hubspot deal found.</div>
          )}
          {error && <div className="text-xs text-red-500">{(error as ApiError).body.message}</div>}
          <DialogFooter>
            <Button type="button" size="sm" disabled={!deal?.id} onClick={onPublish} loading={isPending}>
              Assign and publish
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default MissionPublish;
