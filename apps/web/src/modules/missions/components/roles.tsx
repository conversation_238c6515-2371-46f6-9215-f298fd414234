import { DraftMissionDto } from '@packages/contracts';
import { PlusIcon } from 'lucide-react';
import { useMemo } from 'react';
import { useFormContext } from 'react-hook-form';

import { useMissionContext } from '@modules/missions/components/context';
import RoleCard from '@modules/missions/components/role-card';

import { <PERSON><PERSON>, <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, ScrollArea } from '@shared-components';

const MissionRoles = () => {
  const { setSelectedRoleId, setRoleDrawerOpen, setIsAddingRole } = useMissionContext();
  const { watch } = useFormContext<DraftMissionDto>();
  const roles = watch('roles');

  const onAddRole = () => {
    setRoleDrawerOpen(true);
  };

  const onActionButtonClick = (roleId: string) => {
    setSelectedRoleId(roleId);
    setRoleDrawerOpen(true);
    setIsAddingRole(false);
  };

  const openRoles = useMemo(() => roles.filter((role) => role.status === 'Open' || role.status === 'Pending'), [roles]);
  const activeRoles = useMemo(() => roles.filter((role) => role.status === 'Active' || role.status === 'ScheduledToEnd'), [roles]);
  const endedRoles = useMemo(() => roles.filter((role) => role.status === 'Ended'), [roles]);

  return (
    <div>
      <h2 className="mb-4 text-2xl font-extrabold">Roles to be filled</h2>
      <div
        className="mb-4 flex cursor-pointer items-center justify-center gap-2 rounded-md border border-dashed py-4 text-gray-500 hover:bg-gray-200 sm:rounded-xl"
        onClick={onAddRole}
      >
        <PlusIcon className="size-3" />
        <span className="text-sm">Add a role</span>
      </div>
      <div className="flex flex-col gap-4">
        {openRoles.map((role) => (
          <RoleCard
            key={role.id}
            role={role}
            description={
              role.status === 'Pending' ? <span className="text-orange-500">Pending approval</span> : <span className="text-purple-600">Mission details</span>
            }
            actionButton={
              <Button type="button" onClick={() => onActionButtonClick(role.id)}>
                Finalize role
              </Button>
            }
          />
        ))}
      </div>
      {activeRoles.length > 0 && (
        <>
          <h2 className="mb-4 mt-10 text-2xl font-extrabold">Active roles</h2>
          <div className="flex flex-col gap-4">
            {activeRoles.map((role) => (
              <RoleCard
                key={role.id}
                role={role}
                description={<>{role.assignedUser && `Active role · ${role.assignedUser.firstName} ${role.assignedUser.lastName}`}</>}
                actionButton={
                  <Button type="button" variant="secondary" onClick={() => onActionButtonClick(role.id)}>
                    Manage
                  </Button>
                }
              />
            ))}
          </div>
        </>
      )}
      {endedRoles.length > 0 && (
        <Dialog>
          <DialogTrigger className="hover:text-primary mt-4 text-sm font-medium text-gray-900">{`See past roles ->`}</DialogTrigger>
          <DialogContent className="gap-6">
            <DialogHeader>
              <DialogTitle>Past Roles</DialogTitle>
              <DialogDescription className="hidden">Here are the roles that have been filled in the past.</DialogDescription>
            </DialogHeader>
            <ScrollArea className="max-h-96">
              <div className="flex flex-col gap-4">
                {endedRoles.map((role) => (
                  <RoleCard
                    key={role.id}
                    role={role}
                    description={<>{role.assignedUser && `Ended role · ${role.assignedUser.firstName} ${role.assignedUser.lastName}`}</>}
                    actionButton={
                      <Button type="button" variant="secondary" onClick={() => onActionButtonClick(role.id)}>
                        View
                      </Button>
                    }
                  />
                ))}
              </div>
            </ScrollArea>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default MissionRoles;
