import React, { createContext, ReactNode, useContext, useState } from 'react';

type MissionContextType = {
  roleDrawerOpen: boolean;
  setRoleDrawerOpen: (open: boolean) => void;
  selectedRoleId: string | null;
  setSelectedRoleId: (id: string | null) => void;
  isAddingRole: boolean;
  setIsAddingRole: (adding: boolean) => void;
  isConfirming: boolean;
  setIsConfirming: (confirming: boolean) => void;
};

const MissionContext = createContext<MissionContextType>({} as MissionContextType);

export const MissionContextProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [roleDrawerOpen, setRoleDrawerOpen] = useState<boolean>(false);
  const [selectedRoleId, setSelectedRoleId] = useState<string | null>(null);
  const [isAddingRole, setIsAddingRole] = useState<boolean>(false);
  const [isConfirming, setIsConfirming] = useState<boolean>(false);

  return (
    <MissionContext.Provider
      value={{ roleDrawerOpen, setRoleDrawerOpen, selectedRoleId, setSelectedRoleId, isAddingRole, setIsAddingRole, isConfirming, setIsConfirming }}
    >
      {children}
    </MissionContext.Provider>
  );
};

export const useMissionContext = (): MissionContextType => {
  const context = useContext(MissionContext);

  if (!context) {
    throw new Error('useMissionContext must be used within a MissionContextProvider');
  }

  return context;
};
