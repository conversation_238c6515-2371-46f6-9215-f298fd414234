'use client';

import { getCookie, setCookie, deleteCookie, OptionsType } from 'cookies-next/client';

import { config } from '@config/index';

type Token = string | undefined;
type AuthTokens = { accessToken: Token; refreshToken: Token };

const { IS_LOCAL, IS_PROD } = config;
const ACCESS_TOKEN_NAME = IS_PROD ? 'loginToken' : 'loginTokenSandbox';
const ACCOUNT_TOKEN_NAME = IS_PROD ? 'account_id' : 'sandbox_account_id';
const REFRESH_TOKEN_NAME = IS_PROD ? 'refresh_token' : 'sandbox_refresh_token';

const getCookieOptions = (): OptionsType => ({
  path: '/',
  domain: window?.location?.hostname,
  sameSite: 'strict' as const,
  maxAge: undefined,
  secure: IS_LOCAL ? undefined : true,
});

export const setLocalAuthTokens = (token: string, refreshToken: string) => {
  const options = getCookieOptions();
  setCookie(ACCESS_TOKEN_NAME, token, { ...options, maxAge: 60 * 60 * 24 * 2 });
  setCookie(REFRESH_TOKEN_NAME, refreshToken, { ...options, maxAge: 60 * 60 * 24 * 30 });
};

export const removeLocalAuthTokens = () => {
  deleteCookie(ACCESS_TOKEN_NAME);
  deleteCookie(REFRESH_TOKEN_NAME);
};

export const getLocalAuthTokens = (): AuthTokens => {
  return {
    accessToken: getCookie(ACCESS_TOKEN_NAME),
    refreshToken: getCookie(REFRESH_TOKEN_NAME),
  };
};

export const setAccountToken = (value: string) => {
  setCookie(ACCOUNT_TOKEN_NAME, value, getCookieOptions());
};

export const getAccountToken = (): Token => {
  return getCookie(ACCOUNT_TOKEN_NAME);
};

export const removeAccountToken = () => {
  deleteCookie(ACCOUNT_TOKEN_NAME);
};
