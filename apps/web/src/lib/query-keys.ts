import { QueryRateGuidaceSchemaDto } from '@packages/contracts';

const queryKeys = {
  accounts: {
    getAccountAdvisorByAccountId: (accountId: string) => ['accounts', 'getAccountAdvisorByAccountId', { accountId }],
    getAdminAccountAdvisorByAccountId: (accountId: string) => ['accounts', 'getAdminAccountAdvisorByAccountId', { accountId }],
    getAdminAccountCollaboratorsByAccountId: (accountId: string) => ['accounts', 'getAdminAccountCollaboratorsByAccountId', { accountId }],
  },

  auth: {
    getMe: ['auth', 'me'],
  },
  contracts: {
    getContracts: ['contracts', 'getContracts'],
    getContract: (id: string) => ['contracts', 'getContract', { id }],
  },
  mission: {
    getMission: (id: string) => ['mission', { id }],
    getMissionPrefill: () => ['mission', 'prefill'],
    getRoleCategories: () => ['mission', 'roleCategories'],
    getRoleGuidance: (query: QueryRateGuidaceSchemaDto) => ['mission', 'roleGuidance', query],
    getTalentSkills: () => ['mission', 'talentSkills'],
    getHubspotDeal: (dealId: string) => ['hubspot', { dealId }],
  },
  proposals: {
    getProposal: (id: string) => ['proposal', { id }],
    getProposalBuilders: (proposalId: string, builderIds?: string) => ['proposal', 'builders', proposalId, builderIds],
    getBuilderRecommendations: (proposalId: string, userId: string) => ['builderRecommendations', { proposalId, userId }],
  },
};

export default queryKeys;
