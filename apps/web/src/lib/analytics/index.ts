import { AnalyticsBrowser, EventProperties, GroupTraits, Options } from '@segment/analytics-next';

import { config } from '@config/index';

import { TrackingEvents } from './events';

const globalProps = { app: 'core-platform-web' };

class Analytics {
  segmentAnalytics: AnalyticsBrowser;

  constructor() {
    this.segmentAnalytics = AnalyticsBrowser.load({
      writeKey: config.NEXT_PUBLIC_SEGMENT_WRITE_KEY,
    });
  }

  page(category?: string, name?: string, properties?: Options | EventProperties) {
    void this.segmentAnalytics.page(category, name, { ...properties, ...globalProps });
  }

  track(event: TrackingEvents, properties: EventProperties) {
    void this.segmentAnalytics.track(event, { ...properties, ...globalProps });
  }

  group(groupId: string, properties: GroupTraits) {
    void this.segmentAnalytics.group(groupId, properties);
  }
}

const analytics = new Analytics();

export default analytics;
