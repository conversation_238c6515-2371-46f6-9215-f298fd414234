/**
 * Helper function to return number of items in correct form singular/plural.
 *
 * @param   {string} num - Number of items
 * @param   {string} singularForm - Word in singular form
 * @param   {string|undefined} pluralForm - Optional plural form if word's plural is not made by adding 's' to the end.
 * @returns {string} - Number of items in correct form
 */
export const pluralize = (num: number, singularForm: string, pluralForm?: string) => {
  pluralForm = pluralForm ?? `${singularForm}s`;

  return `${num === 1 ? singularForm : pluralForm}`;
};
