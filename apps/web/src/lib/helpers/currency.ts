/**
 * Formats a number to a currency string.
 * It rounds up to the nearest penny returns a string with 2 decimal places.
 * If the value is a whole number, it returns a string without decimal places.
 *
 * @param   {number} value - The number to format
 * @returns {string} - The formatted currency string
 */
export const formatCurrency = (value: number): string => {
  const formatter = new Intl.NumberFormat('en-US', {
    currency: 'USD',
    maximumFractionDigits: 2,
    minimumFractionDigits: 2,
    style: 'currency',
  });

  const roundedValue = Math.ceil(value * 100) / 100;

  const formattedValue = formatter.format(roundedValue);

  return formattedValue.replace('.00', '');
};
