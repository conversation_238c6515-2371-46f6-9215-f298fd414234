export type CountryData = {
  code: string;
  name: string;
  flag: string;
};

export type Continent = (typeof CONTINENT_CODES)[number];

export const CONTINENT_CODES = ['AF', 'AN', 'AS', 'EU', 'NA', 'OC', 'SA'] as const;

export const CONTINENTS: Record<Continent, string> = {
  AF: 'Africa',
  AN: 'Antarctica',
  AS: 'Asia',
  EU: 'Europe',
  NA: 'North America',
  OC: 'Oceania',
  SA: 'South America',
} as const;

export const GROUPED_COUNTRIES: Record<Continent, CountryData[]> = {
  EU: [
    {
      code: 'AD',
      name: 'Andorra',
      flag: '🇦🇩',
    },
    {
      code: 'AL',
      name: 'Albania',
      flag: '🇦🇱',
    },
    {
      code: 'AT',
      name: 'Austria',
      flag: '🇦🇹',
    },
    {
      code: 'AX',
      name: 'Aland',
      flag: '🇦🇽',
    },
    {
      code: 'BA',
      name: 'Bosnia and Herzegovina',
      flag: '🇧🇦',
    },
    {
      code: 'BE',
      name: 'Belgium',
      flag: '🇧🇪',
    },
    {
      code: 'BG',
      name: 'Bulgaria',
      flag: '🇧🇬',
    },
    {
      code: 'BY',
      name: 'Belarus',
      flag: '🇧🇾',
    },
    {
      code: 'CH',
      name: 'Switzerland',
      flag: '🇨🇭',
    },
    {
      code: 'CY',
      name: 'Cyprus',
      flag: '🇨🇾',
    },
    {
      code: 'CZ',
      name: 'Czech Republic',
      flag: '🇨🇿',
    },
    {
      code: 'DE',
      name: 'Germany',
      flag: '🇩🇪',
    },
    {
      code: 'DK',
      name: 'Denmark',
      flag: '🇩🇰',
    },
    {
      code: 'EE',
      name: 'Estonia',
      flag: '🇪🇪',
    },
    {
      code: 'ES',
      name: 'Spain',
      flag: '🇪🇸',
    },
    {
      code: 'FI',
      name: 'Finland',
      flag: '🇫🇮',
    },
    {
      code: 'FO',
      name: 'Faroe Islands',
      flag: '🇫🇴',
    },
    {
      code: 'FR',
      name: 'France',
      flag: '🇫🇷',
    },
    {
      code: 'GB',
      name: 'United Kingdom',
      flag: '🇬🇧',
    },
    {
      code: 'GG',
      name: 'Guernsey',
      flag: '🇬🇬',
    },
    {
      code: 'GI',
      name: 'Gibraltar',
      flag: '🇬🇮',
    },
    {
      code: 'GR',
      name: 'Greece',
      flag: '🇬🇷',
    },
    {
      code: 'HR',
      name: 'Croatia',
      flag: '🇭🇷',
    },
    {
      code: 'HU',
      name: 'Hungary',
      flag: '🇭🇺',
    },
    {
      code: 'IE',
      name: 'Ireland',
      flag: '🇮🇪',
    },
    {
      code: 'IM',
      name: 'Isle of Man',
      flag: '🇮🇲',
    },
    {
      code: 'IS',
      name: 'Iceland',
      flag: '🇮🇸',
    },
    {
      code: 'IT',
      name: 'Italy',
      flag: '🇮🇹',
    },
    {
      code: 'JE',
      name: 'Jersey',
      flag: '🇯🇪',
    },
    {
      code: 'LI',
      name: 'Liechtenstein',
      flag: '🇱🇮',
    },
    {
      code: 'LT',
      name: 'Lithuania',
      flag: '🇱🇹',
    },
    {
      code: 'LU',
      name: 'Luxembourg',
      flag: '🇱🇺',
    },
    {
      code: 'LV',
      name: 'Latvia',
      flag: '🇱🇻',
    },
    {
      code: 'MC',
      name: 'Monaco',
      flag: '🇲🇨',
    },
    {
      code: 'MD',
      name: 'Moldova',
      flag: '🇲🇩',
    },
    {
      code: 'ME',
      name: 'Montenegro',
      flag: '🇲🇪',
    },
    {
      code: 'MK',
      name: 'North Macedonia',
      flag: '🇲🇰',
    },
    {
      code: 'MT',
      name: 'Malta',
      flag: '🇲🇹',
    },
    {
      code: 'NL',
      name: 'Netherlands',
      flag: '🇳🇱',
    },
    {
      code: 'NO',
      name: 'Norway',
      flag: '🇳🇴',
    },
    {
      code: 'PL',
      name: 'Poland',
      flag: '🇵🇱',
    },
    {
      code: 'PT',
      name: 'Portugal',
      flag: '🇵🇹',
    },
    {
      code: 'RO',
      name: 'Romania',
      flag: '🇷🇴',
    },
    {
      code: 'RS',
      name: 'Serbia',
      flag: '🇷🇸',
    },
    {
      code: 'SE',
      name: 'Sweden',
      flag: '🇸🇪',
    },
    {
      code: 'SI',
      name: 'Slovenia',
      flag: '🇸🇮',
    },
    {
      code: 'SJ',
      name: 'Svalbard and Jan Mayen',
      flag: '🇸🇯',
    },
    {
      code: 'SK',
      name: 'Slovakia',
      flag: '🇸🇰',
    },
    {
      code: 'SM',
      name: 'San Marino',
      flag: '🇸🇲',
    },
    {
      code: 'UA',
      name: 'Ukraine',
      flag: '🇺🇦',
    },
    {
      code: 'VA',
      name: 'Vatican City',
      flag: '🇻🇦',
    },
    {
      code: 'XK',
      name: 'Kosovo',
      flag: '🇽🇰',
    },
  ],
  AS: [
    {
      code: 'AE',
      name: 'United Arab Emirates',
      flag: '🇦🇪',
    },
    {
      code: 'AF',
      name: 'Afghanistan',
      flag: '🇦🇫',
    },
    {
      code: 'AM',
      name: 'Armenia',
      flag: '🇦🇲',
    },
    {
      code: 'AZ',
      name: 'Azerbaijan',
      flag: '🇦🇿',
    },
    {
      code: 'BD',
      name: 'Bangladesh',
      flag: '🇧🇩',
    },
    {
      code: 'BH',
      name: 'Bahrain',
      flag: '🇧🇭',
    },
    {
      code: 'BN',
      name: 'Brunei',
      flag: '🇧🇳',
    },
    {
      code: 'BT',
      name: 'Bhutan',
      flag: '🇧🇹',
    },
    {
      code: 'CC',
      name: 'Cocos (Keeling) Islands',
      flag: '🇨🇨',
    },
    {
      code: 'CN',
      name: 'China',
      flag: '🇨🇳',
    },
    {
      code: 'CX',
      name: 'Christmas Island',
      flag: '🇨🇽',
    },
    {
      code: 'GE',
      name: 'Georgia',
      flag: '🇬🇪',
    },
    {
      code: 'HK',
      name: 'Hong Kong',
      flag: '🇭🇰',
    },
    {
      code: 'ID',
      name: 'Indonesia',
      flag: '🇮🇩',
    },
    {
      code: 'IL',
      name: 'Israel',
      flag: '🇮🇱',
    },
    {
      code: 'IN',
      name: 'India',
      flag: '🇮🇳',
    },
    {
      code: 'IO',
      name: 'British Indian Ocean Territory',
      flag: '🇮🇴',
    },
    {
      code: 'IQ',
      name: 'Iraq',
      flag: '🇮🇶',
    },
    {
      code: 'IR',
      name: 'Iran',
      flag: '🇮🇷',
    },
    {
      code: 'JO',
      name: 'Jordan',
      flag: '🇯🇴',
    },
    {
      code: 'JP',
      name: 'Japan',
      flag: '🇯🇵',
    },
    {
      code: 'KG',
      name: 'Kyrgyzstan',
      flag: '🇰🇬',
    },
    {
      code: 'KH',
      name: 'Cambodia',
      flag: '🇰🇭',
    },
    {
      code: 'KP',
      name: 'North Korea',
      flag: '🇰🇵',
    },
    {
      code: 'KR',
      name: 'South Korea',
      flag: '🇰🇷',
    },
    {
      code: 'KW',
      name: 'Kuwait',
      flag: '🇰🇼',
    },
    {
      code: 'KZ',
      name: 'Kazakhstan',
      flag: '🇰🇿',
    },
    {
      code: 'LA',
      name: 'Laos',
      flag: '🇱🇦',
    },
    {
      code: 'LB',
      name: 'Lebanon',
      flag: '🇱🇧',
    },
    {
      code: 'LK',
      name: 'Sri Lanka',
      flag: '🇱🇰',
    },
    {
      code: 'MM',
      name: 'Myanmar (Burma)',
      flag: '🇲🇲',
    },
    {
      code: 'MN',
      name: 'Mongolia',
      flag: '🇲🇳',
    },
    {
      code: 'MO',
      name: 'Macao',
      flag: '🇲🇴',
    },
    {
      code: 'MV',
      name: 'Maldives',
      flag: '🇲🇻',
    },
    {
      code: 'MY',
      name: 'Malaysia',
      flag: '🇲🇾',
    },
    {
      code: 'NP',
      name: 'Nepal',
      flag: '🇳🇵',
    },
    {
      code: 'OM',
      name: 'Oman',
      flag: '🇴🇲',
    },
    {
      code: 'PH',
      name: 'Philippines',
      flag: '🇵🇭',
    },
    {
      code: 'PK',
      name: 'Pakistan',
      flag: '🇵🇰',
    },
    {
      code: 'PS',
      name: 'Palestine',
      flag: '🇵🇸',
    },
    {
      code: 'QA',
      name: 'Qatar',
      flag: '🇶🇦',
    },
    {
      code: 'RU',
      name: 'Russia',
      flag: '🇷🇺',
    },
    {
      code: 'SA',
      name: 'Saudi Arabia',
      flag: '🇸🇦',
    },
    {
      code: 'SG',
      name: 'Singapore',
      flag: '🇸🇬',
    },
    {
      code: 'SY',
      name: 'Syria',
      flag: '🇸🇾',
    },
    {
      code: 'TH',
      name: 'Thailand',
      flag: '🇹🇭',
    },
    {
      code: 'TJ',
      name: 'Tajikistan',
      flag: '🇹🇯',
    },
    {
      code: 'TM',
      name: 'Turkmenistan',
      flag: '🇹🇲',
    },
    {
      code: 'TR',
      name: 'Turkey',
      flag: '🇹🇷',
    },
    {
      code: 'TW',
      name: 'Taiwan',
      flag: '🇹🇼',
    },
    {
      code: 'UZ',
      name: 'Uzbekistan',
      flag: '🇺🇿',
    },
    {
      code: 'VN',
      name: 'Vietnam',
      flag: '🇻🇳',
    },
    {
      code: 'YE',
      name: 'Yemen',
      flag: '🇾🇪',
    },
  ],
  NA: [
    {
      code: 'AG',
      name: 'Antigua and Barbuda',
      flag: '🇦🇬',
    },
    {
      code: 'AI',
      name: 'Anguilla',
      flag: '🇦🇮',
    },
    {
      code: 'AW',
      name: 'Aruba',
      flag: '🇦🇼',
    },
    {
      code: 'BB',
      name: 'Barbados',
      flag: '🇧🇧',
    },
    {
      code: 'BL',
      name: 'Saint Barthelemy',
      flag: '🇧🇱',
    },
    {
      code: 'BM',
      name: 'Bermuda',
      flag: '🇧🇲',
    },
    {
      code: 'BQ',
      name: 'Bonaire',
      flag: '🇧🇶',
    },
    {
      code: 'BS',
      name: 'Bahamas',
      flag: '🇧🇸',
    },
    {
      code: 'BZ',
      name: 'Belize',
      flag: '🇧🇿',
    },
    {
      code: 'CA',
      name: 'Canada',
      flag: '🇨🇦',
    },
    {
      code: 'CR',
      name: 'Costa Rica',
      flag: '🇨🇷',
    },
    {
      code: 'CU',
      name: 'Cuba',
      flag: '🇨🇺',
    },
    {
      code: 'CW',
      name: 'Curacao',
      flag: '🇨🇼',
    },
    {
      code: 'DM',
      name: 'Dominica',
      flag: '🇩🇲',
    },
    {
      code: 'DO',
      name: 'Dominican Republic',
      flag: '🇩🇴',
    },
    {
      code: 'GD',
      name: 'Grenada',
      flag: '🇬🇩',
    },
    {
      code: 'GL',
      name: 'Greenland',
      flag: '🇬🇱',
    },
    {
      code: 'GP',
      name: 'Guadeloupe',
      flag: '🇬🇵',
    },
    {
      code: 'GT',
      name: 'Guatemala',
      flag: '🇬🇹',
    },
    {
      code: 'HN',
      name: 'Honduras',
      flag: '🇭🇳',
    },
    {
      code: 'HT',
      name: 'Haiti',
      flag: '🇭🇹',
    },
    {
      code: 'JM',
      name: 'Jamaica',
      flag: '🇯🇲',
    },
    {
      code: 'KN',
      name: 'Saint Kitts and Nevis',
      flag: '🇰🇳',
    },
    {
      code: 'KY',
      name: 'Cayman Islands',
      flag: '🇰🇾',
    },
    {
      code: 'LC',
      name: 'Saint Lucia',
      flag: '🇱🇨',
    },
    {
      code: 'MF',
      name: 'Saint Martin',
      flag: '🇲🇫',
    },
    {
      code: 'MQ',
      name: 'Martinique',
      flag: '🇲🇶',
    },
    {
      code: 'MS',
      name: 'Montserrat',
      flag: '🇲🇸',
    },
    {
      code: 'MX',
      name: 'Mexico',
      flag: '🇲🇽',
    },
    {
      code: 'NI',
      name: 'Nicaragua',
      flag: '🇳🇮',
    },
    {
      code: 'PA',
      name: 'Panama',
      flag: '🇵🇦',
    },
    {
      code: 'PM',
      name: 'Saint Pierre and Miquelon',
      flag: '🇵🇲',
    },
    {
      code: 'PR',
      name: 'Puerto Rico',
      flag: '🇵🇷',
    },
    {
      code: 'SV',
      name: 'El Salvador',
      flag: '🇸🇻',
    },
    {
      code: 'SX',
      name: 'Sint Maarten',
      flag: '🇸🇽',
    },
    {
      code: 'TC',
      name: 'Turks and Caicos Islands',
      flag: '🇹🇨',
    },
    {
      code: 'TT',
      name: 'Trinidad and Tobago',
      flag: '🇹🇹',
    },
    {
      code: 'US',
      name: 'United States',
      flag: '🇺🇸',
    },
    {
      code: 'VC',
      name: 'Saint Vincent and the Grenadines',
      flag: '🇻🇨',
    },
    {
      code: 'VG',
      name: 'British Virgin Islands',
      flag: '🇻🇬',
    },
    {
      code: 'VI',
      name: 'U.S. Virgin Islands',
      flag: '🇻🇮',
    },
  ],
  AF: [
    {
      code: 'AO',
      name: 'Angola',
      flag: '🇦🇴',
    },
    {
      code: 'BF',
      name: 'Burkina Faso',
      flag: '🇧🇫',
    },
    {
      code: 'BI',
      name: 'Burundi',
      flag: '🇧🇮',
    },
    {
      code: 'BJ',
      name: 'Benin',
      flag: '🇧🇯',
    },
    {
      code: 'BW',
      name: 'Botswana',
      flag: '🇧🇼',
    },
    {
      code: 'CD',
      name: 'Democratic Republic of the Congo',
      flag: '🇨🇩',
    },
    {
      code: 'CF',
      name: 'Central African Republic',
      flag: '🇨🇫',
    },
    {
      code: 'CG',
      name: 'Republic of the Congo',
      flag: '🇨🇬',
    },
    {
      code: 'CI',
      name: 'Ivory Coast',
      flag: '🇨🇮',
    },
    {
      code: 'CM',
      name: 'Cameroon',
      flag: '🇨🇲',
    },
    {
      code: 'CV',
      name: 'Cape Verde',
      flag: '🇨🇻',
    },
    {
      code: 'DJ',
      name: 'Djibouti',
      flag: '🇩🇯',
    },
    {
      code: 'DZ',
      name: 'Algeria',
      flag: '🇩🇿',
    },
    {
      code: 'EG',
      name: 'Egypt',
      flag: '🇪🇬',
    },
    {
      code: 'EH',
      name: 'Western Sahara',
      flag: '🇪🇭',
    },
    {
      code: 'ER',
      name: 'Eritrea',
      flag: '🇪🇷',
    },
    {
      code: 'ET',
      name: 'Ethiopia',
      flag: '🇪🇹',
    },
    {
      code: 'GA',
      name: 'Gabon',
      flag: '🇬🇦',
    },
    {
      code: 'GH',
      name: 'Ghana',
      flag: '🇬🇭',
    },
    {
      code: 'GM',
      name: 'Gambia',
      flag: '🇬🇲',
    },
    {
      code: 'GN',
      name: 'Guinea',
      flag: '🇬🇳',
    },
    {
      code: 'GQ',
      name: 'Equatorial Guinea',
      flag: '🇬🇶',
    },
    {
      code: 'GW',
      name: 'Guinea-Bissau',
      flag: '🇬🇼',
    },
    {
      code: 'KE',
      name: 'Kenya',
      flag: '🇰🇪',
    },
    {
      code: 'KM',
      name: 'Comoros',
      flag: '🇰🇲',
    },
    {
      code: 'LR',
      name: 'Liberia',
      flag: '🇱🇷',
    },
    {
      code: 'LS',
      name: 'Lesotho',
      flag: '🇱🇸',
    },
    {
      code: 'LY',
      name: 'Libya',
      flag: '🇱🇾',
    },
    {
      code: 'MA',
      name: 'Morocco',
      flag: '🇲🇦',
    },
    {
      code: 'MG',
      name: 'Madagascar',
      flag: '🇲🇬',
    },
    {
      code: 'ML',
      name: 'Mali',
      flag: '🇲🇱',
    },
    {
      code: 'MR',
      name: 'Mauritania',
      flag: '🇲🇷',
    },
    {
      code: 'MU',
      name: 'Mauritius',
      flag: '🇲🇺',
    },
    {
      code: 'MW',
      name: 'Malawi',
      flag: '🇲🇼',
    },
    {
      code: 'MZ',
      name: 'Mozambique',
      flag: '🇲🇿',
    },
    {
      code: 'NA',
      name: 'Namibia',
      flag: '🇳🇦',
    },
    {
      code: 'NE',
      name: 'Niger',
      flag: '🇳🇪',
    },
    {
      code: 'NG',
      name: 'Nigeria',
      flag: '🇳🇬',
    },
    {
      code: 'RE',
      name: 'Reunion',
      flag: '🇷🇪',
    },
    {
      code: 'RW',
      name: 'Rwanda',
      flag: '🇷🇼',
    },
    {
      code: 'SC',
      name: 'Seychelles',
      flag: '🇸🇨',
    },
    {
      code: 'SD',
      name: 'Sudan',
      flag: '🇸🇩',
    },
    {
      code: 'SH',
      name: 'Saint Helena',
      flag: '🇸🇭',
    },
    {
      code: 'SL',
      name: 'Sierra Leone',
      flag: '🇸🇱',
    },
    {
      code: 'SN',
      name: 'Senegal',
      flag: '🇸🇳',
    },
    {
      code: 'SO',
      name: 'Somalia',
      flag: '🇸🇴',
    },
    {
      code: 'SS',
      name: 'South Sudan',
      flag: '🇸🇸',
    },
    {
      code: 'ST',
      name: 'Sao Tome and Principe',
      flag: '🇸🇹',
    },
    {
      code: 'SZ',
      name: 'Eswatini',
      flag: '🇸🇿',
    },
    {
      code: 'TD',
      name: 'Chad',
      flag: '🇹🇩',
    },
    {
      code: 'TG',
      name: 'Togo',
      flag: '🇹🇬',
    },
    {
      code: 'TN',
      name: 'Tunisia',
      flag: '🇹🇳',
    },
    {
      code: 'TZ',
      name: 'Tanzania',
      flag: '🇹🇿',
    },
    {
      code: 'UG',
      name: 'Uganda',
      flag: '🇺🇬',
    },
    {
      code: 'YT',
      name: 'Mayotte',
      flag: '🇾🇹',
    },
    {
      code: 'ZA',
      name: 'South Africa',
      flag: '🇿🇦',
    },
    {
      code: 'ZM',
      name: 'Zambia',
      flag: '🇿🇲',
    },
    {
      code: 'ZW',
      name: 'Zimbabwe',
      flag: '🇿🇼',
    },
  ],
  AN: [
    {
      code: 'AQ',
      name: 'Antarctica',
      flag: '🇦🇶',
    },
    {
      code: 'BV',
      name: 'Bouvet Island',
      flag: '🇧🇻',
    },
    {
      code: 'GS',
      name: 'South Georgia and the South Sandwich Islands',
      flag: '🇬🇸',
    },
    {
      code: 'HM',
      name: 'Heard Island and McDonald Islands',
      flag: '🇭🇲',
    },
    {
      code: 'TF',
      name: 'French Southern Territories',
      flag: '🇹🇫',
    },
  ],
  SA: [
    {
      code: 'AR',
      name: 'Argentina',
      flag: '🇦🇷',
    },
    {
      code: 'BO',
      name: 'Bolivia',
      flag: '🇧🇴',
    },
    {
      code: 'BR',
      name: 'Brazil',
      flag: '🇧🇷',
    },
    {
      code: 'CL',
      name: 'Chile',
      flag: '🇨🇱',
    },
    {
      code: 'CO',
      name: 'Colombia',
      flag: '🇨🇴',
    },
    {
      code: 'EC',
      name: 'Ecuador',
      flag: '🇪🇨',
    },
    {
      code: 'FK',
      name: 'Falkland Islands',
      flag: '🇫🇰',
    },
    {
      code: 'GF',
      name: 'French Guiana',
      flag: '🇬🇫',
    },
    {
      code: 'GY',
      name: 'Guyana',
      flag: '🇬🇾',
    },
    {
      code: 'PE',
      name: 'Peru',
      flag: '🇵🇪',
    },
    {
      code: 'PY',
      name: 'Paraguay',
      flag: '🇵🇾',
    },
    {
      code: 'SR',
      name: 'Suriname',
      flag: '🇸🇷',
    },
    {
      code: 'UY',
      name: 'Uruguay',
      flag: '🇺🇾',
    },
    {
      code: 'VE',
      name: 'Venezuela',
      flag: '🇻🇪',
    },
  ],
  OC: [
    {
      code: 'AS',
      name: 'American Samoa',
      flag: '🇦🇸',
    },
    {
      code: 'AU',
      name: 'Australia',
      flag: '🇦🇺',
    },
    {
      code: 'CK',
      name: 'Cook Islands',
      flag: '🇨🇰',
    },
    {
      code: 'FJ',
      name: 'Fiji',
      flag: '🇫🇯',
    },
    {
      code: 'FM',
      name: 'Micronesia',
      flag: '🇫🇲',
    },
    {
      code: 'GU',
      name: 'Guam',
      flag: '🇬🇺',
    },
    {
      code: 'KI',
      name: 'Kiribati',
      flag: '🇰🇮',
    },
    {
      code: 'MH',
      name: 'Marshall Islands',
      flag: '🇲🇭',
    },
    {
      code: 'MP',
      name: 'Northern Mariana Islands',
      flag: '🇲🇵',
    },
    {
      code: 'NC',
      name: 'New Caledonia',
      flag: '🇳🇨',
    },
    {
      code: 'NF',
      name: 'Norfolk Island',
      flag: '🇳🇫',
    },
    {
      code: 'NR',
      name: 'Nauru',
      flag: '🇳🇷',
    },
    {
      code: 'NU',
      name: 'Niue',
      flag: '🇳🇺',
    },
    {
      code: 'NZ',
      name: 'New Zealand',
      flag: '🇳🇿',
    },
    {
      code: 'PF',
      name: 'French Polynesia',
      flag: '🇵🇫',
    },
    {
      code: 'PG',
      name: 'Papua New Guinea',
      flag: '🇵🇬',
    },
    {
      code: 'PN',
      name: 'Pitcairn Islands',
      flag: '🇵🇳',
    },
    {
      code: 'PW',
      name: 'Palau',
      flag: '🇵🇼',
    },
    {
      code: 'SB',
      name: 'Solomon Islands',
      flag: '🇸🇧',
    },
    {
      code: 'TK',
      name: 'Tokelau',
      flag: '🇹🇰',
    },
    {
      code: 'TL',
      name: 'East Timor',
      flag: '🇹🇱',
    },
    {
      code: 'TO',
      name: 'Tonga',
      flag: '🇹🇴',
    },
    {
      code: 'TV',
      name: 'Tuvalu',
      flag: '🇹🇻',
    },
    {
      code: 'UM',
      name: 'U.S. Minor Outlying Islands',
      flag: '🇺🇲',
    },
    {
      code: 'VU',
      name: 'Vanuatu',
      flag: '🇻🇺',
    },
    {
      code: 'WF',
      name: 'Wallis and Futuna',
      flag: '🇼🇫',
    },
    {
      code: 'WS',
      name: 'Samoa',
      flag: '🇼🇸',
    },
  ],
} as const;

export const COUNTRIES: CountryData[] = Object.values(GROUPED_COUNTRIES).flat();

export const TOTAL_COUNTRIES: number = Object.keys(COUNTRIES).length;

export const COUNTRIES_CODE_NAME_MAP = Object.fromEntries(COUNTRIES.map((country) => [country.code, country.name]));
