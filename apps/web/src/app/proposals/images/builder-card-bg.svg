<svg width="1200" height="184" viewBox="0 0 1200 184" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2311_141)">
<path d="M0 16C0 7.16344 7.16344 0 16 0H1184C1192.84 0 1200 7.16344 1200 16V184H0V16Z" fill="url(#paint0_linear_2311_141)"/>
<g opacity="0.5" filter="url(#filter0_f_2311_141)">
<ellipse cx="897.739" cy="-82.085" rx="37.0144" ry="146.932" transform="rotate(42.8337 897.739 -82.085)" fill="#4A72FF"/>
</g>
<g opacity="0.5" filter="url(#filter1_f_2311_141)">
<ellipse cx="833.725" cy="-18.3774" rx="36.5847" ry="146.932" transform="rotate(42.8337 833.725 -18.3774)" fill="#9024FF"/>
</g>
<g opacity="0.5" filter="url(#filter2_f_2311_141)">
<ellipse cx="914.663" cy="-51.2483" rx="48.3446" ry="156.66" transform="rotate(42.8337 914.663 -51.2483)" fill="url(#paint1_radial_2311_141)"/>
</g>
<g opacity="0.5" filter="url(#filter3_f_2311_141)">
<ellipse cx="782.905" cy="-71.7124" rx="33.1474" ry="146.932" transform="rotate(42.8337 782.905 -71.7124)" fill="#4A72FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_2311_141" x="670.451" y="-316.504" width="454.577" height="468.839" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="61.8726" result="effect1_foregroundBlur_2311_141"/>
</filter>
<filter id="filter1_f_2311_141" x="630.264" y="-228.985" width="406.921" height="421.216" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="50" result="effect1_foregroundBlur_2311_141"/>
</filter>
<filter id="filter2_f_2311_141" x="762.378" y="-210.772" width="304.569" height="319.047" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_2311_141"/>
</filter>
<filter id="filter3_f_2311_141" x="556.326" y="-305.561" width="453.158" height="467.697" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="61.8726" result="effect1_foregroundBlur_2311_141"/>
</filter>
<linearGradient id="paint0_linear_2311_141" x1="600" y1="0" x2="600" y2="184" gradientUnits="userSpaceOnUse">
<stop stop-color="#FBF6FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<radialGradient id="paint1_radial_2311_141" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(962.463 84.0935) rotate(-103.836) scale(242.384 74.799)">
<stop stop-color="#9024FF"/>
<stop offset="1" stop-color="#561699" stop-opacity="0"/>
</radialGradient>
<clipPath id="clip0_2311_141">
<path d="M0 16C0 7.16344 7.16344 0 16 0H1184C1192.84 0 1200 7.16344 1200 16V184H0V16Z" fill="white"/>
</clipPath>
</defs>
</svg>
