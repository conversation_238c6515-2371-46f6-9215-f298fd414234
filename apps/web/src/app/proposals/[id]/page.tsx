'use client';

import { notFound } from 'next/navigation';

import { ProposalsContextProvider } from '@modules/proposals/components/context';
import ProposalLoading from '@modules/proposals/components/loading';
import ProposalView from '@modules/proposals/components/proposal-view';
import { useGetProposal } from '@modules/proposals/rq/queries';

const ProposalPage = ({ params: { id } }: { params: { id: string } }) => {
  const { data, isPending } = useGetProposal(id);

  if (isPending) {
    return <ProposalLoading />;
  }

  if (!data) {
    return notFound();
  }

  return (
    <ProposalsContextProvider proposalId={data.id} missionId={data.missionId} accountId={data.accountId}>
      <ProposalView proposal={data} />
    </ProposalsContextProvider>
  );
};

export default ProposalPage;
