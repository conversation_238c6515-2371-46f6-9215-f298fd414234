import './globals.css';

import { Suspense } from 'react';

import { inter, plusJakartaSans } from '@lib/fonts';

import { Toaster } from '@shared-components';

import Analytics from './analytics';
import ReactQueryProvider from './react-query';

type RootLayoutProps = {
  children: React.ReactNode;
};

const RootLayout: React.FC<RootLayoutProps> = ({ children }): JSX.Element => {
  return (
    <html lang="en" className={`${inter.variable} ${plusJakartaSans.variable}`}>
      <body>
        <ReactQueryProvider>
          {children}
          <Suspense>
            <Analytics />
          </Suspense>
        </ReactQueryProvider>
        <Toaster richColors />
      </body>
    </html>
  );
};

export default RootLayout;
