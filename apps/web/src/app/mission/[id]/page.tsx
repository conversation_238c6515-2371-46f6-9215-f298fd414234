'use client';

import { notFound } from 'next/navigation';

import { MissionContextProvider } from '@modules/missions/components/context';
import MissionForm from '@modules/missions/components/form';
import MissionLoading from '@modules/missions/components/loading';
import { transformMissionToFormValues } from '@modules/missions/helpers/transformer';
import { useGetMission } from '@modules/missions/rq/queries';

const MissionPage = ({ params: { id } }: { params: { id: string } }) => {
  const { data, isPending } = useGetMission(id);

  if (isPending) {
    return <MissionLoading />;
  }

  if (!data) {
    return notFound();
  }

  const initialValues = transformMissionToFormValues(data);

  return (
    <MissionContextProvider>
      <MissionForm initialValues={initialValues} />
    </MissionContextProvider>
  );
};

export default MissionPage;
