'use client';

import { MissionContextProvider } from '@modules/missions/components/context';
import MissionForm from '@modules/missions/components/form';
import MissionLoading from '@modules/missions/components/loading';
import { transformMissionPrefillToFormValues } from '@modules/missions/helpers/transformer';
import { useGetMissionPrefill } from '@modules/missions/rq/queries';

const NewMissionPage = () => {
  const { data, isPending } = useGetMissionPrefill();

  if (isPending) {
    return <MissionLoading />;
  }

  const prefilledValues = transformMissionPrefillToFormValues(data);

  return (
    <MissionContextProvider>
      <MissionForm initialValues={prefilledValues} />
    </MissionContextProvider>
  );
};

export default NewMissionPage;
