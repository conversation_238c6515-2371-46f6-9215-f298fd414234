@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.53%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.53%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.53%;
    --primary: 269.47 100% 44.31%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 97.25%;
    --secondary-foreground: 0 0% 3.53%;
    --muted: 288 100% 99.02%;
    --muted-foreground: 0 0% 3.53%;
    --accent: 272.31 100% 97.45%;
    --accent-foreground: 0 0% 3.53%;
    --destructive: 354.57 90.87% 57.06%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 85.88%;
    --input: 0 0% 41.18%;
    --ring: 255 93.33% 76.47%;
    --chart-1: 270.84 100% 79.02%;
    --chart-2: 270.34 98.89% 64.71%;
    --chart-3: 270.64 100% 53.92%;
    --chart-4: 269.47 100% 44.31%;
    --chart-5: 270 98.92% 36.47%;
    --sidebar: 0 0% 98.5%;
    --sidebar-foreground: 0 0% 14.5%;
    --sidebar-primary: 0 0% 20.5%;
    --sidebar-primary-foreground: 0 0% 98.5%;
    --sidebar-accent: 0 0% 97%;
    --sidebar-accent-foreground: 0 0% 20.5%;
    --sidebar-border: 0 0% 92.2%;
    --sidebar-ring: 0 0% 70.8%;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }

  .dark {
    --background: 22.5 15.38% 10.2%;
    --foreground: 227.14 87.5% 93.73%;
    --card: 270 17.78% 17.65%;
    --card-foreground: 227.14 87.5% 93.73%;
    --popover: 270 17.78% 17.65%;
    --popover-foreground: 227.14 87.5% 93.73%;
    --primary: 256.24 100% 83.33%;
    --primary-foreground: 22.5 15.38% 10.2%;
    --secondary: 272.73 18.03% 23.92%;
    --secondary-foreground: 218.18 13.25% 83.73%;
    --muted: 270 17.78% 17.65%;
    --muted-foreground: 217.89 10.5% 64.51%;
    --accent: 266.9 18.95% 30%;
    --accent-foreground: 218.18 13.25% 83.73%;
    --destructive: 0 91.3% 81.96%;
    --destructive-foreground: 22.5 15.38% 10.2%;
    --border: 272.73 18.03% 23.92%;
    --input: 272.73 18.03% 23.92%;
    --ring: 256.24 100% 83.33%;
    --chart-1: 256.24 100% 83.33%;
    --chart-2: 255 93.33% 76.47%;
    --chart-3: 258.19 91.72% 66.86%;
    --chart-4: 262.42 84.26% 57.65%;
    --chart-5: 263.31 69.17% 50.39%;
    --sidebar: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 240 4.9% 83.9%;
    --radius: 0.5rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  html {
    @apply font-inter;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-jakarta;
  }
}

/* Hide number input spinners for shadcn/ui */
.shadcn-input[type='number']::-webkit-outer-spin-button,
.shadcn-input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
.shadcn-input[type='number'] {
  -moz-appearance: textfield;
}
