'use client';

import { XIcon } from 'lucide-react';

import { useIsMobile } from '@lib/hooks/use-is-mobile';
import { cn } from '@lib/utils';

import { Button, Drawer, DrawerClose, DrawerContent, DrawerTrigger, TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '@shared-components';

type DrawerTooltipProps = {
  trigger: React.ReactNode;
  children: React.ReactNode;
  className?: string;
};

export const DrawerTooltip = ({ trigger, children, className }: DrawerTooltipProps) => {
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <Drawer data-vaul-no-drag="true">
        <DrawerTrigger>{trigger}</DrawerTrigger>
        <DrawerContent className={cn('bg-white p-6 text-sm text-black', className)} hideHandle>
          {children}
          <DrawerClose className="absolute -top-12 right-4">
            <Button variant="secondary" size="icon" className="size-8 rounded-full [&_svg]:size-6">
              <XIcon />
            </Button>
          </DrawerClose>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger>{trigger}</TooltipTrigger>
        <TooltipContent className={cn('max-w-48 bg-gray-700 px-3.5 py-2.5 text-center text-xs text-white', className)}>{children}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
