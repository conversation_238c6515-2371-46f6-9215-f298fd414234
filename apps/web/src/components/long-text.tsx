'use client';

import { ChevronDown, ChevronUp } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

import { cn } from '@lib/utils';

import { Button } from '@shared-components';

type LongTextProps = {
  text: string;
  lines: number;
  className?: string;
};

export const LongText = ({ text, lines, className }: LongTextProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  const [isTextClamped, setIsTextClamped] = useState(false);

  useEffect(() => {
    if (ref.current) {
      new ResizeObserver((e) => {
        const elem = e[0].target as HTMLDivElement;
        const isTextClamped = elem.scrollHeight > elem.clientHeight;
        setIsTextClamped(isTextClamped);
      }).observe(ref.current);
    }
  }, [isExpanded]);

  const onButtonClick = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="flex flex-col items-start gap-1">
      <div ref={ref} className={cn(className, isExpanded ? 'line-clamp-none' : 'line-clamp-1')} style={{ WebkitLineClamp: lines }}>
        {text}
      </div>
      {(isTextClamped || isExpanded) && (
        <Button
          name="Long Text View More Less Button"
          variant="link"
          onClick={onButtonClick}
          className="flex h-5 items-center gap-1 p-0 font-medium hover:no-underline"
        >
          {isExpanded ? 'Close' : 'Read more'}
          {isExpanded ? <ChevronUp className="size-3" /> : <ChevronDown className="size-3" />}
        </Button>
      )}
    </div>
  );
};
