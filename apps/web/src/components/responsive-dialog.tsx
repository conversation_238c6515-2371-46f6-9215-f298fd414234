import * as DialogPrimitive from '@radix-ui/react-dialog';
import { XIcon } from 'lucide-react';
import { Drawer as DrawerPrimitive } from 'vaul';

import { useIsMobile } from '@lib/hooks/use-is-mobile';

import { DialogClose, DialogTitle, DialogDescription, DialogTrigger, DialogContent, DialogFooter } from './dialog';
import { DrawerClose, DrawerTitle, DrawerDescription, DrawerTrigger, DrawerContent, DrawerFooter } from './drawer';

export const ResponsiveDialog = (props: DialogPrimitive.DialogProps | React.ComponentProps<typeof DrawerPrimitive.Root>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerPrimitive.Root {...props} /> : <DialogPrimitive.Root {...props} />;
};

export const ResponsiveDialogTrigger = (props: DialogPrimitive.DialogTriggerProps | React.ComponentProps<typeof DrawerPrimitive.Trigger>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerTrigger {...props} /> : <DialogTrigger {...props} />;
};

export const ResponsiveDialogContent = ({
  className,
  children,
  ...props
}: DialogPrimitive.DialogContentProps | React.ComponentProps<typeof DrawerPrimitive.Content>) => {
  const isMobile = useIsMobile();
  return isMobile ? (
    <DrawerContent {...props} hideHandle>
      <div className={className}>{children}</div>
      <DrawerClose className="absolute -top-12 right-4 flex size-8 items-center justify-center rounded-full bg-white/60 [&_svg]:size-6">
        <XIcon />
      </DrawerClose>
    </DrawerContent>
  ) : (
    <DialogContent {...props} className="p-0">
      <div className={className}>{children}</div>
    </DialogContent>
  );
};

export const ResponsiveDialogTitle = (props: DialogPrimitive.DialogTitleProps | React.ComponentProps<typeof DrawerPrimitive.Title>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerTitle {...props} /> : <DialogTitle {...props} />;
};

export const ResponsiveDialogDescription = (props: DialogPrimitive.DialogDescriptionProps | React.ComponentProps<typeof DrawerPrimitive.Description>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerDescription {...props} /> : <DialogDescription {...props} />;
};

export const ResponsiveDialogClose = (props: DialogPrimitive.DialogCloseProps | React.ComponentProps<typeof DrawerPrimitive.Close>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerClose {...props} /> : <DialogClose {...props} />;
};

export const ResponsiveDialogFooter = (props: React.HTMLAttributes<HTMLDivElement>) => {
  const isMobile = useIsMobile();
  return isMobile ? <DrawerFooter {...props} /> : <DialogFooter {...props} />;
};
