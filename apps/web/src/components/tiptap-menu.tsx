'use client';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>u, Editor } from '@tiptap/react';
import { Bold, Italic, Link2, List, ListOrdered, UnderlineIcon } from 'lucide-react';
import { forwardRef, useCallback, useState } from 'react';

import {
  Button,
  ButtonProps,
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Separator,
} from '@shared-components';

type TiptapMenuProps = {
  editor: Editor;
};

const TiptapMenuItem = forwardRef<HTMLButtonElement, ButtonProps>(({ onClick, children, ...props }, ref) => {
  return (
    <Button variant="ghost" type="button" size="icon" onClick={onClick} className="size-5 text-white hover:bg-gray-700 hover:text-white" ref={ref} {...props}>
      {children}
    </Button>
  );
});

const TiptapMenu = ({ editor }: TiptapMenuProps) => {
  const [url, setUrl] = useState<string>('');
  const [linkDialogOpen, setLinkDialogOpen] = useState<boolean>(false);

  const onLinkSave = useCallback(() => {
    if (!url) {
      editor.chain().focus().unsetLink().run();
      return;
    }

    const href = url.startsWith('http') ? url : `https://${url}`;
    editor.chain().focus().toggleLink({ href }).run();
    setUrl('');
  }, [url, editor]);

  return (
    <BubbleMenu editor={editor} tippyOptions={{ duration: 100 }} className="rounded-xs flex items-center gap-2.5 bg-gray-700 px-3 py-2">
      <TiptapMenuItem onClick={() => editor.chain().focus().toggleBold().run()}>
        <Bold />
      </TiptapMenuItem>
      <TiptapMenuItem onClick={() => editor.chain().focus().toggleItalic().run()}>
        <Italic />
      </TiptapMenuItem>
      <TiptapMenuItem onClick={() => editor.chain().focus().toggleUnderline().run()}>
        <UnderlineIcon />
      </TiptapMenuItem>
      <Separator className="h-6 bg-gray-600" orientation="vertical" />
      <TiptapMenuItem onClick={() => editor.chain().focus().toggleBulletList().run()}>
        <List />
      </TiptapMenuItem>
      <TiptapMenuItem onClick={() => editor.chain().focus().toggleOrderedList().run()}>
        <ListOrdered />
      </TiptapMenuItem>
      <Separator className="h-6 bg-gray-600" orientation="vertical" />
      <Dialog open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
        <DialogTrigger asChild>
          <TiptapMenuItem
            onClick={() => {
              setUrl((editor.getAttributes('link').href as string) ?? '');
              setLinkDialogOpen(true);
            }}
          >
            <Link2 />
          </TiptapMenuItem>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Insert link</DialogTitle>
            <DialogDescription>Enter the URL of the link</DialogDescription>
          </DialogHeader>
          <Input type="url" placeholder="https://example.com" value={url} onChange={(e) => setUrl(e.target.value)} />
          <DialogFooter className="gap-2 sm:gap-0">
            <DialogClose asChild>
              <Button variant="outline" size="sm">
                Cancel
              </Button>
            </DialogClose>
            <DialogClose asChild>
              <Button onClick={onLinkSave} size="sm">
                Save
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </BubbleMenu>
  );
};

export { TiptapMenu, TiptapMenuItem };
