import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';

import { cn } from '@lib/utils';

import { Button } from './button';
import { Calendar, CalendarProps } from './calendar';
import { Popover, PopoverContent, PopoverTrigger } from './popover';

type DatePickerProps = {
  date?: Date;
  onDateChange?: (date?: Date) => void;
  calendarProps?: CalendarProps;
};

export const DatePicker = ({ date, onDateChange, calendarProps }: DatePickerProps) => {
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant={'outline'} className={cn('w-[240px] justify-start text-left font-normal', !date && 'text-muted-foreground')}>
          <CalendarIcon />
          {date ? format(date, 'PPP') : <span>Pick a date</span>}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <Calendar {...calendarProps} mode="single" selected={date} onSelect={(date) => onDateChange && onDateChange(date)} initialFocus />
      </PopoverContent>
    </Popover>
  );
};
