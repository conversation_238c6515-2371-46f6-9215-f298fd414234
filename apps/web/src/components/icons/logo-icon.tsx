export const LogoIcon: React.FC<React.SVGProps<SVGAElement> & { singleColor?: boolean }> = ({ className, singleColor = false }) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" className={className}>
      <path
        d="M9.26666 2.55638C8.93759 1.9375 8.16913 1.70255 7.55025 2.03162C6.93136 2.36069 6.69642 3.12915 7.02549 3.74803L12.2464 13.567C12.5754 14.1859 13.3438 14.4208 13.9627 14.0918C14.5816 13.7627 14.8165 12.9942 14.4875 12.3754L9.26666 2.55638Z"
        fill="currentColor"
      />
      <path
        d="M9.21661 3.74803C9.54572 3.12915 9.31073 2.36069 8.69185 2.03162C8.07297 1.70255 7.3045 1.9375 6.97544 2.55638L4.63191 6.96392C5.15934 7.69746 5.90805 8.18313 6.7304 8.42392L9.21661 3.74803Z"
        fill="currentColor"
      />
      <path
        d="M5.20361 11.2984C4.41175 10.9949 3.67057 10.5581 3.02518 9.98881L1.75623 12.3754C1.42716 12.9943 1.66211 13.7627 2.28099 14.0918C2.89987 14.4209 3.66833 14.1859 3.99741 13.567L5.20361 11.2984Z"
        fill="currentColor"
      />
      <g opacity={singleColor ? 1 : 0.6}>
        <path
          d="M9.30499 8.75972C6.84248 9.45217 3.80386 8.15263 3.53424 4.81165C3.47786 4.113 2.86577 3.59233 2.16712 3.64871C1.46846 3.70509 0.947797 4.31718 1.00418 5.01583C1.43939 10.4088 6.59291 12.442 10.5264 11.0568L9.30499 8.75972Z"
          fill="currentColor"
        />
        <path
          d="M13.2314 9.29478C14.196 8.24629 14.8567 6.8192 14.9962 5.01134C15.0501 4.3125 14.5273 3.70226 13.8284 3.64835C13.1296 3.59444 12.5193 4.11727 12.4654 4.81612C12.407 5.57247 12.2022 6.22551 11.8914 6.77463L13.2314 9.29478Z"
          fill="currentColor"
        />
      </g>
    </svg>
  );
};
