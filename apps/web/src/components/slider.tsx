'use client';

import * as SliderPrimitive from '@radix-ui/react-slider';
import * as React from 'react';

import { cn } from '@lib/utils';

const Slider = React.forwardRef<React.ElementRef<typeof SliderPrimitive.Root>, React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>>(
  ({ className, ...props }, ref) => (
    <SliderPrimitive.Root ref={ref} className={cn('relative flex w-full touch-none select-none items-center', className)} {...props}>
      <SliderPrimitive.Track className="bg-secondary relative h-2 w-full grow overflow-hidden rounded-full">
        <SliderPrimitive.Range className="bg-primary absolute h-full" />
      </SliderPrimitive.Track>
      <SliderPrimitive.Thumb className="border-primary bg-background ring-offset-background focus-visible:ring-ring block h-5 w-5 rounded-full border-2 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50" />
    </SliderPrimitive.Root>
  )
);
Slider.displayName = SliderPrimitive.Root.displayName;

const RangeSlider = React.forwardRef<React.ElementRef<typeof SliderPrimitive.Root>, React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>>(
  ({ className, ...props }, ref) => (
    <SliderPrimitive.Root ref={ref} className={cn('relative flex w-full touch-none select-none items-center', className)} {...props}>
      <SliderPrimitive.Track className="bg-secondary relative h-2 w-full grow overflow-hidden rounded-full">
        <SliderPrimitive.Range className="absolute h-full bg-gradient-to-r from-pink-500 to-purple-500" />
      </SliderPrimitive.Track>
      <SliderPrimitive.Thumb className="block size-4 rounded-full border-2 border-white bg-pink-500 transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50">
        <span className="absolute -left-2 top-6 text-sm text-gray-500">${props.value![0]}</span>
      </SliderPrimitive.Thumb>
      <SliderPrimitive.Thumb className="block size-4 rounded-full border-2 border-white bg-purple-500 transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50">
        <span className="absolute -left-2 top-6 text-sm text-gray-500">${props.value![1]}</span>
      </SliderPrimitive.Thumb>
    </SliderPrimitive.Root>
  )
);
RangeSlider.displayName = SliderPrimitive.Root.displayName;

export { Slider, RangeSlider };
