{"name": "web", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": ""}, "dependencies": {"@calcom/embed-react": "1.5.2", "@hookform/resolvers": "3.9.0", "@packages/contracts": "workspace:*", "@radix-ui/react-avatar": "1.1.1", "@radix-ui/react-checkbox": "1.1.2", "@radix-ui/react-dialog": "1.1.2", "@radix-ui/react-dropdown-menu": "2.1.2", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-popover": "1.1.2", "@radix-ui/react-scroll-area": "1.2.1", "@radix-ui/react-select": "2.1.2", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.3", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.6", "@segment/analytics-next": "1.79.0", "@tanstack/react-query": "5.59.16", "@tiptap/extension-link": "2.10.3", "@tiptap/extension-placeholder": "2.10.3", "@tiptap/extension-underline": "2.10.3", "@tiptap/react": "2.10.3", "@tiptap/starter-kit": "2.10.3", "@ts-rest/core": "3.51.0", "@ts-rest/react-query": "3.51.0", "@uploadcare/react-uploader": "1.2.0", "bson-objectid": "2.0.4", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "cmdk": "1.0.0", "cookies-next": "5.0.2", "date-fns": "4.1.0", "lucide-react": "0.453.0", "next": "14.2.3", "react": "18.2.0", "react-day-picker": "8.10.1", "react-dom": "18.2.0", "react-hook-form": "7.53.1", "sonner": "1.7.0", "tailwind-merge": "2.5.2", "vaul": "1.1.2", "zod": "3.23.8"}, "devDependencies": {"@packages/eslint-config": "workspace:*", "@next/eslint-plugin-next": "^14.2.16", "@tailwindcss/typography": "0.5.15", "@types/node": "^20.17.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss-animate": "1.0.7", "tailwindcss": "^3.4.14", "typescript": "^5.6.3"}}